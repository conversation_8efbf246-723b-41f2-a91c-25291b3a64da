package main

import (
	"bitbucket.org/gatebackend/go-zero/core/conf"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/core/service"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"bitbucket.org/gatebackend/go-zero/rest"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"
	"bitbucket.org/gateio/gateio-lib-base-go/commonconfig"
	"bitbucket.org/gateio/gateio-lib-base-go/health"
	"bitbucket.org/gateio/gateio-lib-base-go/security/inputguard"
	"bitbucket.org/gateio/gateio-lib-common-go/language"
	"bitbucket.org/gateio/gateio-lib-common-go/middleware"
	"bitbucket.org/gateio/gateio-lib-common-go/validate"
	"flag"
	"fmt"
	"gateio_service_welfare_go/internal/config"
	"gateio_service_welfare_go/internal/handler"
	"gateio_service_welfare_go/internal/mqs/kafka"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/task"
	"gateio_service_welfare_go/internal/utils"
	"os"
	"strconv"
)

var configFile = flag.String("f", "etc/gateio_service_welfare_go-api.yaml", "the config file")

func main() {

	// 完全依赖环境变量启动, 本地开发时需要注意, 新建.env文件进行配置
	utils.InitLocalConfig()

	flag.Parse()
	var c config.Config
	conf.MustLoad(*configFile, &c, conf.UseEnv())

	if len(c.Nacos.Ip) != 0 {
		cc, err := config.LoadConfigFromNacos(c.Nacos)
		if err != nil {
			logx.Must(err)
		}
		c = *cc
	}
	// 使用 validator/v10 来验证请求参数
	// see: https://gtglobal.jp.larksuite.com/wiki/M0SIw2WWMis1m8kEtg5jswuopue#XFyMdCAbgoOWOjxtvkxj8OmhpCd
	httpx.SetValidator(validate.NewGoZeroValidator())

	// 用来初始化基础组件需要的配置
	// see: https://gtglobal.jp.larksuite.com/wiki/UOjuw7KZKiCG3tkmQA7jgGLwpXd#Tbn8dRDqkoHJO7xPmIHj0QuRpqg
	logx.Must(commonconfig.Init())

	// 初始化语言包
	language.MustInit()

	// 外部服务, 使用 3460 端口
	outerServer := rest.MustNewServer(c.OuterServiceConf.RestConf)
	outerServer.Use(inputguard.NewInputGuard(inputguard.Replace).GoZeroInterceptor())

	// 内部服务, 使用 9580 端口, 需要自行创建 handlers 并注册
	innerServer := rest.MustNewServer(c.InnerServiceConf.RestConf)
	innerServer.Use(inputguard.NewInputGuard(inputguard.Replace).GoZeroInterceptor())

	svcCtx := svc.NewServiceContext(&c)

	// 注册健康检查接口
	health.RegisterHandlers(outerServer)

	serviceGroup := service.NewServiceGroup()
	serviceGroup.Add(outerServer)
	//serviceGroup.Add(innerServer)

	if isOffLine() {
		srv := xxljob.NewServer(c.XXLJob)
		task.RegisterTasks(srv, svcCtx)
		serviceGroup.Add(srv)
		fmt.Printf("Starting health server at %s:%d\n", c.OuterServiceConf.Host, c.OuterServiceConf.Port)
		outerServer.PrintRoutes()
	} else {
		// 仅在线服务时才会注册路由, 离线任务虽然启动了 HTTP 服务, 但不注册任何接口
		// 注册路由
		// 通过这里注册的接口路由，如果想在inner下注册接口接口，增加一个routes.go文件
		handler.RegisterHandlers(outerServer, svcCtx)
		handler.RegisterInnerHandlers(innerServer, svcCtx)
		//handler.Gateio_service_welfare_goHandler(innerServer, ctx)
		//innerServer.Use(middleware.Authorize()) // 需要全局使用的中间件，可以这样使用
		outerServer.Use(middleware.RequestCarrier())
		innerServer.Use(middleware.RequestCarrier())

		serviceGroup.Add(innerServer)

		fmt.Printf("Starting outer server at %s:%d\n", c.OuterServiceConf.Host, c.OuterServiceConf.Port)
		outerServer.PrintRoutes()
		fmt.Printf("Starting inner server at %s:%d\n", c.InnerServiceConf.Host, c.InnerServiceConf.Port)
		innerServer.PrintRoutes()

		if kafkaEnable() {
			// 启动kafka消费者
			kafka.StartAllConsumer(svcCtx, c.KafkaConf)
			kafka.UserRegisterStartAllConsumer(svcCtx, c.UserCenterKafkaConf)
		}
	}

	//启动服务接口
	serviceGroup.Start()

}

func isOffLine() bool {
	v, ok := os.LookupEnv("IS_OFFLINE")
	if !ok {
		return false
	}

	vv, err := strconv.ParseBool(v)
	if err != nil {
		return false
	}
	return vv
}
func kafkaEnable() bool {
	v, ok := os.LookupEnv("KAFKA_ENABLE")
	if !ok {
		return true
	}

	vv, err := strconv.ParseBool(v)
	if err != nil {
		return true
	}
	return vv
}
