package welfare_task

import (
	"context"
	"fmt"
	"math/rand"
	"net/http"
	"time"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetInnerHelloLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 测试接口
func NewGetInnerHelloLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInnerHelloLogic {
	return &GetInnerHelloLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetInnerHelloLogic) GetInnerHello(req *types.GetInnerHelloReq, r *http.Request) (resp *types.GetInnerHelloResp, err error) {
	redisValue := ""
	rand.Seed(time.Now().UnixNano())
	// 生成一个 0 到 10000 之间的随机整数
	num := rand.Intn(10000)
	userTask, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetUserTask(l.ctx, 0, 0, 0, 0)
	if err != nil {
		l.Logger.Warnf("get_hello_logic GetUserTask is fail, err is:%v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrStatusWrong)
	}

	redisKey := fmt.Sprintf("test:redis:key:%d", num)
	redisVal := fmt.Sprintf("测试redis,%s,%s,%d", redisKey, req.Name, userTask.TaskId)
	err = l.svcCtx.Redis.Setex(redisKey, redisVal, 10)
	if err != nil {
		l.Logger.Warnf("redis设置值失败, err is: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrStatusWrong)
	}
	redisValue, err = l.svcCtx.Redis.Get(redisKey)
	if err != nil {
		l.Logger.Warnf("redis获取值失败, err is: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrStatusWrong)
	}
	resp = &types.GetInnerHelloResp{
		TaskID:   userTask.TaskId,
		RedisVal: redisValue,
	}
	return
}
