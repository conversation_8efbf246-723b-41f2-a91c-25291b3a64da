package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type OfflineNewCustomerTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 新人任务直接下线
func NewOfflineNewCustomerTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *OfflineNewCustomerTaskLogic {
	return &OfflineNewCustomerTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *OfflineNewCustomerTaskLogic) OfflineNewCustomerTask(req *types.OfflineNewCustomerTaskReq) (resp *types.OfflineNewCustomerTaskResp, err error) {
	resp = &types.OfflineNewCustomerTaskResp{}
	_, err = service.OfflineTask(l.svcCtx, l.ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return
}
