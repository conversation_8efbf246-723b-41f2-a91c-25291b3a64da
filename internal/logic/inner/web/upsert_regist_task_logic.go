package web

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"strconv"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"

	"bitbucket.org/gateio/gateio-lib-base-go/environment"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/coupon"
)

type UpsertRegistTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 新增/修改注册任务并提交审核
func NewUpsertRegistTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpsertRegistTaskLogic {
	return &UpsertRegistTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpsertRegistTaskLogic) UpsertRegistTask(req *types.UpsertRegistTaskReq, r *http.Request) (resp *types.UpsertRegistTaskResp, err error) {
	// 获取操作人员
	creatorId := r.Header.Get("x-gate-admin-id")
	creatorName := r.Header.Get("x-gate-admin-name")

	// 使用 creatorId 和 creatorName 进行后续处理
	l.Logger.Infof("Creator ID: %s, Creator Name: %s", creatorId, creatorName)

	// 将 creatorId 转换为 int64
	creatorIdInt64, err := strconv.ParseInt(creatorId, 10, 64)
	if err != nil {
		return nil, err
	}

	// 构建task
	task := &welfare_task_cfgs.WelfareTaskCfgs{
		TaskRegionKey: req.TaskRegionKey,
		ButtonType:    req.ButtonType,
		Status:        consts.ReviewStatusInit,
	}

	// 获取操作人员
	task.CreatorId = creatorIdInt64
	task.CreatorName = creatorName

	// 拼接地区
	regionCfg, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetRegionCfgInfo(l.ctx, req.TaskRegionKey)
	if regionCfg != nil {
		task.TaskRegion = regionCfg.TaskRegion
	} else {
		l.Logger.Warnf("No region configuration found for TaskRegionKey: %s", req.TaskRegionKey)
		task.TaskRegion = ""
	}

	// 拼接额外字段
	extraTaskInfo := &types.RegisterTaskInfo{
		Source:   req.Source,
		CouponId: req.RewardId,
		// ExtraRewards:
	}
	extraTaskInfoJSON, err := json.Marshal(extraTaskInfo)
	if err != nil {
		l.Logger.Errorf("Failed to marshal extraTaskInfo: %v", err)
		return nil, err
	}
	task.ExtraTaskInfo = sql.NullString{String: string(extraTaskInfoJSON), Valid: true}

	// 拼接奖励字段
	couponInfo, couponErr := service.GetCouponInfo(l.svcCtx, l.ctx, req.RewardId, req.Source)
	if couponErr != nil {
		err = couponErr
		return
	}
	coupons := []*coupon.CouponInfoResponse{couponInfo}
	couponsJSON, err := json.Marshal(coupons)
	if err != nil {
		l.Logger.Errorf("Failed to marshal coupons: %v", err)
		return nil, err
	}
	task.Reward = sql.NullString{String: string(couponsJSON), Valid: true}

	if req.Id == 0 {
		// 新增
		// 查询数据库里是否有
		adtasks, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgListByRegionAndType(l.ctx, req.TaskRegionKey, consts.UserTaskTypeNewbieRegister, 0)
		if err != nil {
			return nil, err
		}
		if len(adtasks) > 0 {
			return nil, errors.New("同一地区只能有一个注册任务")
		}

		task.TaskName = "注册任务"
		task.Type = consts.UserTaskTypeNewbieRegister
		task.TaskType = consts.UserTaskTaskTypeRegister
		task.TaskCenterType = consts.TaskCenterTypeRegular
		task.EffectiveTime = ""
		task.OnlineStatus = consts.StatusOffline
		// 获取sort
		sort, _ := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetMaxSort(l.ctx)
		task.Sort = sort + 1
		if environment.IsPre() {
			task.PreEnv = 1
		}
		task.Version = 1
		dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).InsertWelfareTaskCfg(l.ctx, task)
	} else {
		// 更新
		taskCfg, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgListById(l.ctx, req.Id)
		if err == nil {
			task.Id = req.Id
			task.Version = taskCfg.Version + 1
			dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).UpdateWelfareTaskCfg(l.ctx, task)
		}
	}

	return
}
