package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetLanguageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 拉取多语言信息
func NewGetLanguageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLanguageLogic {
	return &GetLanguageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLanguageLogic) GetLanguage() (resp *types.LauguageResp, err error) {
	resp = &types.LauguageResp{}
	resp.LauguageEnum = consts.BackNeedLangMap

	return
}
