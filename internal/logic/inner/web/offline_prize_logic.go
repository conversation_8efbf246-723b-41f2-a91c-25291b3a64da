package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type OfflinePrizeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 商品下架
func NewOfflinePrizeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *OfflinePrizeLogic {
	return &OfflinePrizeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *OfflinePrizeLogic) OfflinePrize(req *types.OfflinePrizeReq) (resp *types.OfflinePrizeResp, err error) {
	resp = &types.OfflinePrizeResp{}
	_, err = service.OfflinePrize(l.svcCtx, l.ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return
}
