package web

import (
	"context"
	"net/http"
	"strconv"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type ApproveOldCustomerTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 老客审核上线/重新上线
func NewApproveOldCustomerTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ApproveOldCustomerTaskLogic {
	return &ApproveOldCustomerTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ApproveOldCustomerTaskLogic) ApproveOldCustomerTask(req *types.ApproveOldCustomerTaskReq, r *http.Request) (resp *types.ApproveOldCustomerTaskResp, err error) {
	// 获取操作人员
	creatorId := r.Header.Get("x-gate-admin-id")
	creatorName := r.Header.Get("x-gate-admin-name")

	// 将 creatorId 转换为 int64
	creatorIdInt64, err := strconv.ParseInt(creatorId, 10, 64)
	if err != nil {
		return nil, err
	}

	resp = &types.ApproveOldCustomerTaskResp{}
	_, err = service.ApproveTask(l.svcCtx, l.ctx, req.Id, req.Version, creatorIdInt64, creatorName)
	if err != nil {
		return nil, err
	}
	return
}
