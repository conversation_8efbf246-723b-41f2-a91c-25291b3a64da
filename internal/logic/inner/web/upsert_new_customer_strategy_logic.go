package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_region_cfgs"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type UpsertNewCustomerStrategyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 新增/修改地区配置
func NewUpsertNewCustomerStrategyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpsertNewCustomerStrategyLogic {
	return &UpsertNewCustomerStrategyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpsertNewCustomerStrategyLogic) UpsertNewCustomerStrategy(req *types.UpsertNewCustomerStrategyReq) (resp *types.UpsertNewCustomerStrategyResp, err error) {
	// 构建Strategy
	strategy := &welfare_region_cfgs.WelfareRegionCfgs{
		DayNum: int64(req.DayNum),
	}
	if req.Id == 0 {
		// 目前没有新增
		return
	} else {
		// 更新
		strategy.Id = req.Id
		dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).UpdateRegionCfg(l.ctx, strategy)
	}

	return
}
