package web

import (
	"context"
	"strings"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetAdvancedTasksLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取进阶任务
func NewGetAdvancedTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAdvancedTasksLogic {
	return &GetAdvancedTasksLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAdvancedTasksLogic) GetAdvancedTasks(req *types.AdvancedTasksReq) (resp *types.AdvancedTasksResp, err error) {
	resp = &types.AdvancedTasksResp{}

	taskList, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgListByStatus(l.ctx, req.TaskRegionKey,
		[]int{consts.UserTaskTypeNewbieAdvanced}, req.EffectiveStatus, 0)
	if err != nil {
		l.Logger.Errorf("GetWelcomeTasksLogic GetAdvancedTasks is fail ，Params is：%s,%d, err is:%v", req.TaskRegionKey, req.EffectiveStatus, err)
		return
	}

	// 遍历 taskList 并赋值给 resp.List
	resp.List = make([]*types.AdvancedTaskDetail, len(taskList))
	for i, task := range taskList {

		// 定义一个数组变量 []*TaskDetail
		var taskDetails []*types.TaskDetail
		// 获取 advancedTaskInfo
		extraTaskInfo, err := consts.GetExtraTaskInfo(task)
		if err != nil {
			l.Logger.Errorf("GetExtraTaskInfo failed for task ID %d: %v", task.Id, err)
			extraTaskInfo = nil // 如果获取失败，设置为 nil
		}
		// 类型断言，将 extraTaskInfo 转换为 AdvancedTaskInfo 类型
		advancedTaskInfo, ok := extraTaskInfo.(*types.AdvancedTaskInfo)
		if ok && advancedTaskInfo != nil {
			// 遍历 advancedTaskInfo 中的子任务列表
			for _, subTask := range advancedTaskInfo.SubTasks {

				var rewardNames []string
				for _, reward := range subTask.Rewards {
					if reward.RewardName != "" {
						rewardNames = append(rewardNames, reward.RewardName)
					}
				}
				rewardName := strings.Join(rewardNames, ",")

				var extraRewardNames []string
				for _, extraReward := range subTask.ExtraRewards {
					if extraReward.RewardName != "" {
						extraRewardNames = append(extraRewardNames, extraReward.RewardName)
					}
				}
				exraRewardName := strings.Join(extraRewardNames, ",")

				// 将子任务信息赋值给 TaskDetail
				taskDetails = append(taskDetails, &types.TaskDetail{
					TaskId:        subTask.TaskId,
					TaskName:      subTask.TaskName,
					TaskType:      subTask.TaskType,
					EffectiveTime: subTask.EffectiveTime,
					RewardName:    rewardName,
					ExtraReward:   exraRewardName,
				})
			}
		}
		resp.List[i] = &types.AdvancedTaskDetail{
			Id:              task.Id,
			TaskRegionKey:   task.TaskRegionKey,
			TaskRegion:      consts.GetRegionMap()[task.TaskRegionKey],
			Status:          int(task.Status),
			CreatorName:     task.CreatorName,
			ApprovalName:    task.ApprovalName,
			TaskList:        taskDetails,
			OnlineStatus:    int(task.OnlineStatus),
			Version:         int(task.Version),
			EffectiveStatus: consts.GetTaskStatusMap(int(task.Status), int(task.OnlineStatus)),
		}
	}
	return resp, nil
}
