package web

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"strconv"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-base-go/environment"
	task_center "bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
	"gateio_service_welfare_go/internal/utils"
)

type UpsertAdvancedTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 新增/修改进阶任务并提交审核
func NewUpsertAdvancedTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpsertAdvancedTaskLogic {
	return &UpsertAdvancedTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpsertAdvancedTaskLogic) UpsertAdvancedTask(req *types.UpsertAdvancedTaskReq, r *http.Request) (resp *types.UpsertAdvancedTaskResp, err error) {
	// 获取操作人员
	creatorId := r.Header.Get("x-gate-admin-id")
	creatorName := r.Header.Get("x-gate-admin-name")

	// 将 creatorId 转换为 int64
	creatorIdInt64, err := strconv.ParseInt(creatorId, 10, 64)
	if err != nil {
		return nil, err
	}

	// 构建task
	task := &welfare_task_cfgs.WelfareTaskCfgs{
		TaskRegionKey: req.TaskRegionKey,
		Status:        consts.ReviewStatusInit,
	}

	// 获取操作人员
	task.CreatorId = creatorIdInt64
	task.CreatorName = creatorName

	// 拼接地区
	regionCfg, _ := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetRegionCfgInfo(l.ctx, req.TaskRegionKey)
	if regionCfg != nil {
		task.TaskRegion = regionCfg.TaskRegion
	} else {
		l.Logger.Warnf("No region configuration found for TaskRegionKey: %s", req.TaskRegionKey)
		task.TaskRegion = ""
	}

	// 获取任务
	detailRes, err := service_client.NewTaskCenterCall(l.ctx).BatchTaskDetail(utils.IntListToStr(req.TaskIds))
	if err != nil {
		l.Logger.Errorf("service_client.NewTaskCenterClient().BatchTaskDetail is err:%v", err)
		return nil, err
	}
	taskDetilkMap := make(map[int64]*task_center.ListItem)
	for _, item := range detailRes.List {
		taskDetilkMap[int64(item.ID)] = &item
	}

	taskAllRes, err := service_client.NewTaskCenterCall(l.ctx).BatchTaskAll(0, consts.WelfareTaskBusinessType, "", utils.IntListToStr(req.TaskIds))
	if err != nil {
		l.Logger.Errorf("service_client.NewTaskCenterClient().BatchTaskAll is err:%v", err)
		return nil, err
	}
	taskMap := make(map[int64]*task_center.Task)
	for _, centerTask := range taskAllRes.List {
		taskMap[int64(centerTask.TaskID)] = &centerTask
	}

	var subTasks []types.AdvancedSubTaskInfo
	for _, taskId := range req.TaskIds {
		item := taskDetilkMap[taskId]
		centerTask := taskMap[taskId]
		rewardDetailList := service.GetTaskCenterRewardDetail(l.svcCtx, l.ctx, *centerTask)

		rewardList := []types.Reward{}
		extraRewardList := []types.Reward{}
		for _, detail := range rewardDetailList {
			reward := types.Reward{
				RewardId:     detail.RewardCouponId,
				RewardType:   int(detail.RewardType),
				RewardName:   detail.RewardName,
				RewardSource: detail.RewardSource,
			}
			rewardList = append(rewardList, reward)

			extraReward := types.Reward{
				RewardId:     detail.ExtraRewardCouponId,
				RewardType:   int(detail.ExtraRewardType),
				RewardName:   detail.ExtraRewardName,
				RewardSource: detail.ExtraRewardSource,
			}
			extraRewardList = append(extraRewardList, extraReward)
		}

		advancedSubTaskInfo := types.AdvancedSubTaskInfo{
			TaskId:       taskId,
			TaskName:     item.Title,
			TaskType:     int(item.Type),
			Rewards:      rewardList,
			ExtraRewards: extraRewardList,
		}

		advancedSubTaskInfo.EffectiveTime = service.GetEffectiveTime(*item)

		subTasks = append(subTasks, advancedSubTaskInfo)
	}

	taskInfo := types.AdvancedTaskInfo{
		SubTasks: subTasks,
	}

	extraByteVal, _ := json.Marshal(taskInfo)
	task.ExtraTaskInfo = sql.NullString{String: string(extraByteVal), Valid: true}

	if req.Id == 0 {
		// 新增
		// 查询数据库里是否有
		adtasks, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgListByRegionAndType(l.ctx, req.TaskRegionKey, consts.UserTaskTypeNewbieAdvanced, 0)
		if err != nil {
			return nil, err
		}
		if len(adtasks) > 0 {
			return nil, errors.New("同一地区只能有一个进阶任务")
		}

		task.Type = consts.UserTaskTypeNewbieAdvanced
		task.OnlineStatus = consts.StatusOffline
		// 获取sort
		sort, _ := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetMaxSort(l.ctx)
		task.Sort = sort + 1
		if environment.IsPre() {
			task.PreEnv = 1
		}
		task.Version = 1
		dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).InsertWelfareTaskCfg(l.ctx, task)
	} else {
		// 更新
		taskCfg, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgListById(l.ctx, req.Id)
		if err == nil {
			task.Id = req.Id
			task.Version = taskCfg.Version + 1
			dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).UpdateWelfareTaskCfg(l.ctx, task)
		}
	}

	return
}
