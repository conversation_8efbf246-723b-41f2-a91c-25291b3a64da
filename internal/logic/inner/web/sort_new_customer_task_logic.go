package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type SortNewCustomerTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 新人任务 拖动排序
func NewSortNewCustomerTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SortNewCustomerTaskLogic {
	return &SortNewCustomerTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SortNewCustomerTaskLogic) SortNewCustomerTask(req *types.SortNewCustomerTaskReq) (resp *types.SortNewCustomerTaskResp, err error) {
	resp = &types.SortNewCustomerTaskResp{}
	_, err = service.SortTask(l.svcCtx, l.ctx, req.TaskSortMap)
	if err != nil {
		return nil, err
	}
	return
}
