package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type SortOldCustomerTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 老客新人拖动排序
func NewSortOldCustomerTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SortOldCustomerTaskLogic {
	return &SortOldCustomerTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SortOldCustomerTaskLogic) SortOldCustomerTask(req *types.SortOldCustomerTaskReq) (resp *types.SortOldCustomerTaskResp, err error) {
	resp = &types.SortOldCustomerTaskResp{}
	_, err = service.SortTask(l.svcCtx, l.ctx, req.TaskSortMap)
	if err != nil {
		return nil, err
	}
	return
}
