package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type OfflineOldCustomerTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 老客直接下线
func NewOfflineOldCustomerTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *OfflineOldCustomerTaskLogic {
	return &OfflineOldCustomerTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *OfflineOldCustomerTaskLogic) OfflineOldCustomerTask(req *types.OfflineOldCustomerTaskReq) (resp *types.OfflineOldCustomerTaskResp, err error) {
	resp = &types.OfflineOldCustomerTaskResp{}
	_, err = service.OfflineTask(l.svcCtx, l.ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return
}
