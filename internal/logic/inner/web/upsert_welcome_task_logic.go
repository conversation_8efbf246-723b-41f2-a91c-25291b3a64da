package web

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-base-go/environment"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type UpsertWelcomeTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 新增/修改其他任务并提交审核
func NewUpsertWelcomeTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpsertWelcomeTaskLogic {
	return &UpsertWelcomeTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpsertWelcomeTaskLogic) UpsertWelcomeTask(req *types.UpsertWelcomeTaskReq, r *http.Request) (resp *types.UpsertWelcomeTaskResp, err error) {
	// 获取操作人员
	creatorId := r.Header.Get("x-gate-admin-id")
	creatorName := r.Header.Get("x-gate-admin-name")

	// 使用 creatorId 和 creatorName 进行后续处理
	l.Logger.Infof("Creator ID: %s, Creator Name: %s", creatorId, creatorName)

	// 将 creatorId 转换为 int64
	creatorIdInt64, err := strconv.ParseInt(creatorId, 10, 64)
	if err != nil {
		return nil, err
	}

	// 构建task
	task := &welfare_task_cfgs.WelfareTaskCfgs{
		TaskRegionKey: req.TaskRegionKey,
		TaskId:        req.TaskId,
		ButtonType:    req.ButtonType,
		Status:        consts.ReviewStatusInit,
	}

	// 获取操作人员
	task.CreatorId = creatorIdInt64
	task.CreatorName = creatorName

	// 拼接地区
	regionCfg, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetRegionCfgInfo(l.ctx, req.TaskRegionKey)
	if regionCfg != nil {
		task.TaskRegion = regionCfg.TaskRegion
	} else {
		l.Logger.Warnf("No region configuration found for TaskRegionKey: %s", req.TaskRegionKey)
		task.TaskRegion = ""
	}

	// 获取任务信息
	rewardList := []types.Reward{}
	extraRewardList := []types.Reward{}
	detailRes, err := service_client.NewTaskCenterCall(l.ctx).BatchTaskDetail(strconv.FormatInt(req.TaskId, 10))
	if err != nil {
		l.Logger.Errorf("service_client.NewTaskCenterClient().BatchTaskDetail is err:%v", err)
		return nil, err
	}
	item := detailRes.List[0]
	task.TaskName = item.Title
	task.TaskCenterType = int64(item.Type)

	task.EffectiveTime = service.GetEffectiveTime(item)

	// 获取任务奖励
	taskAllRes, err := service_client.NewTaskCenterCall(l.ctx).BatchTaskAll(0, consts.WelfareTaskBusinessType, "", strconv.FormatInt(req.TaskId, 10))
	if err != nil {
		l.Logger.Errorf("service_client.NewTaskCenterClient().BatchTaskAll is err:%v", err)
		return nil, err
	}
	taskRes := taskAllRes.List[0]

	// 存第一个规则的mark转换后的int
	taskMarkMap := consts.GetTaskMarkMapTaskType()
	if taskType, ok := taskMarkMap[taskRes.RuleInfo[0].Mark]; ok {
		task.TaskType = int64(taskType)
	} else {
		errMsg := fmt.Sprintf("任务中心类型 %s 未在福利中心定义", taskRes.RuleInfo[0].Mark)
		l.Logger.Warnf(errMsg)
		return nil, errors.New(errMsg)
	}

	rewardDetailList := service.GetTaskCenterRewardDetail(l.svcCtx, l.ctx, taskRes)
	for _, detail := range rewardDetailList {
		reward := types.Reward{
			RewardId:     detail.RewardCouponId,
			RewardType:   int(detail.RewardType),
			RewardName:   detail.RewardName,
			RewardSource: detail.RewardSource,
		}
		rewardList = append(rewardList, reward)

		extraReward := types.Reward{
			RewardId:     detail.ExtraRewardCouponId,
			RewardType:   int(detail.ExtraRewardType),
			RewardName:   detail.ExtraRewardName,
			RewardSource: detail.ExtraRewardSource,
		}
		extraRewardList = append(extraRewardList, extraReward)
	}
	byteVal, _ := json.Marshal(rewardList)
	task.Reward = sql.NullString{String: string(byteVal), Valid: true}

	extraTaskInfo := types.WelcomeTaskInfo{
		LimitDays:    *req.LimitDays,
		LimitRewards: extraRewardList,
	}
	extraByteVal, _ := json.Marshal(extraTaskInfo)
	task.ExtraTaskInfo = sql.NullString{String: string(extraByteVal), Valid: true}

	if req.Id == 0 {
		// 新增
		task.Type = consts.UserTaskTypeNewbieGuide
		task.OnlineStatus = consts.StatusOffline
		// 获取sort
		sort, _ := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetMaxSort(l.ctx)
		task.Sort = sort + 1
		if environment.IsPre() {
			task.PreEnv = 1
		}
		task.Version = 1
		dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).InsertWelfareTaskCfg(l.ctx, task)
	} else {
		// 更新
		taskCfg, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgListById(l.ctx, req.Id)
		if err == nil {
			task.Id = req.Id
			task.Version = taskCfg.Version + 1
			dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).UpdateWelfareTaskCfg(l.ctx, task)
		}
	}

	return
}
