package web

import (
	"context"
	"encoding/json"
	"strings"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetLimitTimeTasksLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取限时任务
func NewGetLimitTimeTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLimitTimeTasksLogic {
	return &GetLimitTimeTasksLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLimitTimeTasksLogic) GetLimitTimeTasks(req *types.GetLimitTimeTasksReq) (resp *types.GetLimitTimeTasksResp, err error) {
	resp = &types.GetLimitTimeTasksResp{}

	taskList, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgListByStatus(l.ctx, req.TaskRegionKey,
		[]int{consts.UserTaskTypeVeteranLimited}, req.EffectiveStatus, req.TaskIdentityId)
	if err != nil {
		l.Logger.Errorf("GetLimitTimeTasks is fail ，Params is：%s,%d,%d, err is:%v", req.TaskRegionKey, req.EffectiveStatus, req.TaskIdentityId, err)
		return
	}

	// 遍历 taskList 并赋值给 resp.List
	resp.List = make([]*types.TaskDetail, len(taskList))
	for i, task := range taskList {
		rewardName := ""
		var rewards []types.Reward
		if task.Reward.Valid {
			// 将 task.Reward 转换为 types.Reward 的列表
			err = json.Unmarshal([]byte(task.Reward.String), &rewards)
			if err != nil {
				l.Logger.Errorf("GetLimitTimeTasks failed to unmarshal rewards: %s, err: %v", task.Reward.String, err)
				rewards = []types.Reward{} // 如果解析失败，初始化为空列表
			}
			var rewardNames []string
			for _, reward := range rewards {
				if reward.RewardName != "" {
					rewardNames = append(rewardNames, reward.RewardName)
				}
			}
			rewardName = strings.Join(rewardNames, ",")
		}

		extraTaskInfo, err := consts.GetExtraTaskInfo(task)
		if err != nil {
			extraTaskInfo = err
		}

		resp.List[i] = &types.TaskDetail{
			Id:               task.Id,
			Sort:             int(task.Sort),
			TaskRegionKey:    task.TaskRegionKey,
			TaskRegion:       consts.GetRegionMap()[task.TaskRegionKey],
			TaskIdentityName: task.TaskIdentityName,
			TaskId:           task.TaskId,
			TaskName:         task.TaskName,
			TaskType:         int(task.TaskCenterType),
			EffectiveTime:    task.EffectiveTime,
			RewardName:       rewardName,
			Status:           int(task.Status),
			CreatorName:      task.CreatorName,
			ApprovalName:     task.ApprovalName,
			LimitTimeReward:  consts.GetLimitTimeRewardName(task),
			ExtraReward:      consts.GetExtraRewardName(task),
			StairsReward:     consts.GetStairsRewardName(task),
			ExtraTaskInfo:    extraTaskInfo,
			TaskIdentityId:   int(task.TaskIdentityId),
			Type:             int(task.Type),
			TaskCenterType:   int(task.TaskCenterType),
			ButtonType:       task.ButtonType,
			OnlineStatus:     int(task.OnlineStatus),
			Version:          int(task.Version),
			EffectiveStatus:  consts.GetTaskStatusMap(int(task.Status), int(task.OnlineStatus)),
		}
	}
	return resp, nil
}
