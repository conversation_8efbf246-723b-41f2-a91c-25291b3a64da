package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetInnerHelloLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 测试接口
func NewGetInnerHelloLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInnerHelloLogic {
	return &GetInnerHelloLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetInnerHelloLogic) GetInnerHello(_ *types.GetInnerHelloReq) (resp *types.GetInnerHelloResp, err error) {

	return
}
