package web

import (
	"context"
	"encoding/json"
	"strings"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/coupon"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetWelcomeTasksLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取入门任务
func NewGetWelcomeTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWelcomeTasksLogic {
	return &GetWelcomeTasksLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWelcomeTasksLogic) GetWelcomeTasks(req *types.WelcomeTasksReq) (resp *types.WelcomeTasksResp, err error) {
	resp = &types.WelcomeTasksResp{}

	taskList, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgListByStatus(l.ctx, req.TaskRegionKey,
		[]int{consts.UserTaskTypeNewbieRegister, consts.UserTaskTypeNewbieGuide}, req.EffectiveStatus, 0)
	if err != nil {
		l.Logger.Errorf("GetWelcomeTasksLogic GetWelcomeTasks is fail ，Params is：%s,%d, err is:%v", req.TaskRegionKey, req.EffectiveStatus, err)
		return
	}

	// 遍历 taskList 并赋值给 resp.List
	resp.List = make([]*types.TaskDetail, len(taskList))
	for i, task := range taskList {
		rewardName := ""

		if task.Reward.Valid {
			var rewardNames []string

			if task.Type == consts.UserTaskTypeNewbieRegister {
				var coupons []coupon.CouponInfoResponse
				err = json.Unmarshal([]byte(task.Reward.String), &coupons)
				if err != nil {
					l.Logger.Errorf("GetWelcomeTasksLogic GetWelcomeTasks failed to unmarshal rewards: %s, err: %v", task.Reward.String, err)
					coupons = []coupon.CouponInfoResponse{} // 如果解析失败，初始化为空列表
				}
				for _, coupon := range coupons {
					if coupon.CouponName != "" {
						rewardNames = append(rewardNames, coupon.CouponName)
					}
				}
			} else {
				var rewards []types.Reward
				err = json.Unmarshal([]byte(task.Reward.String), &rewards)
				if err != nil {
					l.Logger.Errorf("GetWelcomeTasksLogic GetWelcomeTasks failed to unmarshal rewards: %s, err: %v", task.Reward.String, err)
					rewards = []types.Reward{} // 如果解析失败，初始化为空列表
				}
				for _, reward := range rewards {
					if reward.RewardName != "" {
						rewardNames = append(rewardNames, reward.RewardName)
					}
				}
			}
			rewardName = strings.Join(rewardNames, ",")
		}

		extraTaskInfo, err := consts.GetExtraTaskInfo(task)
		if err != nil {
			extraTaskInfo = err
		}

		resp.List[i] = &types.TaskDetail{
			Id:               task.Id,
			Sort:             int(task.Sort),
			TaskRegionKey:    task.TaskRegionKey,
			TaskRegion:       consts.GetRegionMap()[task.TaskRegionKey],
			TaskIdentityName: task.TaskIdentityName,
			TaskId:           task.TaskId,
			TaskName:         task.TaskName,
			TaskType:         int(task.TaskCenterType),
			EffectiveTime:    task.EffectiveTime,
			RewardName:       rewardName,
			Status:           int(task.Status),
			CreatorName:      task.CreatorName,
			ApprovalName:     task.ApprovalName,
			LimitTimeReward:  consts.GetLimitTimeRewardName(task),
			ExtraReward:      consts.GetExtraRewardName(task),
			StairsReward:     consts.GetStairsRewardName(task),
			ExtraTaskInfo:    extraTaskInfo,
			TaskIdentityId:   int(task.TaskIdentityId),
			Type:             int(task.Type),
			TaskCenterType:   int(task.TaskCenterType),
			ButtonType:       task.ButtonType,
			OnlineStatus:     int(task.OnlineStatus),
			Version:          int(task.Version),
			EffectiveStatus:  consts.GetTaskStatusMap(int(task.Status), int(task.OnlineStatus)),
		}
	}
	return resp, nil
}
