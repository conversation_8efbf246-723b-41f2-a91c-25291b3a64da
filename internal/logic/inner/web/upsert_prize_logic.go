package web

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-base-go/environment"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_points_shop"
	"gateio_service_welfare_go/internal/models/welfare_points_shops_current_limit"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type UpsertPrizeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 新增/修改商品
func NewUpsertPrizeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpsertPrizeLogic {
	return &UpsertPrizeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpsertPrizeLogic) UpsertPrize(req *types.UpsertPrizeReq) (resp *types.UpsertPrizeResp, err error) {
	// 构建task
	pointShop := &welfare_points_shop.WelfarePointsShop{
		PrizeId:        req.PrizeId,
		Source:         req.Source,
		PrizeUrl:       req.PrizeDescUrl,
		ExchangePoints: int64(req.ExchangePoints),
		ExchangeCycle:  int64(req.ExchangeCycle),
		PrizeMaxNum:    int64(req.PrizeMaxNum),
		ExchangeNum:    int64(req.ExchangeNum),
	}
	prizeNameJSON, err := json.Marshal(req.PrizeName)
	if err != nil {
		l.Logger.Errorf("Failed to marshal PrizeName: %v", err)
		return nil, err
	}
	pointShop.PrizeName = string(prizeNameJSON)

	pointShop.PrizeType = consts.ShopPrizeTypePoints
	prizeSubType := int64(0)
	if pointShop.PrizeType == consts.ShopPrizeTypePoints {
		// 获取卡券信息
		couponInfo, couponErr := service.GetCouponInfo(l.svcCtx, l.ctx, req.PrizeId, req.Source)
		if couponErr != nil {
			err = couponErr
			return
		}

		market, ok := couponInfo.CouponExtInfo["market"].(string)
		if !ok {
			market = ""
		}
		prizeSubTypeInt := consts.GetCouponTypeByTypeAndSubType(couponInfo.CouponType, market)
		if prizeSubTypeInt == -1 {
			errMsg := fmt.Sprintf("卡券中台类型 %s 未在福利中心定义，market: %s", couponInfo.CouponType, market)
			l.Logger.Infof(errMsg)
			return nil, errors.New(errMsg)
		} else {
			prizeSubType = int64(prizeSubTypeInt)
		}

		pointShop.PrizeSubType = prizeSubType

		pointShop.PrizeValue = couponInfo.Amount

		listJSON, err := json.Marshal(couponInfo.CouponShortRuleTranslationList)
		if err != nil {
			l.Logger.Errorf("Failed to marshal CouponRewardTranslationList: %v", err)
			return nil, err
		}

		updatedListJSON := strings.Replace(string(listJSON), `"zh"`, `"cn"`, -1)
		pointShop.PrizeDesc = updatedListJSON
	}

	// amount有没有可能不是数字？目前没有，有了再说吧
	amount, err := strconv.ParseInt(pointShop.PrizeValue, 10, 64)
	if err != nil {
		l.Logger.Errorf("Failed to parse pointShop.PrizeValue as int64 %s: %v", pointShop.PrizeValue, err)
		return nil, err
	}

	if req.Id == 0 {
		// 新增
		pointShop.PrizeType = consts.ShopPrizeTypePoints
		pointShop.Status = consts.PointsShopStatusOnline
		// 获取sort
		sort, _ := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).ShopGetMaxSort(l.ctx)
		pointShop.Sort = sort + 1

		if environment.IsPre() {
			pointShop.PreEnv = 1
		}

		newPrizeId, _ := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).InsertWelfarePointsShop(l.ctx, pointShop)

		currentLimit := &welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{
			PrizeId: newPrizeId,
			Type:    prizeSubType,
			TypeNum: amount,
			Num:     int64(req.PrizeMaxNum),
		}
		dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).InsertCurrentLimit(l.ctx, currentLimit)
	} else {
		// 更新
		pointShop.Id = req.Id
		dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).UpdateWelfarePointsShopAndPrizeMaxNum(l.ctx, pointShop)

		// 获取current_limit
		currentLimit, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetCurrentLimitByPrizeId(l.ctx, req.Id)
		if err != nil {
			l.Logger.Errorf("GetCurrentLimitByPrizeId is fail ，err is:%v", err)
			return nil, err
		}
		updateNum := currentLimit.Num
		if int64(req.PrizeMaxNum) < currentLimit.Num {
			updateNum = int64(req.PrizeMaxNum)
		}

		updateCurrentLimit := &welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{
			PrizeId: req.Id,
			Type:    prizeSubType,
			TypeNum: amount,
			Num:     updateNum,
		}

		dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).UpdateCurrentLimit(l.ctx, updateCurrentLimit)
		// 删除缓存
		dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).DelRedisCurrentLimit(l.svcCtx, l.ctx, int(req.Id), int(prizeSubType), int(amount))

	}

	return
}
