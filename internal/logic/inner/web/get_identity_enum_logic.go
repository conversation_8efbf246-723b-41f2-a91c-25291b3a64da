package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetIdentityEnumLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取身份（人群）枚举
func NewGetIdentityEnumLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetIdentityEnumLogic {
	return &GetIdentityEnumLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetIdentityEnumLogic) GetIdentityEnum() (resp *types.IdentityEnumResp, err error) {
	resp = &types.IdentityEnumResp{}
	resp.IdentityEnum = consts.GetIdentityMap()

	return
}
