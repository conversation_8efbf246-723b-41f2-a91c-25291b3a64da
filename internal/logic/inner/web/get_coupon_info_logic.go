package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetCouponInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 拉取卡券信息
func NewGetCouponInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCouponInfoLogic {
	return &GetCouponInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCouponInfoLogic) GetCouponInfo(req *types.CouponInfoReq) (resp *types.CouponInfoResp, err error) {
	resp = &types.CouponInfoResp{}

	couponInfo, couponErr := service.GetCouponInfo(l.svcCtx, l.ctx, req.CouponId, req.Source)
	if couponErr != nil {
		err = couponErr
		return
	}

	assembleMap := make(map[string]interface{})
	for _, item := range couponInfo.CouponAssembleListInfo {
		assembleMap[item.Key] = item.Value
	}
	couponDesc := couponInfo.CouponShortRuleTranslationList
	assembleMap["卡券描述"] = couponDesc
	resp.Map = assembleMap
	return
}
