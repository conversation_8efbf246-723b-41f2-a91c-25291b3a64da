package web

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetTaskInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 拉取任务信息
func NewGetTaskInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTaskInfoLogic {
	return &GetTaskInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTaskInfoLogic) GetTaskInfo(ctx context.Context, req *types.TaskInfoReq) (resp *types.TaskInfoResp, err error) {
	resp = &types.TaskInfoResp{}

	// 获取任务类型等
	detailRes, err := service_client.NewTaskCenterCall(ctx).BatchTaskDetail(strconv.FormatInt(req.TaskId, 10))
	if err != nil {
		l.Logger.Errorf("service_client.NewTaskCenterClient().BatchTaskDetail is err:%v", err)
		return nil, err
	}
	if len(detailRes.List) < 1 {
		return resp, nil
	}
	item := detailRes.List[0]
	resp.TaskName = item.Title
	resp.TaskType = item.Type
	// effective_time_type:1  固定时间范围 ，任务开始时间start_time - 结束时间end_time
	// effective_time_type:2  领取后，天数：claim_days
	// 生效时间类型（根据 EffectiveTimeType 判断）
	if item.EffectiveTimeType == 1 {
		// 固定时间范围，任务开始时间 - 结束时间
		startTime := time.Unix(item.StartTime, 0).Format("2006-01-02 15:04:05")
		endTime := time.Unix(item.EndTime, 0).Format("2006-01-02 15:04:05")
		resp.EffectiveTime = fmt.Sprintf("%s - %s", startTime, endTime)
	} else if item.EffectiveTimeType == 2 {
		// 领取后，天数：claim_days
		resp.EffectiveTime = fmt.Sprintf("领取后 %d 天", item.ClaimDays)
	}

	// 获取任务奖励
	taskAllRes, err := service_client.NewTaskCenterCall(ctx).BatchTaskAll(0, consts.WelfareTaskBusinessType, "", strconv.FormatInt(req.TaskId, 10))
	if err != nil {
		l.Logger.Errorf("service_client.NewTaskCenterClient().BatchTaskAll is err:%v", err)
		return nil, err
	}
	if len(taskAllRes.List) < 1 {
		return nil, errors.New("任务中心BatchTaskAll未返回任务")
	}
	taskRes := taskAllRes.List[0]

	rewardList := []string{}
	extraRewardList := []string{}
	rewardDetailList := service.GetTaskCenterRewardDetail(l.svcCtx, l.ctx, taskRes)

	for _, detail := range rewardDetailList {
		if detail.RewardName != "" {
			rewardList = append(rewardList, detail.RewardName)
		}
		if detail.ExtraRewardName != "" {
			extraRewardList = append(extraRewardList, detail.ExtraRewardName)
		}

	}
	resp.Reward = strings.Join(rewardList, ",")
	resp.ExtraReward = strings.Join(extraRewardList, ",")
	return resp, nil
}
