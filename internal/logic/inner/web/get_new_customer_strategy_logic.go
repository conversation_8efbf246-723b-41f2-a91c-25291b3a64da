package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetNewCustomerStrategyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取地区配置
func NewGetNewCustomerStrategyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetNewCustomerStrategyLogic {
	return &GetNewCustomerStrategyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetNewCustomerStrategyLogic) GetNewCustomerStrategy(req *types.NewCustomerStrategyReq) (resp *types.NewCustomerStrategyResp, err error) {
	resp = &types.NewCustomerStrategyResp{}
	regionCfgList, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryRegionCfgList(l.ctx, int(req.CustomerType))
	if err != nil {
		l.Logger.Errorf("GetNewCustomerStrategy is fail ，err is:%v", err)
		return nil, err
	}

	var regionInfoList []*types.RegionInfo
	for _, v := range regionCfgList {
		regionInfoList = append(regionInfoList, &types.RegionInfo{
			Id:            v.Id,
			TaskRegionKey: v.TaskRegionKey,
			TaskRegion:    v.TaskRegion,
			DayNum:        int(v.DayNum),
		})
	}
	resp.List = regionInfoList
	return
}
