package web

import (
	"context"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type SortPrizeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 商品拖动排序
func NewSortPrizeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SortPrizeLogic {
	return &SortPrizeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SortPrizeLogic) SortPrize(req *types.SortPrizeReq) (resp *types.SortPrizeResp, err error) {
	resp = &types.SortPrizeResp{}
	_, err = service.SortPrize(l.svcCtx, l.ctx, req.PrizeSortMap)
	if err != nil {
		return nil, err
	}
	return
}
