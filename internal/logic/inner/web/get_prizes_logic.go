package web

import (
	"context"
	"encoding/json"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
	"gateio_service_welfare_go/internal/utils"
)

type GetPrizesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取商品
func NewGetPrizesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPrizesLogic {
	return &GetPrizesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPrizesLogic) GetPrizes() (resp *types.GetPrizesResp, err error) {
	resp = &types.GetPrizesResp{}
	welfarePointsShopList, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetAllPrizeList(l.ctx)
	if err != nil {
		l.Logger.Errorf("GetPrizes is fail ，err is:%v", err)
		return nil, err
	}

	var prizeList []*types.PrizeDetail
	for _, v := range welfarePointsShopList {

		// prizeSurplusNum, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetCurrentLimitNumByPrizeId(l.ctx, v.Id)
		// 改为读取缓存
		prizeSurplusNum, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetRedisCurrentLimit(l.svcCtx, l.ctx, int(v.Id), int(v.PrizeSubType), int(utils.MustInt64(v.PrizeValue)))
		if err != nil {
			l.Logger.Errorf("GetCurrentLimitNumByPrizeId is fail ，err is:%v", err)
			return nil, err
		}

		var prizeNameMap map[string]string
		err = json.Unmarshal([]byte(v.PrizeName), &prizeNameMap)
		if err != nil {
			l.Logger.Errorf("Failed to unmarshal PrizeName JSON: %v", err)
			return nil, err
		}

		prizeList = append(prizeList, &types.PrizeDetail{
			Id:              v.Id,
			PrizeId:         v.PrizeId,
			PrizeName:       prizeNameMap,
			PrizeVaule:      v.PrizeValue,
			PrizeUrl:        v.PrizeUrl,
			ExchangePoints:  int(v.ExchangePoints),
			PrizeMaxNum:     int(v.PrizeMaxNum),
			ExchangeCycle:   int(v.ExchangeCycle),
			ExchangeNum:     int(v.ExchangeNum),
			PrizeType:       utils.PrizeTypeList[int(v.PrizeSubType)],
			PrizeSurplusNum: prizeSurplusNum,
			Sort:            int(v.Sort),
			Source:          v.Source,
		})
	}
	resp.List = prizeList
	return
}
