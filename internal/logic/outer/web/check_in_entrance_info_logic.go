package web

import (
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

const (
	CheckInCacheValue = "1"
)

type CheckInEntranceInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 签到入口查询
func NewCheckInEntranceInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckInEntranceInfoLogic {
	return &CheckInEntranceInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CheckInEntranceInfoLogic) CheckInEntranceInfo(r *http.Request) (resp *types.CheckInEntranceInfoResp, err error) {
	resp = &types.CheckInEntranceInfoResp{}
	userInfo := requestools.GetUserInfo(r)
	uid := 0
	if userInfo != nil {
		uid = userInfo.UID
	}
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		l.Logger.Info("CheckInEntranceInfo resp: ", string(respJson), " err: ", err, "  uid is:", uid)
	}()

	if uid <= 0 {
		resp = &types.CheckInEntranceInfoResp{
			IsFirstVisit: 0,
			IsAttendance: 0,
			Points:       0,
		}
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}

	// 查询是否为第一次进入福利中心
	firstRes, _ := l.IsTodayFirstVisit(uid)
	if firstRes {
		resp.IsFirstVisit = 1
	}

	today := utils.GetYmdDay(0)
	//获取用户签到记录
	cacheData, _ := l.GetTodayCheckIn(uid, today)
	if cacheData == false {
		// 查询用户今日签到记录
		checkInList, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryCheckInList(l.ctx, uid, today, consts.CheckInStatusFailed)
		if err != nil {
			l.Logger.Errorf("CheckInEntranceInfo QueryCheckInList failed: uid=%d, today=%d, err=%v", uid, today, err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}

		// 检查今日是否已签到
		for _, checkIn := range checkInList {
			if checkIn.Day == int64(today) {
				resp.IsAttendance = 1
				break // 找到今日签到记录后立即退出循环
			}
		}

		// 如果今日已签到，设置缓存
		if resp.IsAttendance == 1 {
			if _, err := l.SetTodayCheckIn(uid, today); err != nil {
				l.Logger.Warnf("SetTodayCheckIn failed: uid=%d, today=%d, err=%v", uid, today, err)
			}
		}
	} else {
		resp.IsAttendance = 1
	}

	// 查询用户积分
	points, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetPointsByUid(l.svcCtx, l.ctx, int64(uid))
	if err != nil {
		l.Logger.Errorf("GetPointsByUid: get points failed, uid=%d, err=%v", uid, err)
	}
	resp.Points = int(points)
	return
}

// GetTodayCheckIn 获取签到缓存
func (l *CheckInEntranceInfoLogic) GetTodayCheckIn(uid int, day int) (bool, error) {
	redisKey := fmt.Sprintf(consts.TodayCheckIn, uid, day)

	// 尝试获取key
	val, err := l.svcCtx.Redis.Get(redisKey)
	if err != nil {
		l.Logger.Warnf("GetTodayCheckIn: get redis key failed, key=%s, err=%v", redisKey, err)
		return false, err
	}
	if val == CheckInCacheValue {
		return true, nil
	}

	return false, nil
}

// SetTodayCheckIn 设置签到缓存
func (l *CheckInEntranceInfoLogic) SetTodayCheckIn(uid int, day int) (bool, error) {
	redisKey := fmt.Sprintf(consts.TodayCheckIn, uid, day)

	// 计算今天24点的过期时间
	now := time.Now()
	todayEnd := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	expireSeconds := int(todayEnd.Sub(now).Seconds())

	// key不存在，设置key并返回true
	if err := l.svcCtx.Redis.Setex(redisKey, CheckInCacheValue, expireSeconds); err != nil {
		l.Logger.Warnf("SetTodayCheckIn: set redis key failed, key=%s, err=%v", redisKey, err)
		return false, err
	}

	return true, nil
}

// IsTodayFirstVisit 判断今日是否第一次访问
func (l *CheckInEntranceInfoLogic) IsTodayFirstVisit(uid int) (bool, error) {
	redisKey := fmt.Sprintf(consts.FirstVisitKey, uid)

	// 尝试获取key
	val, err := l.svcCtx.Redis.Get(redisKey)
	if err != nil {
		l.Logger.Errorf("IsFirstVisit: get redis key failed, key=%s, err=%v", redisKey, err)
		return false, err
	}
	if val != "" {
		return false, nil
	}

	// 计算今天24点的过期时间
	now := time.Now()
	todayEnd := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	expireSeconds := int(todayEnd.Sub(now).Seconds())

	// key不存在，设置key并返回true
	if err := l.svcCtx.Redis.Setex(redisKey, "1", expireSeconds); err != nil {
		l.Logger.Warnf("IsFirstVisit: set redis key failed, key=%s, err=%v", redisKey, err)
		return false, err
	}

	return true, nil
}
