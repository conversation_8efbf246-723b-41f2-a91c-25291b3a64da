package web

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"context"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"math/rand"
	"net/http"
	"time"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetHelloLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 测试接口
func NewGetHelloLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetHelloLogic {
	return &GetHelloLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetHelloLogic) GetHello(req *types.GetHelloReq, r *http.Request) (resp *types.GetHelloResp, err error) {
	redisValue := ""
	rand.Seed(time.Now().UnixNano())
	// 生成一个 0 到 10000 之间的随机整数
	num := rand.Intn(10000)
	userTask, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetUserTask(l.ctx, 0, 0, 0, 0)
	if err != nil {
		logx.Warnf("get_hello_logic GetUserTask is err, err is:%v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrStatusWrong)
	}

	redisKey := fmt.Sprintf("test:redis:key:%d", num)
	redisVal := fmt.Sprintf("测试redis,%s,%s,%d", redisKey, req.Name, userTask.TaskId)
	err = l.svcCtx.Redis.Setex(redisKey, redisVal, 10)
	if err != nil {
		logx.Warnf("redis设置值失败, err is: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrStatusWrong)
	}
	redisValue, err = l.svcCtx.Redis.Get(redisKey)
	if err != nil {
		logx.Warnf("redis获取值失败, err is: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrStatusWrong)
	}
	resp = &types.GetHelloResp{
		TaskID:   userTask.TaskId,
		RedisVal: redisValue,
	}
	return
}
