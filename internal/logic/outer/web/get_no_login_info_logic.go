package web

import (
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/service"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetNoLoginInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取请求IP的国家ID
func NewGetNoLoginInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetNoLoginInfoLogic {
	return &GetNoLoginInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetNoLoginInfoLogic) GetNoLoginInfo(req *types.UniversalNoParamReq, r *http.Request) (resp *types.GetNoLoginInfoResp, err error) {
	countryId, err := service.GetClientCountry(r, l.ctx)
	if err != nil {
		logx.Errorf("GetNoLoginInfo service.GetClientCountry is err, err is:%v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrApiError)
	}
	isBlack, err := service.IsBlackCountry(l.svcCtx, l.ctx, int64(countryId), 0)
	if err != nil {
		logx.Errorf("GetNoLoginInfo service.IsBlackCountry is err, err is:%v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	resp = &types.GetNoLoginInfoResp{
		IsBlack: isBlack,
	}
	return
}
