package web

import (
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/service"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type DailyTaskListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 老客每日任务列表
func NewDailyTaskListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DailyTaskListLogic {
	return &DailyTaskListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DailyTaskListLogic) DailyTaskList(_ *types.UniversalNoParamReq, r *http.Request) (resp *types.DailyTaskListFormatResp, err error) {
	return service.NewOldCustomerTaskService(l.ctx, l.svcCtx).GetTaskList(consts.UserTaskTypeVeteranDaily, r)
}
