package web

import (
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/service"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type UnLoginUserRegionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取未登录态用户区域
func NewUnLoginUserRegionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UnLoginUserRegionLogic {
	return &UnLoginUserRegionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UnLoginUserRegionLogic) UnLoginUserRegion(req *types.UniversalNoParamReq, r *http.Request) (resp *types.UnLoginUserRegionResp, err error) {
	region, err := service.GetUnLoginUserBelongRegion(l.svcCtx, l.ctx, r)
	if err != nil {
		logx.Infof("UnLoginUserRegion GetUnLoginUserBelongRegion is err, err is:%v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	resp = &types.UnLoginUserRegionResp{
		UserRegion: region,
	}
	return
}
