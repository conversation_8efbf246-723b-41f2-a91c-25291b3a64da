package web

import (
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"context"
	"encoding/json"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_points_shop"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/types"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"strings"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
)

type PointsPrizeListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	dbUtil *dao.WelfareMysqlDBUtil
}

// 积分兑换奖品列表
func NewPointsPrizeListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PointsPrizeListLogic {
	return &PointsPrizeListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		dbUtil: dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB),
	}
}

func (l *PointsPrizeListLogic) PointsPrizeList(_ *types.UniversalNoParamReq, r *http.Request) (resp *types.PointsPrizeListResp, err error) {
	// 获取用户地区和语言信息
	country := utils.GetUserCountry(r)
	lang := requestools.GetUserLanguage(r)

	// 获取积分商店奖品列表
	pointsShop, err := l.getPrizeList()
	if err != nil {
		logx.Errorf("PointsPrizeList getPrizeList failed: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	// 根据合规策略过滤奖品
	filteredShop, err := l.filterPrizesByCompliance(country, pointsShop, r)
	if err != nil {
		logx.Errorf("PointsPrizeList filterPrizesByCompliance failed: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}

	// 构建响应数据
	prizeList := l.buildPrizeList(filteredShop, lang)

	return &types.PointsPrizeListResp{
		List: prizeList,
	}, nil
}

// getPrizeList 获取奖品列表
func (l *PointsPrizeListLogic) getPrizeList() ([]*welfare_points_shop.WelfarePointsShop, error) {
	return l.dbUtil.GetAllPrizeList(l.ctx)
}

// filterPrizesByCompliance 根据合规策略过滤奖品
func (l *PointsPrizeListLogic) filterPrizesByCompliance(country int, pointsShop []*welfare_points_shop.WelfarePointsShop, r *http.Request) ([]*welfare_points_shop.WelfarePointsShop, error) {
	complianceService := service.NewComplianceCenterCall(l.ctx, l.svcCtx)
	filteredShop, err := complianceService.GetCompliancePointsShopByKey(country, r, pointsShop)
	if err != nil {
		return nil, err
	}

	return filteredShop, nil
}

// buildPrizeList 构建奖品列表响应
func (l *PointsPrizeListLogic) buildPrizeList(pointsShop []*welfare_points_shop.WelfarePointsShop, lang string) []*types.PointsPrizeInfo {
	if len(pointsShop) == 0 {
		return []*types.PointsPrizeInfo{}
	}

	prizeList := make([]*types.PointsPrizeInfo, 0, len(pointsShop))

	for _, item := range pointsShop {
		if item == nil {
			continue
		}

		prizeInfo := l.buildSinglePrizeInfo(item, lang)
		if prizeInfo != nil {
			prizeList = append(prizeList, prizeInfo)
		}
	}

	return prizeList
}

// buildSinglePrizeInfo 构建单个奖品信息
func (l *PointsPrizeListLogic) buildSinglePrizeInfo(item *welfare_points_shop.WelfarePointsShop, lang string) *types.PointsPrizeInfo {
	// 获取多语言文本
	prizeNameText := l.getLocalizedText(lang, l.nullStringToString(item.PrizeName))
	prizeDescText := l.getLocalizedText(lang, l.nullStringToString(item.PrizeDesc))

	// 获取库存信息
	surplus := l.getPrizeSurplus(item)

	return &types.PointsPrizeInfo{
		Id:             int(item.Id),
		PrizeNameText:  prizeNameText,
		PrizeType:      int(item.PrizeType),
		PrizeSubType:   int(item.PrizeSubType),
		PrizeValue:     item.PrizeValue,
		PrizeDescText:  prizeDescText,
		PrizeUrl:       item.PrizeUrl,
		ExchangePoints: int(item.ExchangePoints),
		Sort:           int(item.Sort),
		Surplus:        surplus,
	}
}

// getPrizeSurplus 获取奖品库存
func (l *PointsPrizeListLogic) getPrizeSurplus(item *welfare_points_shop.WelfarePointsShop) int {
	surplus, err := l.dbUtil.GetRedisCurrentLimit(
		l.svcCtx,
		l.ctx,
		int(item.Id),
		int(item.PrizeSubType),
		int(utils.MustInt64(item.PrizeValue)),
	)
	if err != nil {
		logx.Errorf("getPrizeSurplus failed for prize %d: %v", item.Id, err)
		return 0 // 错误时返回0库存，避免超卖
	}

	// 确保库存不为负数
	if surplus < 0 {
		surplus = 0
	}

	return surplus
}

// nullStringToString 将sql.NullString转换为string
func (l *PointsPrizeListLogic) nullStringToString(ns interface{}) string {
	if ns == nil {
		return ""
	}

	// 根据实际的NullString类型进行处理
	// 这里需要根据你的具体NullString实现来调整
	switch v := ns.(type) {
	case string:
		return v
	default:
		// 如果有其他NullString类型，在这里添加处理
		return ""
	}
}

// getLocalizedText 获取本地化文本
func (l *PointsPrizeListLogic) getLocalizedText(lang, jsonText string) string {
	// 处理空字符串或空白字符串
	if strings.TrimSpace(jsonText) == "" {
		return ""
	}

	// 尝试解析JSON多语言文本
	var textMap map[string]string
	if err := json.Unmarshal([]byte(jsonText), &textMap); err != nil {
		logx.Warnf("getLocalizedText failed to unmarshal JSON, using original text. text=%s, err=%v", jsonText, err)
		return jsonText // 如果不是JSON格式，直接返回原文本
	}

	// 优先返回指定语言的文本
	if localizedText, exists := textMap[lang]; exists && localizedText != "" {
		return localizedText
	}

	// 回退到英语
	if enText, exists := textMap["en"]; exists && enText != "" {
		return enText
	}

	// 如果都没有，返回第一个非空值
	for _, text := range textMap {
		if text != "" {
			return text
		}
	}

	return "" // 所有文本都为空时返回空字符串
}
