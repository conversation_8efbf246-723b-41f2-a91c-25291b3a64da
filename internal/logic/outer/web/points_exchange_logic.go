package web

import (
	"context"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/service"
	prizeservice "gateio_service_welfare_go/internal/service/prize_service"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"strconv"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

const (
	// Redis锁相关常量
	exchangeLockTTL = 5 // 分布式锁超时时间（秒）
)

type PointsExchangeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// ExchangeContext 兑换上下文
type ExchangeContext struct {
	UserInfo       interface{}
	UID            int
	PrizeInfo      interface{}
	PrizeSubType   int
	ExchangePoints int64
	PrizeNum       int
	PrizeId        int64
	ExchangeCycle  int
	ExchangeNum    int
	CouponId       int64
	Source         string
	RecordId       int64
	RecordDetailId int64
	Detail         map[string]string
	LockKey        string
}

// 积分兑换奖品
func NewPointsExchangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PointsExchangeLogic {
	return &PointsExchangeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PointsExchangeLogic) PointsExchange(req *types.PointsExchangeReq, r *http.Request) (resp *types.UniversalNoParamReq, err error) {
	// 检查兑换功能开关
	if err := l.validateExchangeConfig(); err != 0 {
		l.Logger.Infof("PointsExchange exchange is disabled: %v", err)
		return nil, consts.GetErrorMsg(r, err)
	}

	// 获取用户信息
	userInfo := requestools.GetUserInfo(r)
	uid := 0
	if userInfo != nil {
		uid = userInfo.UID
	}

	// 打印最终日志
	defer func() {
		utils.LogRouterData(l.ctx, "PointsExchange", uid, req, resp, err)
	}()

	// 创建兑换上下文
	exchangeCtx := &ExchangeContext{
		UserInfo: userInfo,
		UID:      uid,
		LockKey:  fmt.Sprintf(consts.PointsExchange, uid),
	}

	// 确保分布式锁释放
	defer func() {
		if exchangeCtx.LockKey != "" {
			_, _ = l.svcCtx.Redis.Del(exchangeCtx.LockKey)
		}
	}()

	// 预处理验证
	if err := l.preValidation(exchangeCtx, req, r); err != nil {
		return nil, err
	}

	// 获取分布式锁
	if err := l.acquireLock(exchangeCtx); err != nil {
		return nil, err
	}

	boolCheck, checkCode := service.CheckUserIdentity(l.svcCtx, l.ctx, userInfo)
	if !boolCheck {
		return nil, consts.GetErrorMsg(r, checkCode)
	}

	// 准备兑换数据
	if err := l.prepareExchangeData(exchangeCtx, req, r); err != nil {
		return nil, err
	}

	// 执行兑换逻辑
	if err := l.executeExchange(exchangeCtx, r); err != nil {
		return nil, err
	}

	return &types.UniversalNoParamReq{}, nil
}

// preValidation 预处理验证
func (l *PointsExchangeLogic) preValidation(ctx *ExchangeContext, req *types.PointsExchangeReq, r *http.Request) error {
	// 验证用户登录状态
	if ctx.UserInfo == nil || ctx.UID <= 0 {
		l.Logger.Infof("PointsExchange user not login, uid: %d", ctx.UID)
		return consts.GetErrorMsg(r, consts.ErrUserLogin)
	}

	// 验证请求参数
	if err := l.validateParams(req); err != 0 {
		l.Logger.Infof("PointsExchange invalid params: %v", err)
		return consts.GetErrorMsg(r, err)
	}

	return nil
}

// acquireLock 获取分布式锁
func (l *PointsExchangeLogic) acquireLock(ctx *ExchangeContext) error {
	isLocked, err := l.acquireDistributedLock(ctx.LockKey)
	if err != nil {
		l.Logger.Errorf("PointsExchange failed to acquire lock: %v", err)
		return consts.GetErrorMsg(nil, consts.ErrSystemError)
	}
	if !isLocked {
		l.Logger.Infof("PointsExchange too many attempts, uid: %d", ctx.UID)
		return consts.GetErrorMsg(nil, consts.ManyAttempts)
	}
	return nil
}

// prepareExchangeData 准备兑换数据
func (l *PointsExchangeLogic) prepareExchangeData(ctx *ExchangeContext, req *types.PointsExchangeReq, r *http.Request) error {
	dbUtil := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB)

	// 获取奖品信息
	prizeInfo, err := dbUtil.GetPrizeInfoById(l.ctx, req.Id)
	if err != nil || prizeInfo == nil {
		l.Logger.Warnf("PointsExchange failed to get prize info, id: %d, err: %v", req.Id, err)
		return consts.GetErrorMsg(r, consts.ErrPrizeExpired)
	}
	// 字段太大，忽略
	prizeInfo.PrizeDesc = ""
	// 填充上下文数据
	ctx.PrizeInfo = prizeInfo
	ctx.PrizeSubType = int(prizeInfo.PrizeSubType)
	ctx.ExchangePoints = prizeInfo.ExchangePoints
	ctx.PrizeNum = int(utils.MustInt64(prizeInfo.PrizeValue, 0))
	ctx.PrizeId = int64(req.Id)
	ctx.ExchangeCycle = int(prizeInfo.ExchangeCycle)
	ctx.ExchangeNum = int(prizeInfo.ExchangeNum)
	ctx.CouponId = prizeInfo.PrizeId
	ctx.Source = prizeInfo.Source
	ctx.Detail = map[string]string{
		"decrPointsStatus":          "INIT",
		"sendPrizeStatus":           "INIT",
		"returnIncrPointsStatus":    "INIT",
		"insertPointsRecordsStatus": "INIT",
		"decrCurrentLimitStatus":    "INIT",
	}

	// 验证用户积分
	if err := l.validateUserPoints(ctx.UID, ctx.ExchangePoints); err != 0 {
		l.Logger.Infof("PointsExchange insufficient points, uid: %d, required: %d", ctx.UID, ctx.ExchangePoints)
		return consts.GetErrorMsg(r, err)
	}

	// 风控检查
	if err := l.checkRiskControl(ctx, req, r); err != nil {
		return err
	}

	// 检查库存和频率限制
	if err := l.checkLimits(ctx, req, r); err != nil {
		return err
	}

	return nil
}

// checkRiskControl 风控检查
func (l *PointsExchangeLogic) checkRiskControl(ctx *ExchangeContext, req *types.PointsExchangeReq, r *http.Request) error {
	dataMap := map[string]interface{}{
		"user_id":  ctx.UID,
		"ip":       requestools.GetClientIP(r),
		"const_id": req.ConstID,
		"is_async": 0,
	}

	isRisk, err := service.GetUserRiskData(l.svcCtx, l.ctx, ctx.UID, req.ConstID, consts.RiskEventCodeTaskCenter, dataMap, r)
	if err != nil {
		l.Logger.Errorf("ReceiveTask InnerCheckRisk is err,params is:%d, %s ，err is：%v", ctx.UID, req.ConstID, err)
		return consts.GetErrorMsg(r, consts.ErrExchangeRiskNoticeReject)
	}

	if isRisk {
		return consts.GetErrorMsg(r, consts.ErrExchangeRiskNoticeReject)
	}

	return nil
}

// checkLimits 检查库存和频率限制
func (l *PointsExchangeLogic) checkLimits(ctx *ExchangeContext, req *types.PointsExchangeReq, r *http.Request) error {
	dbUtil := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB)

	// 检查库存
	limit, err := dbUtil.HandleRedisCurrentLimit(l.svcCtx, l.ctx, int(ctx.PrizeId), ctx.PrizeSubType, ctx.PrizeNum, 1)
	if err != nil {
		l.Logger.Errorf("PointsExchange failed to check inventory: %v", err)
		return consts.GetErrorMsg(r, consts.ErrSystemError)
	}
	if !limit {
		l.Logger.Infof("PointsExchange insufficient inventory, prizeId: %d", ctx.PrizeId)
		return consts.GetErrorMsg(r, consts.ErrThisRewardIsLimitedInNumber)
	}

	// 检查兑换频率限制
	stm := time.Now().AddDate(0, 0, -ctx.ExchangeCycle).Format(utils.DateTimeFormat)
	etm := time.Now().Format(utils.DateTimeFormat)
	records, err := dbUtil.GetExchangeRecordsByTime(l.ctx, ctx.UID, req.Id, stm, etm)
	if err != nil {
		l.Logger.Errorf("PointsExchange failed to get exchange records: %v", err)
		l.rollbackInventory(ctx)
		return consts.GetErrorMsg(r, consts.ErrSystemError)
	}

	if len(records) >= ctx.ExchangeNum {
		l.Logger.Infof("PointsExchange frequency limit reached, uid: %d, prizeId: %d, limit: %d", ctx.UID, req.Id, ctx.ExchangeNum)
		l.rollbackInventory(ctx)
		return consts.GetErrorMsg(r, consts.ErrWelfareExchangePrizeLimit)
	}

	return nil
}

// executeExchange 执行兑换逻辑
func (l *PointsExchangeLogic) executeExchange(ctx *ExchangeContext, r *http.Request) error {
	// 创建兑换记录
	if err := l.createExchangeRecord(ctx, r); err != nil {
		return err
	}

	// 发放奖品
	if err := l.sendPrize(ctx, r); err != nil {
		return err
	}

	// 后续处理
	if err := l.postProcessing(ctx, r); err != nil {
		return err
	}

	// 更新最终状态
	return l.updateFinalStatus(ctx, r)
}

// createExchangeRecord 创建兑换记录
func (l *PointsExchangeLogic) createExchangeRecord(ctx *ExchangeContext, r *http.Request) error {
	dbUtil := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB)

	// 开启事务
	tx := dbUtil.DB.Begin()
	defer tx.Rollback()

	// 插入兑换记录
	recordId, err := dao.NewWelfareMysqlTxDBUtil(tx).InsertExchangeRecord(l.ctx, int64(ctx.UID), ctx.PrizeId, ctx.ExchangePoints,
		consts.RecordStatusInit, consts.RecordPrizeSourceExchange, int64(ctx.PrizeSubType), int64(ctx.PrizeNum), 0)
	if err != nil || recordId <= 0 {
		l.rollbackInventory(ctx)
		return consts.GetErrorMsg(r, consts.ErrWelfareExchangePrizeFailed)
	}
	ctx.RecordId = recordId

	// 插入奖品详情记录
	now := time.Now()
	recordDetailId, err := dao.NewWelfareMysqlTxDBUtil(tx).InsertUserPrizeDetail(l.ctx, int64(ctx.UID), recordId,
		ctx.PrizeInfo, ctx.Detail, now, now)
	if err != nil || recordDetailId <= 0 {
		l.rollbackInventory(ctx)
		return consts.GetErrorMsg(r, consts.ErrWelfareExchangePrizeFailed)
	}
	ctx.RecordDetailId = recordDetailId

	// 扣减用户积分
	if err := dao.NewWelfareMysqlTxDBUtil(tx).DecrUserPoints(l.ctx, int64(ctx.UID), ctx.ExchangePoints); err != nil {
		l.rollbackInventory(ctx)
		return consts.GetErrorMsg(r, consts.ErrWelfareExchangePrizeFailed)
	}

	tx.Commit()
	dbUtil.DelRedisUserPoints(l.svcCtx, int64(ctx.UID))
	ctx.Detail["decrPointsStatus"] = "SUCCESS"
	return nil
}

// sendPrize 发放奖品
func (l *PointsExchangeLogic) sendPrize(ctx *ExchangeContext, r *http.Request) error {
	// 请求卡券发放奖励
	sendCouponFormatResp := service.SendCouponPrice(l.ctx, int64(ctx.UID), ctx.CouponId,
		strconv.FormatInt(ctx.RecordId, 10), strconv.FormatInt(int64(ctx.PrizeNum), 10), ctx.Source)

	if sendCouponFormatResp.Code == consts.Success {
		ctx.Detail["sendPrizeStatus"] = "SUCCESS"
		return nil
	}

	// 发放失败处理
	ctx.Detail["sendPrizeStatus"] = "FAILED"

	// 如果需要返还积分
	if sendCouponFormatResp.IsReturn == 1 {
		if err := l.returnPoints(ctx); err != nil {
			ctx.Detail["returnIncrPointsStatus"] = "FAILED"
		} else {
			ctx.Detail["returnIncrPointsStatus"] = "SUCCESS"
		}
		l.rollbackInventory(ctx)
	}

	// 更新兑换状态为失败
	if err := l.updateExchangeStatus(ctx.UID, ctx.RecordId, ctx.RecordDetailId, ctx.PrizeId, consts.RecordStatusFailed, ctx.Detail, r); err != nil {
		return err
	}

	return consts.GetErrorMsg(r, consts.ErrWelfareExchangePrizeFailed)
}

// returnPoints 返还积分
func (l *PointsExchangeLogic) returnPoints(ctx *ExchangeContext) error {
	dbUtil := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB)
	_, err := dbUtil.IncrUserPoints(l.ctx, ctx.UID, ctx.ExchangePoints)
	dbUtil.DelRedisUserPoints(l.svcCtx, int64(ctx.UID))
	return err
}

// rollbackInventory 回滚库存
func (l *PointsExchangeLogic) rollbackInventory(ctx *ExchangeContext) {
	dbUtil := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB)
	_, _ = dbUtil.IncrRedisCurrentLimit(l.svcCtx, l.ctx, int(ctx.PrizeId), ctx.PrizeSubType, ctx.PrizeNum, 1)
}

// postProcessing 后续处理
func (l *PointsExchangeLogic) postProcessing(ctx *ExchangeContext, r *http.Request) error {
	dbUtil := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB)

	// 减少库存
	err := dbUtil.DecrCurrentLimitByPrizeId(l.ctx, int(ctx.PrizeId), ctx.PrizeSubType, ctx.PrizeNum, 1)
	if err != nil {
		ctx.Detail["decrCurrentLimitStatus"] = "FAILED"
	} else {
		ctx.Detail["decrCurrentLimitStatus"] = "SUCCESS"
	}

	// 增加积分流水
	_, err = dbUtil.InsertPointsRecords(l.ctx, ctx.UID, consts.PointsRecordActionDecr, ctx.ExchangePoints, ctx.PrizeInfo,
		consts.UserTaskTypePointsExchange, 0, 0, int(ctx.RecordId))
	if err != nil {
		l.Logger.Errorf("积分兑换成功，但是插入积分流水失败，uid:%d, prizeId:%d", ctx.UID, ctx.PrizeId)
		ctx.Detail["insertPointsRecordsStatus"] = "FAILED"
	} else {
		ctx.Detail["insertPointsRecordsStatus"] = "SUCCESS"
	}

	return nil
}

// updateFinalStatus 更新最终状态
func (l *PointsExchangeLogic) updateFinalStatus(ctx *ExchangeContext, r *http.Request) error {
	return l.updateExchangeStatus(ctx.UID, ctx.RecordId, ctx.RecordDetailId, ctx.PrizeId, consts.RecordStatusSuccess, ctx.Detail, r)
}

// updateExchangeStatus 更新兑换状态
func (l *PointsExchangeLogic) updateExchangeStatus(uid int, recordId, recordDetailId, prizeId int64, status string, detail map[string]string, r *http.Request) error {
	dbUtil := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB)

	// 开启事务
	tx2 := dbUtil.DB.Begin()
	defer tx2.Rollback()

	prizeService := prizeservice.NewPrizeService(l.ctx, l.svcCtx)
	updateExchangeBool := prizeService.UpdateExchangeStatusById(tx2, uid, int(recordId), int(recordDetailId), status, detail, int(prizeId), consts.RecordPrizeSourceExchange, 0, "")
	if !updateExchangeBool {
		return consts.GetErrorMsg(r, consts.ErrWelfareExchangePrizeFailed)
	}
	tx2.Commit()
	return nil
}

// validateExchangeConfig 验证兑换功能配置
func (l *PointsExchangeLogic) validateExchangeConfig() int {
	boolSwitch, boolErr := service.GetSystemSwitchByKey(l.svcCtx, l.ctx, consts.ExchangePrize)
	if boolErr != nil {
		return consts.ErrSystemError
	}

	if boolSwitch == false {
		return consts.ErrSuspendedForMaintenance
	}

	return 0
}

// validateParams 验证请求参数
func (l *PointsExchangeLogic) validateParams(req *types.PointsExchangeReq) int {
	if req.Id <= 0 {
		return consts.ErrInvalidParam
	}
	if req.ConstID == "" {
		return consts.ErrInvalidParam
	}
	return 0
}

// acquireDistributedLock 获取分布式锁
func (l *PointsExchangeLogic) acquireDistributedLock(lockKey string) (bool, error) {
	isLocked, err := l.svcCtx.Redis.SetnxExCtx(l.ctx, lockKey, "1", exchangeLockTTL)
	if err != nil {
		l.Logger.Warnf("Failed to set lock %s: %v", lockKey, err)
		return false, err
	}
	return isLocked, nil
}

// validateUserPoints 验证用户积分是否足够
func (l *PointsExchangeLogic) validateUserPoints(uid int, requiredPoints int64) int {
	// 获取用户积分
	myPoints, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetPointsByUid(l.svcCtx, l.ctx, int64(uid))
	if err != nil {
		l.Logger.Errorf("Failed to get user points, uid: %d, err: %v", uid, err)
		return consts.ErrDbError
	}

	// 检查积分是否足够
	if myPoints < requiredPoints {
		l.Logger.Infof("User points insufficient, uid: %d, current: %d, required: %d", uid, myPoints, requiredPoints)
		return consts.ErrWelfareExchangePrizeInsufficientPoints
	}

	return 0
}
