package web

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"context"
	"crypto/md5"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service"
	kafkaService "gateio_service_welfare_go/internal/service/kafka_producer"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"time"
)

type ReceiveTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 领取任务
func NewReceiveTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReceiveTaskLogic {
	return &ReceiveTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReceiveTaskLogic) ReceiveTask(req *types.ReveiveTaskReq, r *http.Request) (resp *types.UniversalNoParamReq, err error) {

	userInfo := requestools.GetUserInfo(r)
	uid := 0
	if userInfo != nil {
		uid = userInfo.UID
	}

	defer func() {
		utils.LogRouterData(l.ctx, "ReceiveTask", uid, req, resp, err)
	}()

	if uid <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	salt := "gateio_welfare_receiveTask"
	tokenStr := fmt.Sprintf("%s_%d_%d", salt, uid, req.TaskId)
	hash := md5.Sum([]byte(tokenStr))
	md5Str := hex.EncodeToString(hash[:])

	if md5Str != req.WelfareToken {
		return nil, consts.GetErrorMsg(r, consts.ErrWelfareInvalidActivity)
	}

	boolCheck, checkCode := service.CheckUserIdentity(l.svcCtx, l.ctx, userInfo)
	if !boolCheck {
		return nil, consts.GetErrorMsg(r, checkCode)
	}

	userTaskCfg, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgListById(l.ctx, req.TaskId)
	if err != nil {
		return nil, consts.GetErrorMsg(r, consts.ErrRecordNotExist)
	}

	if userTaskCfg.OnlineStatus == consts.StatusOffline {
		return nil, consts.GetErrorMsg(r, consts.ErrWelfareInvalidActivity)
	}

	if userTaskCfg.Status == consts.EffectiveStatusReview {
		cfg := &welfare_task_cfgs.WelfareTaskCfgs{}
		_ = json.Unmarshal([]byte(userTaskCfg.ApprovedSnapshot.String), cfg)
		if cfg.Id <= 0 {
			return nil, consts.GetErrorMsg(r, consts.ErrWelfareInvalidActivity)
		}

		userTaskCfg = cfg
	}

	taskCenterId := userTaskCfg.TaskId
	taskCenterInfo, err := service.GetTaskListByTaskIds(l.svcCtx, l.ctx, 0, []int64{taskCenterId})
	//获取任务系统失败
	if err != nil {
		return nil, err
	}

	//任务ID不存在
	if _, ok := taskCenterInfo[taskCenterId]; !ok {
		return nil, consts.GetErrorMsg(r, consts.ErrWelfareInvalidActivity)
	}

	userTaskInfo, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetUserTaskByUIdAndTaskCenterId(l.ctx, int64(uid), taskCenterId)
	if err != nil {
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}
	//已经存在相同任务的处理中任务
	if userTaskInfo.Id > 0 && userTaskInfo.Status == consts.StatusInProcess {
		return nil, consts.GetErrorMsg(r, consts.ErrReveiveTaskNotInConditionMsg)
	}

	//先检查风控
	dataMap := make(map[string]interface{})
	dataMap["user_id"] = uid
	dataMap["ip"] = requestools.GetClientIP(r)
	dataMap["const_id"] = req.ConstID
	dataMap["is_async"] = 0
	dataMap["action"] = "receive_task"
	dataMap["task_id"] = taskCenterId

	isRisk, err := service.GetUserRiskData(l.svcCtx, l.ctx, uid, req.ConstID, consts.RiskEventCodeTaskCenter, dataMap, r)
	if err != nil {
		logx.Errorf("ReceiveTask InnerCheckRisk is failed,params is:%d, %s ，err is：%v", uid, req.ConstID, err)
		return nil, consts.GetErrorMsg(r, consts.ErrReveiveTaskRiskNoticeReject)
	}
	//命中风控
	if isRisk {
		//命中风控直接返回
		logx.Infof("ReceiveTask user is risk, uid is:%d, constId:%s", uid, req.ConstID)
		return nil, consts.GetErrorMsg(r, consts.ErrReveiveTaskRiskNoticeReject)
	}

	//开启事务
	tx := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).DB.Begin()
	defer tx.Rollback()

	//去除大字段的配置信息，避免占用过多表空间
	memo := userTaskCfg
	if userTaskCfg.Type == consts.UserTaskTypeNewbieGuide || userTaskCfg.Type == consts.UserTaskTypeNewbieRegister {
		memo.Reward = sql.NullString{Valid: false}
		memo.ApprovedSnapshot = sql.NullString{Valid: false}
	} else {
		memo.Reward = sql.NullString{Valid: false}
		memo.ApprovedSnapshot = sql.NullString{Valid: false}
		memo.ExtraTaskInfo = sql.NullString{Valid: false}
	}
	memoByte, _ := json.Marshal(memo)
	data := &welfare_user_tasks.WelfareUserTasks{
		Uid:          int64(uid),
		Type:         userTaskCfg.Type,
		TaskId:       userTaskCfg.Id,
		TaskType:     userTaskCfg.TaskType,
		Status:       consts.StatusInProcess,
		Memo:         string(memoByte),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		Stage:        0,
		TaskCenterId: taskCenterId,
	}
	taskLastInsertId, err := dao.NewWelfareMysqlTxDBUtil(tx).InsertUserTask(l.ctx, data)
	if err != nil {
		return nil, err
	}

	if taskLastInsertId <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}

	// 把业务的任务上报给任务系统
	getTaskTime := time.Now().Unix()
	startTime := int64(0)
	endTime := int64(0)
	if userTaskCfg.Type == consts.UserTaskTypeNewbieGuide || userTaskCfg.Type == consts.UserTaskTypeNewbieRegister {
		startTime = getTaskTime
		//查询用户快照
		registerInfo, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetWelfareUserRegisterInfo(l.svcCtx, l.ctx, int64(uid))
		if err != nil {
			l.Logger.Errorf("ReceiveTask GetWelfareUserRegisterInfo is err,params is:%d ，err is：%v", uid, err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		endTime = registerInfo.NewbieEndTime
	}
	kafkaProducer, err := kafkaService.NewKafkaProducer(l.svcCtx.KafkaConf)
	defer kafkaProducer.Close()
	if err != nil {
		logx.Errorf("ReceiveTask failed, step: kafka.NewKafkaProducer, UserId : %d, BusinessId: %d, err: %v", uid, taskCenterId, err)
	} else {

		//提交事务
		tx.Commit()

		err = kafkaProducer.TaskRecordUserReceiveTask(l.ctx, int64(uid), taskCenterId, consts.WelfareTaskBusinessType, taskLastInsertId, getTaskTime, startTime, endTime, 0)
		if err != nil {
			logx.Errorf("ReceiveTask Producer.TaskRecordReceiveProducer error：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d, reviceTime:%d, startTime:%d, endTime:%d，err:%v", uid, taskLastInsertId, taskCenterId, consts.WelfareTaskBusinessType, getTaskTime, startTime, endTime, err)
		} else {
			logx.Infof("ReceiveTask Producer.TaskRecordReceiveProducer succ：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d, reviceTime:%d, startTime:%d, endTime:%d", uid, taskLastInsertId, taskCenterId, consts.WelfareTaskBusinessType, getTaskTime, startTime, endTime)
		}
	}
	lang := requestools.GetUserLanguage(r)
	utils.DelCommonRedisKey(l.svcCtx, int64(uid), int(userTaskCfg.Type), lang)
	//删除任务接口缓存
	utils.DelayDelRedisKey(l.svcCtx, fmt.Sprintf(consts.TaskCenterUserTaskInfoKey, uid, taskCenterId), 0, 3)
	//删除任务接口缓存
	utils.DelayDelRedisKey(l.svcCtx, fmt.Sprintf(consts.TaskCenterTaskInfoKey, taskCenterId), 0, 3)

	resp = &types.UniversalNoParamReq{}
	return resp, nil
}
