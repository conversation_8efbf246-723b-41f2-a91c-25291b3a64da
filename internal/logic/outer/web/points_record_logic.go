package web

import (
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type PointsRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 分页查询积分记录
func NewPointsRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PointsRecordLogic {
	return &PointsRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PointsRecordLogic) PointsRecord(req *types.PointsRecordReq, r *http.Request) (resp *types.PointsRecordResp, err error) {
	userInfo := requestools.GetUserInfo(r)
	if userInfo == nil || userInfo.UID <= 0 || userInfo.Type == 5 {
		l.Logger.Info("PrizeRecord user not login")
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	resp = &types.PointsRecordResp{}
	startTime := ""
	if req.Day > 0 {
		startTime = time.Now().AddDate(0, 0, -req.Day).Format(time.DateTime)
	}
	list := []*types.PointRecord{}
	//类型 全部：0，1:挑战任务 2:福利任务 3:邀请任务 4:新人任务(包含新客入门，进阶任务 4,11,12) 5:签到任务 6:tg签到任务 7:限时任务(包含老客限时任务,7,14) 8:游戏中心 9:游戏中心-邀请 10:新客注册任务  11:每日任务 12:积分过期 13:积分升级  99 积分兑换

	fromId := int64(0)
	toId := int64(0)
	//获取用户签到记录
	records, countVal, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryPointRecordsByPage(l.ctx, userInfo.UID, req.SourceType, startTime, "", req.Sort, req.Page, req.PerPage)
	if err != nil {
		l.Logger.Errorf("PointsRecord QueryExchangeRecordsByPage is err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	list = make([]*types.PointRecord, len(records))
	for i, record := range records {
		if i == 0 {
			fromId = record.Id
		}
		if i == len(records)-1 {
			toId = record.Id
		}
		//避免出现type为0的情况
		typeVal := record.Type
		if record.Type == 0 && record.Action == dao.ActionDecr {
			typeVal = consts.UserTaskTypePointsExchange
		}
		if record.Type == consts.ExSourceTypeNewUser || record.Type == consts.ExSourceTypeOnboarding || record.Type == consts.ExSourceTypeAdvanced {
			typeVal = dao.ParamSourceTypeNewUser
		}
		if record.Type == consts.ExSourceTypeDaily {
			typeVal = dao.ParamSourceTypeDaily
		}
		if record.Type == consts.ExSourceTypeLimitedTimeEvent {
			typeVal = dao.ParamSourceTypeLimitedTimeEven
		}
		if record.Type == consts.ExSourceTypePointsExpire {
			typeVal = dao.ParamSourceTypePointsExpire
		}
		if record.Type == consts.ExSourceTypePointsClearBeforeExpansion || record.Type == consts.ExSourceTypePointsIncreaseAfterExpansion {
			typeVal = dao.ParamSourceTypePointsUpgrade
		}
		list[i] = &types.PointRecord{
			ID:          record.Id,
			Action:      record.Action,
			Points:      int(record.Points),
			SourceType:  int(typeVal),
			ReceiveTime: record.CreatedAt.Unix(),
		}
	}
	lastPage := 0
	if countVal%int64(req.PerPage) > 0 {
		lastPage = int(countVal/int64(req.PerPage)) + 1
	} else {
		lastPage = int(countVal / int64(req.PerPage))
	}
	resp.CurrentPage = req.Page
	resp.From = fromId
	resp.LastPage = lastPage
	resp.PerPage = req.PerPage
	resp.To = toId
	resp.Total = countVal
	resp.List = list
	return
}
