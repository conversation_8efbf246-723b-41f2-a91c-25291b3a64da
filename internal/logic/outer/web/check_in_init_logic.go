package web

import (
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"context"
	"encoding/json"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type CheckInInitLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取签到任务初始化信息
func NewCheckInInitLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckInInitLogic {
	return &CheckInInitLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CheckInInitLogic) CheckInInit(r *http.Request) (resp *types.CheckInInitResp, err error) {
	userInfo := requestools.GetUserInfo(r)
	resp = &types.CheckInInitResp{}
	uid := 0
	if userInfo != nil {
		uid = userInfo.UID
	}
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		l.Logger.Info("CheckInInit resp: ", string(respJson), " err: ", err, "  uid is:", uid)
	}()
	day := utils.GetYmdDay(0)
	//获取签到任务信息
	taskList := dao.GetCheckInTasks()
	if len(taskList) == 0 {
		l.Logger.Infof("CheckInInit QueryCheckInList len(taskList) is 0")
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	//正序排序 按照天数周期排序
	//sort.Slice(taskList, func(i, j int) bool {
	//	return taskList[i].DayNum < taskList[j].DayNum
	//})
	if userInfo == nil || uid <= 0 {
		list := FormatCheckInTaskInfo(taskList)
		resp.List = list
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	//获取用户最后一次签到信息
	lastCheckIn, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetLastCheckIn(l.ctx, userInfo.UID, consts.CheckInStatusFailed)
	if err != nil {
		l.Logger.Infof("CheckInInit GetLastCheckIn is err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	yesterday := utils.GetYmdDay(time.Now().AddDate(0, 0, -1).Unix())
	//当完成一个签到周期或者没有签到过或者签到中断过从第一天开始签到
	if lastCheckIn == nil || (lastCheckIn.IsCycleLastDay == 1 && int(lastCheckIn.Day) == yesterday) || lastCheckIn.Day == 0 ||
		int(lastCheckIn.Day) < yesterday {
		list := FormatCheckInTaskInfo(taskList)
		resp.List = list
		return
	}
	//找出最后一次签到是第几天的
	lastCheckInDay := 0
	for _, task := range taskList {
		if int64(task.WeekDay) == lastCheckIn.CheckInTaskId {
			lastCheckInDay = task.WeekDay
			break
		}
	}
	list := make([]*types.CheckInTaskInfo, len(taskList))
	for i, task := range taskList {
		isAttendance := 0
		//最后一次签到之前对应的签到任务都应该是签到完成
		if lastCheckIn.CheckInTaskId >= int64(task.WeekDay) {
			isAttendance = 1
		}
		isToday := 0
		//当下标等于最后一次签到的天数时，对应的就是当天签到 //判断对后一次签到是否是昨天签到
		if i == lastCheckInDay && int(lastCheckIn.Day) == yesterday {
			isToday = 1
		}
		//判断最后一次签到是否是今天
		if lastCheckIn.CheckInTaskId == int64(task.WeekDay) && int(lastCheckIn.Day) == day {
			isToday = 1
		}
		list[i] = &types.CheckInTaskInfo{
			IsAttendance: isAttendance,
			PrizeType:    task.PrizeType,
			PrizeTypeNum: task.PrizeTypeNum,
			DayNum:       task.WeekDay,
			IsToday:      isToday,
		}
	}
	resp.List = list

	return
}

func FormatCheckInTaskInfo(taskList []*dao.CheckInConfig) []*types.CheckInTaskInfo {
	list := make([]*types.CheckInTaskInfo, len(taskList))
	for i, task := range taskList {
		isToday := 0
		if i == 0 {
			isToday = 1
		}
		list[i] = &types.CheckInTaskInfo{
			IsAttendance: 0,
			PrizeType:    task.PrizeType,
			PrizeTypeNum: task.PrizeTypeNum,
			DayNum:       task.WeekDay,
			IsToday:      isToday,
		}
	}
	return list
}
