package web

import (
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetUserIdentityLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// web-获取用户身份
func NewGetUserIdentityLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserIdentityLogic {
	return &GetUserIdentityLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserIdentityLogic) GetUserIdentity(req *types.UniversalNoParamReq, r *http.Request) (resp *types.GetUserIdentityResp, err error) {
	resp = &types.GetUserIdentityResp{}
	userInfo := requestools.GetUserInfo(r)
	uid := 0
	if userInfo != nil {
		uid = userInfo.UID
	}
	if userInfo == nil || uid <= 0 {
		return resp, nil
	}
	//打印最终日志
	defer func() {
		utils.LogRouterData(l.ctx, "GetUserIdentity", uid, req, resp, err)
	}()

	mmUidList, err := dao.NewFeeSettingMysqlDBUtil(l.svcCtx.FeeSettingDB).GetMmUidAry(l.svcCtx, l.ctx)
	if err != nil {
		l.Logger.Infof("GetUserIdentity GetMmUidAry is err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	if utils.ContainsArray(mmUidList, int64(uid)) {
		resp.IsMarketplace = 1
	}
	if userInfo.Verified == consts.KycLvCompany {
		resp.IsEnterprise = 1
	}
	if userInfo.IsSub == 1 {
		resp.IsSub = 1
	}
	resp.AgencyType = userInfo.AgencyType

	//dataMap := make(map[string]interface{})
	//dataMap["user_id"] = userInfo.UID
	//dataMap["ip"] = requestools.GetClientIP(r)
	//dataMap["const_id"] = req.ConstID
	//dataMap["is_async"] = 0
	//isRisk, err := service.GetUserRiskData(l.svcCtx, l.ctx, userInfo.UID, req.ConstID, consts.RiskEventCodeIndexPageCheck, dataMap, r)
	//if err != nil {
	//	logx.Errorf("GetBeginnerTasks InnerCheckRisk is err,params is:%d, %s ，err is：%v", userInfo.UID, req.ConstID, err)
	//	resp.IsRiskNewbie = 1
	//}
	////命中风控
	//if isRisk {
	//	resp.IsRiskNewbie = 1
	//}
	//查询用户快照
	registerInfo, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetWelfareUserRegisterInfo(l.svcCtx, l.ctx, int64(uid))
	if err != nil {
		l.Logger.Errorf("GetBeginnerTasks GetWelfareUserRegisterInfo is err,params is:%d ，err is：%v", uid, err)
	}
	// 用户区域
	region, err := service.GetUserBelongRegion(l.svcCtx, l.ctx, int64(uid), r, utils.MustInt64(userInfo.Country, 0), utils.MustInt64(userInfo.ResidenceCountryID, 0), registerInfo)
	if err != nil {
		l.Logger.Errorf("UserRegisterDeal GetUserBelongRegion is error ，UserRegisterDeal uid is:%d ,Params is：%s, err is:%v", uid, registerInfo, err)
	}
	resp.Region = region

	t, err := service.GetWelfareConfig(l.svcCtx, l.ctx, consts.WelfareRefactorTime)
	if err != nil {
		l.Logger.Infof("GetUserIdentity GetWelfareConfig is err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	timestampT, err := utils.TransformDateStringToTime(t)
	if err != nil {
		l.Logger.Infof("GetUserIdentity TransformDateStringToTime is err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}
	l.Logger.Infof("GetUserIdentity registerInfo is registerInfo: %v", registerInfo)
	// todo 上线后存在15天后逻辑需要删除
	//先判断老福利中心的新客用户和老福利中心的老客，如果命中老福利中心新客就跳到老福利中心新客任务，如果命中老客就跳到新福利中心的老客
	// 老福利中心新客
	if userInfo.RegTimest < timestampT.Unix() {
		var endTime int64
		var region2 string
		// 快照未生成(历史注册用户没快照)
		if registerInfo.Id <= 0 {
			region2 = region
		} else {
			region2 = registerInfo.Region
		}

		// sa地区：注册时间未超过7天
		if region2 == consts.NewbieTaskRegionSa {
			endTime = userInfo.RegTimest + int64(7*24*60*60)
		} else {
			// 	其他地区：注册时间未超过15天
			endTime = userInfo.RegTimest + int64(15*24*60*60)
		}
		if time.Now().Unix() < endTime && resp.IsBlack != 1 {
			resp.UserIdentity = consts.CustomerTypeOldNewbie
			resp.NewUserEndTime = endTime
		} else {
			// 新福利中心老客
			resp.UserIdentity = consts.CustomerTypeVeteran
		}
		return
	}

	// 快照未生成
	if registerInfo.Id <= 0 {
		resp.UserIdentity = consts.CustomerTypeNewbie
		newUserEndTime, _ := l.calculateNewUserEndTime(region, userInfo.RegTimest)
		resp.NewUserEndTime = newUserEndTime

	} else { // 快照已生成
		resp.UserIdentity = consts.CustomerTypeNewbie

		// NewbieEndTime未写入（非黑名单）
		if registerInfo.NewbieEndTime == 0 && registerInfo.IsBlack != 1 {
			newUserEndTime, _ := l.calculateNewUserEndTime(region, userInfo.RegTimest)
			resp.NewUserEndTime = newUserEndTime
		} else {
			resp.NewUserEndTime = registerInfo.NewbieEndTime
		}

		resp.IsVisitNewbie = int(registerInfo.IsVisitNewbie)
		resp.IsPopUp = int(registerInfo.IsPopUp)
		resp.IsBlack = int(registerInfo.IsBlack)

		// 当前大于新客结束时间：新福利中心老客
		if time.Now().Unix() > resp.NewUserEndTime {
			resp.UserIdentity = consts.CustomerTypeVeteran
		}
	}

	return
}
func (l *GetUserIdentityLogic) calculateNewUserEndTime(region string, regTime int64) (int64, error) {
	regionCfgInfo, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetRegionCfgInfo(l.ctx, region)
	if err != nil {
		l.Logger.Errorf("GetRegionCfgInfo failed, region: %s, err: %v", region, err)
		return 0, err
	}

	if regionCfgInfo == nil || regionCfgInfo.DayNum <= 0 {
		l.Logger.Infof("regionCfgInfo invalid for uid: %d, region: %s", regTime, region)
		return 0, nil
	}

	return time.Unix(regTime, 0).AddDate(0, 0, int(regionCfgInfo.DayNum)).Unix(), nil
}
