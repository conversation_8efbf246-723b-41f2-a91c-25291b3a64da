package web

import (
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type UploadPopUpLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 上报弹窗
func NewUploadPopUpLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadPopUpLogic {
	return &UploadPopUpLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UploadPopUpLogic) UploadPopUp(r *http.Request) (resp *types.UniversalNoParamReq, err error) {

	userInfo := requestools.GetUserInfo(r)
	uid := 0
	if userInfo != nil {
		uid = userInfo.UID
	}
	if uid <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	//查询用户快照
	registerInfo, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetWelfareUserRegisterInfo(l.svcCtx, l.ctx, int64(uid))
	if err != nil {
		logx.Errorf("UploadPopUp GetWelfareUserRegisterInfo is err,params is:%d ，err is：%v", uid, err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	// 已标记为弹过窗
	if registerInfo.IsPopUp == consts.IsPopUpYes {
		return
	}
	updateMap := map[string]interface{}{}
	updateMap["is_pop_up"] = consts.IsPopUpYes
	err = dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).UpdateUserRegisterInfo(l.svcCtx, l.ctx, registerInfo.Uid, updateMap)
	if err != nil {
		logx.Errorf("UploadPopUp UpdateUserRegisterInfo is error ，Params is：%d, %v, err is:%v", registerInfo.Uid, updateMap, err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	return
}
