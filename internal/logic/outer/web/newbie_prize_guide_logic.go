package web

import (
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/utils"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type NewbiePrizeGuideLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取新客奖励信息
func NewNewbiePrizeGuideLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NewbiePrizeGuideLogic {
	return &NewbiePrizeGuideLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *NewbiePrizeGuideLogic) NewbiePrizeGuide(req *types.UniversalNoParamReq, r *http.Request) (resp *types.NewbiePrizeGuideResp, err error) {
	resp = &types.NewbiePrizeGuideResp{}
	userInfo := requestools.GetUserInfo(r)
	region := ""
	uid := 0
	//打印最终日志
	defer func() {
		utils.LogRouterData(l.ctx, "NewbiePrizeGuide", uid, req, resp, err)
	}()
	if userInfo == nil || userInfo.UID <= 0 {
		regionKey, err := service.GetUnLoginUserBelongRegion(l.svcCtx, l.ctx, r)
		if err != nil {
			l.Logger.Errorf("NewbiePrizeGuide GetUnLoginUserBelongRegion is error: %v", err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		region = regionKey
		//赋值描述中获取的最大奖励
		resp.PrizeNum = consts.GetRegionMaxDescAllReward()[regionKey]
		l.Logger.Infof("NewbiePrizeGuide user not login, regionKey:%v , PrizeNum is: %s", regionKey, resp.PrizeNum)
	} else {
		uid = userInfo.UID
		//查询用户快照
		registerInfo, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetWelfareUserRegisterInfo(l.svcCtx, l.ctx, int64(userInfo.UID))
		if err != nil {
			l.Logger.Errorf("NewbiePrizeGuide GetWelfareUserRegisterInfo is err,params is:%d ，err is：%v", userInfo.UID, err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		if registerInfo == nil || registerInfo.Region == "" {
			//先获取用户区域，再做其他处理
			region, err = service.GetUserBelongRegion(l.svcCtx, l.ctx, int64(userInfo.UID), r, 0, 0, nil)
			if err != nil {
				l.Logger.Errorf("NewbiePrizeGuide GetUserBelongRegion is error ，UserRegisterDeal uid is:%d , err is:%v", userInfo.UID, err)
				return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
			}
		} else {
			region = registerInfo.Region
		}
		//赋值描述中获取的最大奖励
		resp.PrizeNum = consts.GetRegionMaxDescAllReward()[region]
		l.Logger.Infof("NewbiePrizeGuide user login, regionKey:%v , PrizeNum is: %s", region, resp.PrizeNum)
	}
	//根据地区获取用户的新客时间
	regionCfgInfo, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetRegionCfgInfo(l.ctx, region)
	if err != nil {
		l.Logger.Errorf("NewbiePrizeGuide GetRegionCfgInfo is failed , uid is:%d ,Params is：%s, err is:%v", uid, region, err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	resp.NewbieDays = int(regionCfgInfo.DayNum)
	return
}
