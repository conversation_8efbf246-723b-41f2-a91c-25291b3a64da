package web

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/constants"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	taskService "bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"sort"
	"strconv"
	"time"
)

type GetBeginnerTasksLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 查询新客入门任务信息
func NewGetBeginnerTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBeginnerTasksLogic {
	return &GetBeginnerTasksLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetBeginnerTasksLogic) GetBeginnerTasks(req *types.GetBeginnerTasksReq, r *http.Request) (resp *types.GetBeginnerTasksResp, err error) {
	uid := 0
	//打印最终日志
	defer func() {
		utils.LogRouterData(l.ctx, "GetBeginnerTasks", uid, req, resp, err)
	}()

	var respData *types.GetBeginnerTasksResp
	resp = &types.GetBeginnerTasksResp{
		BeginnerTasks: []*types.BeginnerTask{},
	}
	userInfo := requestools.GetUserInfo(r)
	lang := requestools.GetUserLanguage(r)
	//获取请求来源是否是app
	isApp := 0
	if utils.ContainsArray([]int{constants.DeviceTypeAndroid, constants.DeviceTypeIOS}, requestools.GetDeviceType(r)) {
		isApp = 1
	}
	if userInfo != nil {
		uid = userInfo.UID
	}
	//接入缓存提高查询速度
	redisKey := fmt.Sprintf(consts.WelfareBeginnerUserTasksKey, uid, lang, isApp)
	redisVal, _ := l.svcCtx.Redis.Get(redisKey)
	if redisVal != "" {
		_ = json.Unmarshal([]byte(redisVal), &respData)
	}
	//判断当最大奖励描述字段存在时证明时有效值 缓存中存在则直接返回
	if respData != nil {
		l.Logger.Infof("GetBeginnerTasks get list from cache ，uid:%d", uid)
		resp = respData
		//更新用户新客倒计时信息
		if userInfo != nil && userInfo.UID > 0 {
			registerInfo, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetWelfareUserRegisterInfo(l.svcCtx, l.ctx, int64(userInfo.UID))
			if err != nil {
				l.Logger.Errorf("GetBeginnerTasks GetWelfareUserRegisterInfo is err,params is:%d ，err is：%v", userInfo.UID, err)
				return nil, consts.GetErrorMsg(r, consts.ErrDbError)
			}
			resp.NewbieEndTime = registerInfo.NewbieEndTime
		}
		return
	}
	l.Logger.Infof("GetBeginnerTasks get list from db ，uid:%d", uid)
	//非登陆状态根据ip查询任务列表
	if userInfo == nil || userInfo.UID == 0 {
		regionKey, err := service.GetUnLoginUserBelongRegion(l.svcCtx, l.ctx, r)
		if err != nil {
			l.Logger.Errorf("GetBeginnerTasks GetUnLoginUserBelongRegion is error: %v", err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		l.Logger.Infof("GetBeginnerTasks user not login regionKey is :%s", regionKey)
		//根据地区获取任务列表 注册，入门
		tasks, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgList(l.ctx, regionKey, []int{consts.UserTaskTypeNewbieRegister, consts.UserTaskTypeNewbieGuide}, 0)
		if err != nil {
			l.Logger.Errorf("GetBeginnerTasks NewWelfareMysqlDBUtil is error ，Params is：%s, err is:%v", regionKey, err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		//赋值描述中获取的最大奖励
		resp.DescAllReward = consts.GetRegionMaxDescAllReward()[regionKey]
		if len(tasks) == 0 {
			l.Logger.Errorf("GetBeginnerTasks taskList len is 0 ,Params is：%s", regionKey)
		}
		//定义入门任务一共获取到的奖励
		allBeginnerReward := int64(0)
		//获取任务中心的任务ID列表
		taskCenterIds := make([]int64, 0, len(tasks))
		//注册任务的卡劵ID和Source
		registerCouponId := int64(0)
		registerSource := ""
		for _, taskInfo := range tasks {
			if taskInfo.TaskId > 0 {
				taskCenterIds = append(taskCenterIds, taskInfo.TaskId)
			}
			if taskInfo.Type == consts.UserTaskTypeNewbieRegister {
				registerTaskInfo := dao.RegisterTaskInfo{}
				err = json.Unmarshal([]byte(taskInfo.ExtraTaskInfo.String), &registerTaskInfo)
				if err != nil {
					l.Logger.Errorf("GetBeginnerTasks json.Unmarshal.task.ExtraTaskInfo is err,params is:%s ，err is：%v", taskInfo.ExtraTaskInfo.String, err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
				if registerTaskInfo.CouponId == 0 || registerTaskInfo.Source == "" {
					l.Logger.Errorf("GetBeginnerTasks registerTaskInfo is err,params is:%s ，err is：%v", taskInfo.ExtraTaskInfo.String, err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
				registerCouponId = registerTaskInfo.CouponId
				registerSource = registerTaskInfo.Source
			}
		}
		//获取任务中心任务信息
		taskCenterMap := map[int64]*task.Task{}
		if len(taskCenterIds) > 0 {
			taskCenterMap, err = service.GetTaskListByTaskIds(l.svcCtx, l.ctx, 0, taskCenterIds)
			if err != nil {
				l.Logger.Errorf("GetBeginnerTasks GetTaskListByTaskIds is error ，Params is：%v, err is:%v", taskCenterIds, err)
				return nil, consts.GetErrorMsg(r, consts.ErrApiError)
			}
		}

		taskCenterList := make([]*task.Task, 0, len(taskCenterMap))
		for _, taskInfo := range taskCenterMap {
			taskCenterList = append(taskCenterList, taskInfo)
		}
		//获取任务中心所有奖励信息
		taskRewardMap := service.QueryTaskCenterReward(taskCenterList)
		//获取卡劵详情信息 卡劵详情信息中有缓存提高查询速度
		queryCouponList := make([]*service.QueryCoupon, 0, len(taskRewardMap)*2)
		for _, taskRewardList := range taskRewardMap {
			for _, reward := range taskRewardList {
				if reward.RewardCouponId > 0 && reward.RewardSource != "" {
					queryCouponList = append(queryCouponList, &service.QueryCoupon{
						CouponId: reward.RewardCouponId,
						Source:   reward.RewardSource,
					})
				}
			}
		}
		//增加注册任务的卡劵ID和Source
		if registerCouponId > 0 && registerSource != "" {
			queryCouponList = append(queryCouponList, &service.QueryCoupon{
				CouponId: registerCouponId,
				Source:   registerSource,
			})
		}
		//获取卡劵信息
		couponMap, err := service.QueryCouponList(l.svcCtx, l.ctx, queryCouponList)
		if err != nil {
			queryCouponListByte, _ := json.Marshal(queryCouponList)
			l.Logger.Errorf("GetBeginnerTasks QueryCouponList is error ，Params is：%s, err is:%v", string(queryCouponListByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrApiError)
		}
		taskList := make([]*types.BeginnerTask, 0, len(tasks))
		for _, taskInfo := range tasks {
			if taskInfo.TaskId > 0 && (taskCenterMap[taskInfo.TaskId] == nil || taskCenterMap[taskInfo.TaskId].Title == "") {
				l.Logger.Warnf("GetBeginnerTasks center task failed, taskCenterId is:%d 任务中心数据存在或状态异常", taskInfo.TaskId)
				continue
			}
			copyInfo := taskService.Copy{}
			reward := &service.TaskCenterReward{}
			//app请求不显示下载任务
			if taskInfo.TaskType == consts.UserTaskTaskTypeAppDownload && isApp == 1 {
				continue
			}
			if _, ok := taskCenterMap[taskInfo.TaskId]; ok && taskCenterMap[taskInfo.TaskId] != nil {
				copyInfo = taskCenterMap[taskInfo.TaskId].CopyList[lang]
				//默认使用英文标题和描述
				if copyInfo.Desc == "" && copyInfo.Title == "" {
					copyInfo = taskCenterMap[taskInfo.TaskId].CopyList[consts.EN]
				}
				rewards := taskRewardMap[taskInfo.TaskId]
				if len(rewards) > 0 {
					reward = rewards[0]
				}
				//处理任务描述信息 单位使用默认USDT
				copyInfo.Desc = service.DealTaskDesc(copyInfo.Desc, reward.MinNum, reward.RewardNum, "USDT", 0)
			}
			//额外奖励过期时间
			extraRewardEndTime := int64(0)
			//获取注册任务的奖励信息
			if taskInfo.Type == consts.UserTaskTypeNewbieRegister {
				registerTaskInfo := dao.RegisterTaskInfo{}
				err = json.Unmarshal([]byte(taskInfo.ExtraTaskInfo.String), &registerTaskInfo)
				if err != nil {
					l.Logger.Errorf("GetBeginnerTasks json.Unmarshal.task.ExtraTaskInfo is err,params is:%s ，err is：%v", taskInfo.ExtraTaskInfo.String, err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
				if registerTaskInfo.CouponId == 0 || registerTaskInfo.Source == "" {
					l.Logger.Errorf("GetBeginnerTasks registerTaskInfo is err,params is:%s ，err is：%v", taskInfo.ExtraTaskInfo.String, err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
				reward.RewardCouponId = registerTaskInfo.CouponId
				reward.RewardSource = registerTaskInfo.Source
				reward.RewardType = consts.PrizeTypeCoupon
				couponInfo := couponMap[fmt.Sprintf("%d:%s", registerTaskInfo.CouponId, registerTaskInfo.Source)]
				if couponInfo != nil {
					couponAmountVal, err := strconv.ParseInt(couponInfo.Amount, 10, 64)
					if err != nil {
						//只打印不返回
						l.Logger.Warnf("GetBeginnerTasks strconv.Atoi(couponAmount) is error is:%v, params is:%s", err, couponInfo.Amount)
					}
					reward.RewardNum = couponAmountVal
				}
			} else {
				//普通入门任务
				welcomeTaskInfo := dao.WelcomeTaskInfo{}
				_ = json.Unmarshal([]byte(taskInfo.ExtraTaskInfo.String), &welcomeTaskInfo)
				//配置了额外奖励时间
				if welcomeTaskInfo.LimitDays > 0 && reward.ExtraRewardType > 0 {
					extraRewardEndTime = time.Now().AddDate(0, 0, int(welcomeTaskInfo.LimitDays)).Unix()
				} else {
					//没有配置额外奖励时间不展示额外奖励信息
					reward.ExtraRewardNum = 0
					reward.ExtraRewardType = 0
				}
			}
			//总奖励等于正常奖励加上额外奖励 只加卡劵的
			if reward.RewardType == consts.PrizeTypeCoupon {
				allBeginnerReward += reward.RewardNum
			}
			if reward.ExtraRewardType == consts.PrizeTypeCoupon {
				allBeginnerReward += reward.ExtraRewardNum
			}
			rewardCurrency := "USDT"
			resp.AllBeginnerCurrency = "USDT"
			couponInfo := couponMap[fmt.Sprintf("%d:%s", reward.RewardCouponId, reward.RewardSource)]
			if couponInfo != nil {
				if couponInfo.Currency != "" && couponInfo.Currency != rewardCurrency {
					rewardCurrency = couponInfo.Currency
					//目前福利中心新人任务的币种都是一样的，所以总奖励的币种直接覆盖，后续如果需要修改直接更改AllBeginnerCurrency字段
					resp.AllBeginnerCurrency = couponInfo.Currency
				}
			}
			taskList = append(taskList, &types.BeginnerTask{
				WelfareTaskID:      taskInfo.Id,
				WelfareTaskType:    int(taskInfo.Type),
				TaskCenterType:     int(taskInfo.TaskType),
				TaskName:           copyInfo.Title,
				TaskDesc:           copyInfo.Desc,
				RewardType:         reward.RewardType,
				RewardNum:          reward.RewardNum,
				ExtraRewardType:    reward.ExtraRewardType,
				ExtraRewardNum:     reward.ExtraRewardNum,
				ButtonType:         taskInfo.ButtonType,
				Status:             0,
				ExtraRewardEndTime: extraRewardEndTime,
				RewardCurrency:     rewardCurrency,
				TaskFinishTime:     0,
			})
		}
		resp.BeginnerTasks = taskList
		resp.AllBeginnerReward = allBeginnerReward
	} else {
		if req.ConstID == "" {
			l.Logger.Infof("GetBeginnerTasks req invalid parameter: %s", req.ConstID)
			return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
		}
		uid = userInfo.UID
		//登陆状态根据用户信息查询查询任务列表
		//先检查风控
		dataMap := make(map[string]interface{})
		dataMap["user_id"] = userInfo.UID
		dataMap["ip"] = requestools.GetClientIP(r)
		dataMap["const_id"] = req.ConstID
		dataMap["is_async"] = 0
		isRisk, err := service.GetUserRiskData(l.svcCtx, l.ctx, userInfo.UID, req.ConstID, consts.RiskEventCodeIndexPageCheck, dataMap, r)
		if err != nil {
			l.Logger.Errorf("GetBeginnerTasks InnerCheckRisk is err,params is:%d, %s ，err is：%v", userInfo.UID, req.ConstID, err)
			return nil, consts.GetErrorMsg(r, consts.ErrApiError)
		}
		//命中风控
		if isRisk {
			resp.IsRiskNewbie = 1
		}
		//查询用户快照
		registerInfo, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetWelfareUserRegisterInfo(l.svcCtx, l.ctx, int64(userInfo.UID))
		if err != nil {
			l.Logger.Errorf("GetBeginnerTasks GetWelfareUserRegisterInfo is err,params is:%d ，err is：%v", userInfo.UID, err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		//赋值描述中获取的最大奖励
		resp.DescAllReward = consts.GetRegionMaxDescAllReward()[registerInfo.Region]
		userTasks, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryUserTaskList(l.ctx, userInfo.UID, []int{consts.UserTaskTypeNewbieRegister, consts.UserTaskTypeNewbieGuide}, 0, 0)
		if err != nil {
			l.Logger.Errorf("GetBeginnerTasks QueryUserTaskList is err,params is:%d ，err is：%v", userInfo.UID, err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		welfareTasks := make([]*welfare_task_cfgs.WelfareTaskCfgs, len(userTasks))
		//验证用户是否领取了下载任务
		hasDownloadTask := false
		taskCenterIds := make([]int64, 0, len(userTasks))
		userTaskMap := map[int64]*welfare_user_tasks.WelfareUserTasks{}
		registerCouponId := int64(0)
		registerSource := ""
		//定义下载任务所在的地区
		downloadRegion := ""
		for i, userTask := range userTasks {
			taskInfo := &welfare_task_cfgs.WelfareTaskCfgs{}
			err = json.Unmarshal([]byte(userTask.Memo), taskInfo)
			if err != nil {
				l.Logger.Errorf("GetBeginnerTasks json.Unmarshal([]byte(userTask.Memo),taskInfo) is err,params is:%s ，err is：%v", userTask.Memo, err)
				return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
			}
			downloadRegion = taskInfo.TaskRegionKey
			welfareTasks[i] = taskInfo
			if userTask.TaskCenterId > 0 {
				taskCenterIds = append(taskCenterIds, userTask.TaskCenterId)
			}
			userTaskMap[userTask.TaskId] = userTask
			if userTask.TaskType == consts.UserTaskTaskTypeAppDownload {
				hasDownloadTask = true
			}
			if taskInfo.Type == consts.UserTaskTypeNewbieRegister {
				registerTaskInfo := dao.RegisterTaskInfo{}
				err = json.Unmarshal([]byte(taskInfo.ExtraTaskInfo.String), &registerTaskInfo)
				if err != nil {
					l.Logger.Errorf("GetBeginnerTasks json.Unmarshal.task.ExtraTaskInfo is err,params is:%s ，err is：%v", taskInfo.ExtraTaskInfo.String, err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
				if registerTaskInfo.CouponId == 0 || registerTaskInfo.Source == "" {
					l.Logger.Errorf("GetBeginnerTasks registerTaskInfo is err,params is:%s ，err is：%v", taskInfo.ExtraTaskInfo.String, err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
				registerCouponId = registerTaskInfo.CouponId
				registerSource = registerTaskInfo.Source
			}
		}
		//获取任务中心任务信息
		taskCenterMap := map[int64]*task.Task{}
		if len(taskCenterIds) > 0 {
			taskCenterMap, err = service.GetTaskListByTaskIds(l.svcCtx, l.ctx, uid, taskCenterIds)
			if err != nil {
				l.Logger.Errorf("GetBeginnerTasks GetTaskListByTaskIds is error ，Params is：%d , %v, err is:%v", uid, taskCenterIds, err)
				return nil, consts.GetErrorMsg(r, consts.ErrApiError)
			}
		}

		hasDownloadApp, err := service.HasDownloadApp(l.svcCtx, l.ctx, strconv.Itoa(uid))
		if err != nil {
			l.Logger.Errorf("GetBeginnerTasks HasDownloadApp is error ，Params is：%d , err is:%v", uid, err)
			return nil, consts.GetErrorMsg(r, consts.ErrApiError)
		}
		//用户未领取下载任务 这里只需要判断是否登陆过app即可
		//因为如果用户领取了下载任务，在app中也会看到。如果用户未领取下载任务，并且用户还在app上登陆过，便看不到下载任务
		if !hasDownloadTask && hasDownloadApp == consts.CommonNo {
			welfareTask, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetWelfareTaskCfgInfo(l.ctx, downloadRegion, consts.UserTaskTypeNewbieGuide, 0, consts.UserTaskTaskTypeAppDownload)
			if err != nil {
				l.Logger.Errorf("GetBeginnerTasks GetWelfareTaskCfgInfo is error ，Params is：%s, err is:%v", registerInfo.Region, err)
				return nil, consts.GetErrorMsg(r, consts.ErrDbError)
			}
			if welfareTask.Id > 0 {
				welfareTasks = append(welfareTasks, welfareTask)
			}
			//获取任务中心任务信息
			taskCenterDownMap, err := service.GetTaskListByTaskIds(l.svcCtx, l.ctx, 0, []int64{welfareTask.TaskId})
			if err != nil {
				l.Logger.Errorf("GetBeginnerTasks GetTaskListByTaskIds is error ，taskCenterIds is %v, err is:%v", taskCenterIds, err)
				return nil, consts.GetErrorMsg(r, consts.ErrApiError)
			}
			for key, taskInfo := range taskCenterDownMap {
				taskCenterMap[key] = taskInfo
			}
		}
		//获取到了全部任务中心的任务信息
		l.Logger.Infof("GetBeginnerTasks taskCenter task is:%v", taskCenterMap)
		//正序排序
		sort.Slice(welfareTasks, func(i, j int) bool {
			return welfareTasks[i].Sort < welfareTasks[j].Sort
		})
		taskCenterList := make([]*task.Task, 0, len(taskCenterMap))
		for _, taskInfo := range taskCenterMap {
			taskCenterList = append(taskCenterList, taskInfo)
		}
		//获取任务中心所有奖励信息
		taskRewardMap := service.QueryTaskCenterReward(taskCenterList)
		//获取卡劵详情信息 卡劵详情信息中有缓存提高查询速度
		queryCouponList := make([]*service.QueryCoupon, 0, len(taskRewardMap)*2)
		for _, taskRewardList := range taskRewardMap {
			for _, reward := range taskRewardList {
				if reward.RewardCouponId > 0 && reward.RewardSource != "" {
					queryCouponList = append(queryCouponList, &service.QueryCoupon{
						CouponId: reward.RewardCouponId,
						Source:   reward.RewardSource,
					})
				}
			}
		}
		if registerCouponId > 0 && registerSource != "" {
			//增加注册任务的卡劵ID和Source
			queryCouponList = append(queryCouponList, &service.QueryCoupon{
				CouponId: registerCouponId,
				Source:   registerSource,
			})
		}
		//获取卡劵信息
		couponMap, err := service.QueryCouponList(l.svcCtx, l.ctx, queryCouponList)
		if err != nil {
			queryCouponListByte, _ := json.Marshal(queryCouponList)
			l.Logger.Errorf("GetBeginnerTasks QueryCouponList is error ，Params is：%s, err is:%v", string(queryCouponListByte), err)
			return nil, consts.GetErrorMsg(r, consts.ErrApiError)
		}
		//定义入门任务一共获取到的奖励
		allBeginnerReward := int64(0)
		taskList := make([]*types.BeginnerTask, 0, 10)
		for _, welfareTask := range welfareTasks {
			if welfareTask.TaskId > 0 && (taskCenterMap[welfareTask.TaskId] == nil || taskCenterMap[welfareTask.TaskId].Title == "") {
				l.Logger.Warnf("GetBeginnerTasks center task failed, taskCenterId is:%d,uid is:%d 任务中心数据存在或状态异常", welfareTask.TaskId, uid)
				continue
			}
			copyInfo := taskService.Copy{}
			reward := &service.TaskCenterReward{}
			if _, ok := taskCenterMap[welfareTask.TaskId]; ok && taskCenterMap[welfareTask.TaskId] != nil {
				copyInfo = taskCenterMap[welfareTask.TaskId].CopyList[lang]
				//默认使用英文标题和描述
				l.Logger.Infof("GetBeginnerTasks center task desc; lang is:%s , copyInfo.Desc is %s, copyInfo.Title is %s, task center is:%d", lang, copyInfo.Desc, copyInfo.Title, welfareTask.TaskId)
				if copyInfo.Desc == "" && copyInfo.Title == "" {
					copyInfo = taskCenterMap[welfareTask.TaskId].CopyList[consts.EN]
				}
				rewards := taskRewardMap[welfareTask.TaskId]
				if len(rewards) > 0 {
					reward = rewards[0]
				}
				//处理任务描述信息 单位使用默认USDT
				copyInfo.Desc = service.DealTaskDesc(copyInfo.Desc, reward.MinNum, reward.RewardNum, "USDT", 0)
			}
			//定义额外奖励到期时间
			extraRewardEndTime := int64(0)
			//获取注册任务的奖励信息
			if welfareTask.Type == consts.UserTaskTypeNewbieRegister {
				registerTaskInfo := dao.RegisterTaskInfo{}
				err = json.Unmarshal([]byte(welfareTask.ExtraTaskInfo.String), &registerTaskInfo)
				if err != nil {
					l.Logger.Errorf("GetBeginnerTasks json.Unmarshal.task.ExtraTaskInfo is err,params is:%s ，err is：%v", welfareTask.ExtraTaskInfo.String, err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
				if registerTaskInfo.CouponId == 0 || registerTaskInfo.Source == "" {
					l.Logger.Errorf("GetBeginnerTasks registerTaskInfo is err,params is:%s ，err is：%v", welfareTask.ExtraTaskInfo.String, err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
				reward.RewardCouponId = registerTaskInfo.CouponId
				reward.RewardSource = registerTaskInfo.Source
				reward.RewardType = consts.PrizeTypeCoupon
				couponInfo := couponMap[fmt.Sprintf("%d:%s", registerTaskInfo.CouponId, registerTaskInfo.Source)]
				if couponInfo != nil {
					couponAmountVal, err := strconv.ParseInt(couponInfo.Amount, 10, 64)
					if err != nil {
						//只打印不返回
						l.Logger.Warnf("GetBeginnerTasks strconv.Atoi(couponAmount) is error is:%v, params is:%s", err, couponInfo.Amount)
					}
					reward.RewardNum = couponAmountVal
				}
			} else {
				//普通入门任务
				welcomeTaskInfo := dao.WelcomeTaskInfo{}
				_ = json.Unmarshal([]byte(welfareTask.ExtraTaskInfo.String), &welcomeTaskInfo)
				if welcomeTaskInfo.LimitDays > 0 && reward.ExtraRewardType > 0 {
					if userTaskMap[welfareTask.Id] != nil {
						extraRewardEndTime = userTaskMap[welfareTask.Id].CreatedAt.AddDate(0, 0, int(welcomeTaskInfo.LimitDays)).Unix()
					} else {
						extraRewardEndTime = time.Now().AddDate(0, 0, int(welcomeTaskInfo.LimitDays)).Unix()
					}
					if extraRewardEndTime > registerInfo.NewbieEndTime {
						extraRewardEndTime = registerInfo.NewbieEndTime
					}
				} else {
					//没有配置额外奖励时间就没有额外奖励
					reward.ExtraRewardType = 0
					reward.ExtraRewardNum = 0
				}
			}
			//总奖励等于正常奖励加上额外奖励 只加卡劵的
			if reward.RewardType == consts.PrizeTypeCoupon {
				allBeginnerReward += reward.RewardNum
			}
			status := 0
			taskFinishTime := int64(0)
			if userTaskMap[welfareTask.Id] != nil {
				status = int(userTaskMap[welfareTask.Id].Status)
				taskFinishTime = userTaskMap[welfareTask.Id].FinishTaskTime
				//如果缓存中存在从缓存中获取
				statusStr, _ := l.svcCtx.Redis.Get(fmt.Sprintf(consts.WelfareUidAndTaskIdAndStatus, uid, userTaskMap[welfareTask.Id].Id))
				if statusStr != "" {
					l.Logger.Infof("GetBeginnerTasks task get status from cache,uid is:%d , userTaskID is:%d , statusStr is:%s", uid, userTaskMap[welfareTask.Id].Id, statusStr)
					status, _ = strconv.Atoi(statusStr)
				}
			}

			//额外奖励要根据用户完成时间计算
			if reward.ExtraRewardType == consts.PrizeTypeCoupon && extraRewardEndTime > 0 {
				//如果是未领奖状态，并且完成时间大于额外奖励时间不展示额外奖励
				if (status < consts.StatusDone && time.Now().Unix() <= extraRewardEndTime) || (status >= consts.StatusDone && taskFinishTime <= extraRewardEndTime) {
					allBeginnerReward += reward.ExtraRewardNum
				}
			}
			rewardCurrency := "USDT"
			resp.AllBeginnerCurrency = "USDT"
			couponInfo := couponMap[fmt.Sprintf("%d:%s", reward.RewardCouponId, reward.RewardSource)]
			if couponInfo != nil {
				if couponInfo.Currency != "" && couponInfo.Currency != rewardCurrency {
					rewardCurrency = couponInfo.Currency
					//目前福利中心新人任务的币种都是一样的，所以总奖励的币种直接覆盖，后续如果需要修改直接更改AllBeginnerCurrency字段
					resp.AllBeginnerCurrency = couponInfo.Currency
				}
			}
			taskList = append(taskList, &types.BeginnerTask{
				WelfareTaskID:      welfareTask.Id,
				WelfareTaskType:    int(welfareTask.Type),
				TaskCenterType:     int(welfareTask.TaskType),
				TaskName:           copyInfo.Title,
				TaskDesc:           copyInfo.Desc,
				RewardType:         reward.RewardType,
				RewardNum:          reward.RewardNum,
				ExtraRewardType:    reward.ExtraRewardType,
				ExtraRewardNum:     reward.ExtraRewardNum,
				ButtonType:         welfareTask.ButtonType,
				Status:             status,
				ExtraRewardEndTime: extraRewardEndTime,
				RewardCurrency:     rewardCurrency,
				TaskFinishTime:     taskFinishTime,
			})
		}
		//赋值入门任务的总奖励
		resp.AllBeginnerReward = allBeginnerReward
		resp.NewbieEndTime = registerInfo.NewbieEndTime
		resp.BeginnerTasks = taskList
		//更新用户快照信息
		if registerInfo.IsVisitNewbie != 1 {
			updateMap := map[string]interface{}{}
			updateMap["is_visit_newbie"] = 1
			err = dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).UpdateUserRegisterInfo(l.svcCtx, l.ctx, registerInfo.Uid, updateMap)
			if err != nil {
				//只打印，不返回报错
				l.Logger.Errorf("GetBeginnerTasks UpdateUserRegisterInfo is error ，Params is：%d, %v, err is:%v", registerInfo.Uid, updateMap, err)
			}
		}
	}
	byteVal, err := json.Marshal(resp)
	if err == nil {
		//将结果缓存1分钟
		_ = l.svcCtx.Redis.Setex(redisKey, string(byteVal), 60)
	}
	return
}
