package web

import (
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/service/prize_service"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"time"

	"bitbucket.org/gateio/gateio-lib-common-go/requestools"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type CheckInLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 签到
func NewCheckInLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckInLogic {
	return &CheckInLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CheckInLogic) CheckIn(req *types.CheckInReq, r *http.Request) (resp *types.UniversalNoParamReq, err error) {
	userInfo := requestools.GetUserInfo(r)
	uid := 0
	if userInfo != nil {
		uid = userInfo.UID
	}
	defer func() {
		respJson := []byte{}
		if resp != nil {
			respJson, _ = json.Marshal(resp)
		}
		l.Logger.Info("CheckIn resp: ", string(respJson), " err: ", err, "  uid is:", uid)
	}()
	if userInfo == nil || uid <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	//频率限制，加锁
	lockKey := fmt.Sprintf(consts.CheckInLock, uid)
	isLock, err := l.svcCtx.Redis.SetnxExCtx(l.ctx, lockKey, "1", 5)
	if err != nil {
		l.Logger.Warnf("CheckIn SetnxExCtx is err,redis key is:%s, err is:%v", lockKey, err)
	}
	if !isLock {
		return nil, consts.GetErrorMsg(r, consts.ManyAttempts)
	}

	boolCheck, checkCode := service.CheckUserIdentity(l.svcCtx, l.ctx, userInfo)
	if !boolCheck {
		return nil, consts.GetErrorMsg(r, checkCode)
	}

	today := utils.GetYmdDay(0)
	//获取签到任务信息
	taskList := dao.GetCheckInTasks()
	if len(taskList) == 0 {
		l.Logger.Infof("CheckIn GetCheckInTasks len(taskList) is 0")
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}
	// 风控查询
	dataMap := make(map[string]interface{})
	dataMap["user_id"] = uid
	dataMap["ip"] = requestools.GetClientIP(r)
	dataMap["const_id"] = req.ConstID
	dataMap["is_async"] = 0
	isRisk, err := service.GetUserRiskData(l.svcCtx, l.ctx, userInfo.UID, req.ConstID, consts.RiskEventCodeTaskCenter, dataMap, r)
	if err != nil {
		l.Logger.Errorf("ReceiveTask InnerCheckRisk is err,params is:%d, %s ，err is：%v", userInfo.UID, req.ConstID, err)
		return nil, consts.GetErrorMsg(r, consts.ErrRiskNoticeReject)
	}
	//命中风控
	if isRisk {
		//命中风控直接返回
		return nil, consts.GetErrorMsg(r, consts.ErrRiskNoticeReject)
	}

	//获取用户最后一次签到信息
	lastCheckIn, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).GetLastCheckIn(l.ctx, uid, consts.CheckInStatusFailed)
	if err != nil {
		l.Logger.Errorf("CheckIn GetLastCheckIn is err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	//检查今天是否已签到
	if lastCheckIn != nil && lastCheckIn.Day == int64(today) {
		return nil, consts.GetErrorMsg(r, consts.AttendedToday)
	}

	yesterday := utils.GetYmdDay(time.Now().AddDate(0, 0, -1).Unix())
	//获取今天应该签到的任务
	task := &dao.CheckInConfig{}
	isCycleLastDay := 0
	// 确定今天应该签到的任务
	// 以下情况需要从第一天开始签到:
	// 1. 用户从未签到过(lastCheckIn == nil)
	// 2. 用户完成了上一个签到周期且昨天是最后一天(lastCheckIn.IsCycleLastDay == 1 && lastCheckIn.Day == yesterday)
	// 3. 用户签到记录异常(lastCheckIn.Day == 0)
	// 4. 用户签到中断(lastCheckIn.Day < yesterday)
	if lastCheckIn == nil || (lastCheckIn.IsCycleLastDay == 1 && int(lastCheckIn.Day) == yesterday) || lastCheckIn.Day == 0 ||
		int(lastCheckIn.Day) < yesterday {
		task = &dao.CheckInConfig{
			WeekDay:      taskList[0].WeekDay,
			IsAttendance: taskList[0].IsAttendance,
			PrizeType:    taskList[0].PrizeType,
			PrizeTypeNum: taskList[0].PrizeTypeNum,
			Sort:         taskList[0].Sort,
		}
	} else {
		// 找出用户最后一次签到对应的任务索引
		taskIndex := -1
		for i, taskInfo := range taskList {
			if int64(taskInfo.WeekDay) == lastCheckIn.CheckInTaskId {
				taskIndex = i
				break
			}
		}

		// 如果是倒数第二个任务,标记为周期最后一天
		if taskIndex == len(taskList)-2 {
			isCycleLastDay = 1
		}

		// 获取下一个任务
		if taskIndex >= 0 && taskIndex < len(taskList)-1 {
			nextTask := taskList[taskIndex+1]
			task = &dao.CheckInConfig{
				WeekDay:      nextTask.WeekDay,
				IsAttendance: nextTask.IsAttendance,
				PrizeType:    nextTask.PrizeType,
				PrizeTypeNum: nextTask.PrizeTypeNum,
				Sort:         nextTask.Sort,
			}
		}
	}

	// 开关处理
	var boolSwitch bool
	var boolErr error
	switch task.PrizeType {
	case consts.CheckInPoints:
		// 验证积分开关
		boolSwitch, boolErr = service.GetSystemSwitchByKey(l.svcCtx, l.ctx, consts.WelfareConfigPointsSend)
	case consts.CheckInCoupon:
		// 验证卡券开关
		boolSwitch, boolErr = service.GetSystemSwitchByKey(l.svcCtx, l.ctx, consts.WelfareConfigCouponSend)
	}

	if boolErr != nil {
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}

	if boolSwitch == false {
		return nil, consts.GetErrorMsg(r, consts.ErrSuspendedForMaintenance)
	}

	_, err = prize_service.CheckIn(l.svcCtx, l.ctx, task, uid, isCycleLastDay)
	if err != nil {
		l.Logger.Errorf("CheckIn InsertCheckIn is err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}

	return
}
