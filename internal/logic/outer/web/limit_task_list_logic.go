package web

import (
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/service"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type LimitTaskListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 老客限时任务列表
func NewLimitTaskListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LimitTaskListLogic {
	return &LimitTaskListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LimitTaskListLogic) LimitTaskList(_ *types.UniversalNoParamReq, r *http.Request) (resp *types.DailyTaskListFormatResp, err error) {
	return service.NewOldCustomerTaskService(l.ctx, l.svcCtx).GetTaskList(consts.UserTaskTypeVeteranLimited, r)
}
