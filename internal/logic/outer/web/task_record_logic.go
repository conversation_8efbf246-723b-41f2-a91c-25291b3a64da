package web

import (
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	taskService "bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/service"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type TaskRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 分页查询任务记录
func NewTaskRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TaskRecordLogic {
	return &TaskRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TaskRecordLogic) TaskRecord(req *types.TaskRecordReq, r *http.Request) (resp *types.TaskRecordResp, err error) {
	userInfo := requestools.GetUserInfo(r)
	if userInfo == nil || userInfo.UID <= 0 || userInfo.Type == 5 {
		l.Logger.Info("PrizeRecord user not login")
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	lang := requestools.GetUserLanguage(r)
	resp = &types.TaskRecordResp{}
	startTime := ""
	if req.Day > 0 {
		startTime = time.Now().AddDate(0, 0, -req.Day).Format(time.DateTime)
	}
	list := []*types.TaskRecord{}
	fromId := int64(0)
	toId := int64(0)
	//获取用户签到记录
	records, countVal, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryTaskRecordsByPage(l.ctx, userInfo.UID, req.Status, startTime, req.Sort, req.Page, req.PerPage)
	if err != nil {
		l.Logger.Errorf("TaskRecord QueryExchangeRecordsByPage is err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	taskCenterIds := make([]int64, 0)
	for _, record := range records {
		if record.TaskCenterId > 0 {
			taskCenterIds = append(taskCenterIds, record.TaskCenterId)
		}
	}
	taskCenterNameMap := map[int64]string{}
	if len(taskCenterIds) > 0 {
		//获取任务中心任务信息
		taskCenterMap, err := service.GetTaskListByTaskIds(l.svcCtx, l.ctx, userInfo.UID, taskCenterIds)
		if err != nil {
			l.Logger.Errorf("TaskRecord GetTaskListByTaskIds is error ，Params is：%d , %v, err is:%v", userInfo.UID, taskCenterIds, err)
			return nil, consts.GetErrorMsg(r, consts.ErrApiError)
		}
		for _, record := range records {
			copyInfo := taskService.Copy{}
			if _, ok := taskCenterMap[record.TaskCenterId]; ok && taskCenterMap[record.TaskCenterId] != nil {
				copyInfo = taskCenterMap[record.TaskCenterId].CopyList[lang]
				//默认使用英文标题和描述
				if copyInfo.Title == "" {
					copyInfo = taskCenterMap[record.TaskCenterId].CopyList[consts.EN]
				}
				taskCenterNameMap[record.TaskCenterId] = copyInfo.Title
			}
		}
	}
	l.Logger.Infof("TaskRecord taskCenterNameMap is: %v", taskCenterNameMap)
	list = make([]*types.TaskRecord, len(records))
	for i, record := range records {
		if i == 0 {
			fromId = record.Id
		}
		if i == len(records)-1 {
			toId = record.Id
		}
		//将数据库的状态转成前端对应的枚举值状态
		status := int(record.Status)
		list[i] = &types.TaskRecord{
			ID:              record.Id,
			TaskID:          record.TaskId,
			WelfareTaskType: int(record.Type),
			TaskCenterType:  int(record.TaskType),
			TaskName:        taskCenterNameMap[record.TaskCenterId],
			Status:          status,
			ReceiveTime:     record.CreatedAt.Unix(),
			Stage:           int(record.Stage),
		}
	}
	lastPage := 0
	if countVal%int64(req.PerPage) > 0 {
		lastPage = int(countVal/int64(req.PerPage)) + 1
	} else {
		lastPage = int(countVal / int64(req.PerPage))
	}
	resp.CurrentPage = req.Page
	resp.From = fromId
	resp.LastPage = lastPage
	resp.PerPage = req.PerPage
	resp.To = toId
	resp.Total = countVal
	resp.List = list

	return
}
