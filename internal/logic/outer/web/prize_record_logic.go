package web

import (
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/coupon"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"encoding/json"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"strings"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type PrizeRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 分页查询领奖记录
func NewPrizeRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PrizeRecordLogic {
	return &PrizeRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *PrizeRecordLogic) PrizeRecord(req *types.PrizeRecordReq, r *http.Request) (resp *types.PrizeRecordResp, err error) {
	userInfo := requestools.GetUserInfo(r)
	//Tg游客限制
	if userInfo == nil || userInfo.UID <= 0 || userInfo.Type == 5 {
		l.Logger.Info("PrizeRecord user not login")
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}
	lang := requestools.GetUserLanguage(r)
	resp = &types.PrizeRecordResp{}
	startTime := ""
	if req.Day > 0 {
		startTime = time.Now().AddDate(0, 0, -req.Day).Format(time.DateTime)
	}
	//获取时间T值，用于判断是老福利中心记录还是新福利中心记录
	t, err := service.GetWelfareConfig(l.svcCtx, l.ctx, consts.WelfareRefactorTime)
	if err != nil {
		l.Logger.Errorf("PrizeRecord GetWelfareConfig is err: %v,UserRegisterDeal uid is:%d ,", err, userInfo.UID)
		return
	}
	if t == "" {
		l.Logger.Errorf("PrizeRecord welfare_refactor_time is nil;UserRegisterDeal uid is:%d ,name is:%s ,time is: %s", userInfo.UID, consts.WelfareRefactorTime, t)
		return
	}
	timestampT, _ := utils.TransformDateStringToTime(t)
	list := []*types.PrizeRecord{}
	fromId := int64(0)
	toId := int64(0)
	//获取用户签到记录
	records, countVal, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryExchangeRecordsByPage(l.ctx, userInfo.UID, req.Status, startTime, req.Sort, req.Page, req.PerPage)
	if err != nil {
		l.Logger.Errorf("PrizeRecord QueryExchangeRecordsByPage is err: %v", err)
		return nil, consts.GetErrorMsg(r, consts.ErrDbError)
	}
	prizeIds := make([]int, 0, len(records))
	//新客奖励用户任务记录ID
	userNewTaskRecordIds := make([]int64, 0)

	for _, record := range records {
		//获取积分兑换的名称
		if record.PrizeSource == consts.RecordPrizeSourceExchange && record.CreatedAt.Unix() > timestampT.Unix() {
			prizeIds = append(prizeIds, record.PrizeID)
		}
		//获取新福利中心的任务信息
		if record.TaskID > 0 && record.CreatedAt.Unix() > timestampT.Unix() {
			userNewTaskRecordIds = append(userNewTaskRecordIds, record.TaskID)
		}

	}
	userTaskCenterMap := map[int64]int64{}
	taskCenterCouponListMap := map[int64][]*service.QueryCoupon{}
	registerTaskCouponListMap := map[int64][]*service.QueryCoupon{}
	if len(userNewTaskRecordIds) > 0 {
		userTasks, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryTaskRecordsByIds(l.ctx, userInfo.UID, userNewTaskRecordIds)
		if err != nil {
			l.Logger.Errorf("PrizeRecord QueryTaskRecordsByIds is err: %v", err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		taskCenterIds := make([]int64, 0, len(userTasks))
		for _, userTask := range userTasks {
			if userTask.TaskCenterId > 0 {
				userTaskCenterMap[userTask.Id] = userTask.TaskCenterId
				taskCenterIds = append(taskCenterIds, userTask.TaskCenterId)
			}
			//注册任务
			if userTask.Type == consts.UserTaskTypeNewbieRegister {
				taskCfg := &welfare_task_cfgs.WelfareTaskCfgs{}
				err = json.Unmarshal([]byte(userTask.Memo), taskCfg)
				if err != nil {
					l.Logger.Errorf("PrizeRecord json.Unmarshal([]byte(userTask.Memo),taskInfo) is err,params is:%s ，err is：%v", userTask.Memo, err)
					return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
				}
				registerTaskInfo := dao.RegisterTaskInfo{}
				err = json.Unmarshal([]byte(taskCfg.ExtraTaskInfo.String), &registerTaskInfo)
				if err != nil {
					l.Logger.Errorf("PrizeRecord json.Unmarshal.task.ExtraTaskInfo is err,params is:%s ，err is：%v", taskCfg.ExtraTaskInfo.String, err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
				if registerTaskInfo.CouponId == 0 || registerTaskInfo.Source == "" {
					l.Logger.Errorf("PrizeRecord registerTaskInfo is err,params is:%s ，err is：%v", taskCfg.ExtraTaskInfo.String, err)
					return nil, consts.GetErrorMsg(r, consts.ErrDbError)
				}
				if registerTaskCouponListMap[userTask.Id] == nil {
					registerTaskCouponListMap[userTask.Id] = []*service.QueryCoupon{}
				}
				registerTaskCouponListMap[userTask.Id] = append(registerTaskCouponListMap[userTask.Id], &service.QueryCoupon{
					CouponId: registerTaskInfo.CouponId,
					Source:   registerTaskInfo.Source,
				})
			}
		}
		//获取任务中心任务信息
		taskCenterMap := map[int64]*task.Task{}
		if len(taskCenterIds) > 0 {
			taskCenterMap, err = service.GetTaskListByTaskIds(l.svcCtx, l.ctx, userInfo.UID, taskCenterIds)
			if err != nil {
				l.Logger.Errorf("PrizeRecord GetTaskListByTaskIds is error ，Params is：%d , %v, err is:%v", userInfo.UID, taskCenterIds, err)
				return nil, consts.GetErrorMsg(r, consts.ErrApiError)
			}
		}
		for _, taskInfo := range taskCenterMap {
			//获取任务中心所有奖励信息
			taskRewardMap := service.QueryTaskCenterReward([]*task.Task{taskInfo})
			queryCouponList := make([]*service.QueryCoupon, 0, len(taskRewardMap)*2)
			for _, taskRewardList := range taskRewardMap {
				for _, reward := range taskRewardList {
					if reward.RewardCouponId > 0 && reward.RewardSource != "" {
						queryCouponList = append(queryCouponList, &service.QueryCoupon{
							CouponId: reward.RewardCouponId,
							Source:   reward.RewardSource,
						})
					}
					if reward.ExtraRewardCouponId > 0 && reward.ExtraRewardSource != "" {
						queryCouponList = append(queryCouponList, &service.QueryCoupon{
							CouponId: reward.ExtraRewardCouponId,
							Source:   reward.ExtraRewardSource,
						})
					}
				}
			}
			taskCenterCouponListMap[taskInfo.TaskID] = queryCouponList
		}
	}
	//根据语言获取奖品名称
	prizeNameMap := map[int64]string{}
	if len(prizeIds) > 0 {
		prizes, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryPrizeInfo(l.ctx, prizeIds)
		if err != nil {
			l.Logger.Errorf("PrizeRecord QueryPrizeInfo,params is: %v, is err: %v", prizeIds, err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		for _, prize := range prizes {
			nameMap := map[string]string{}
			_ = json.Unmarshal([]byte(prize.PrizeName), &nameMap)
			//默认使用英文配置
			if nameMap[lang] == "" {
				prizeNameMap[prize.Id] = nameMap[consts.EN]
			} else {
				prizeNameMap[prize.Id] = nameMap[lang]
			}
		}
	}
	list = make([]*types.PrizeRecord, len(records))
	for i, record := range records {
		if i == 0 {
			fromId = record.ID
		}
		if i == len(records)-1 {
			toId = record.ID
		}
		prizeName := ""
		if record.PrizeSource == consts.RecordPrizeSourceExchange {
			prizeName = prizeNameMap[int64(record.PrizeID)]
		}
		couponResp := &coupon.CouponInfoResponse{}
		//获取新福利中心的任务信息
		if record.TaskID > 0 && record.CreatedAt.Unix() > timestampT.Unix() {
			taskCenterId := userTaskCenterMap[record.TaskID]
			queryCouponList := taskCenterCouponListMap[taskCenterId]
			if queryCouponList != nil && len(queryCouponList) > 0 {
				for _, queryCoupon := range queryCouponList {
					//如果CouponId一致则查询卡劵中心
					if queryCoupon.CouponId == int64(record.PrizeID) {
						couponResp, err = service.GetCouponInfo(l.svcCtx, l.ctx, queryCoupon.CouponId, queryCoupon.Source)
						if err != nil {
							l.Logger.Errorf("PrizeRecord GetCouponInfo is error ，Params is：%d ,%s, err is:%v", queryCoupon.CouponId, queryCoupon.Source, err)
							return nil, consts.GetErrorMsg(r, consts.ErrApiError)
						}
					}
				}
			}
		}
		if registerTaskCouponListMap[record.TaskID] != nil && len(registerTaskCouponListMap[record.TaskID]) > 0 {
			for _, queryCoupon := range registerTaskCouponListMap[record.TaskID] {
				//如果CouponId一致则查询卡劵中心
				if queryCoupon.CouponId == int64(record.PrizeID) {
					couponResp, err = service.GetCouponInfo(l.svcCtx, l.ctx, queryCoupon.CouponId, queryCoupon.Source)
					if err != nil {
						l.Logger.Errorf("PrizeRecord GetCouponInfo is error ，Params is：%d ,%s, err is:%v", queryCoupon.CouponId, queryCoupon.Source, err)
						return nil, consts.GetErrorMsg(r, consts.ErrApiError)
					}
				}
			}
		}
		//签到任务
		if record.PrizeSource == consts.RecordPrizeSourceCheckinTasks {
			checkInCouponId := 0
			checkInCouponSource := ""
			if utils.CheckDev() {
				checkInCouponId = consts.GetRealCouponId(consts.CheckInCouponId)
				checkInCouponSource = consts.GetRealSource(consts.CheckInSource)
			} else {
				checkInCouponId = consts.CheckInCouponId
				checkInCouponSource = consts.CheckInSource
			}
			couponResp, err = service.GetCouponInfo(l.svcCtx, l.ctx, int64(checkInCouponId), checkInCouponSource)
			if err != nil {
				l.Logger.Errorf("PrizeRecord GetCouponInfo is error ，Params is：%d ,%s, err is:%v", int64(checkInCouponId), checkInCouponSource, err)
				return nil, consts.GetErrorMsg(r, consts.ErrApiError)
			}
		}

		if couponResp != nil && len(couponResp.CouponRewardTranslationList) > 0 {
			langVal := lang
			if lang == "cn" {
				langVal = "zh"
			}
			nameVal := couponResp.CouponRewardTranslationList[langVal]
			//默认使用英文
			if nameVal == "" {
				nameVal = couponResp.CouponRewardTranslationList[consts.EN]
			}
			if couponResp.AmountType == 1 {
				prizeName = strings.ReplaceAll(nameVal, "{Amount}", couponResp.Amount)
			} else {
				prizeName = nameVal
			}
		}
		list[i] = &types.PrizeRecord{
			ID:          record.ID,
			PrizeName:   prizeName,
			ReceiveTime: record.UpdatedAt.Unix(),
			Type:        record.Type,
			TypeNum:     record.TypeNum,
			PrizeSource: record.PrizeSource,
		}
	}
	lastPage := 0
	if countVal%int64(req.PerPage) > 0 {
		lastPage = int(countVal/int64(req.PerPage)) + 1
	} else {
		lastPage = int(countVal / int64(req.PerPage))
	}
	resp.CurrentPage = req.Page
	resp.From = fromId
	resp.LastPage = lastPage
	resp.PerPage = req.PerPage
	resp.To = toId
	resp.Total = countVal
	resp.List = list
	return
}
