package web

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"strconv"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

type GetAdvancedTasksLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 查询新客进阶任务信息
func NewGetAdvancedTasksLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAdvancedTasksLogic {
	return &GetAdvancedTasksLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAdvancedTasksLogic) GetAdvancedTasks(req *types.GetBeginnerTasksReq, r *http.Request) (resp *types.GetAdvancedTasksResp, err error) {
	uid := 0
	//打印最终日志
	defer func() {
		utils.LogRouterData(l.ctx, "GetAdvancedTasks", uid, req, resp, err)
	}()
	var respData *types.GetAdvancedTasksResp
	resp = &types.GetAdvancedTasksResp{
		GearTasks: []*types.AdvancedGearTask{},
	}
	userInfo := requestools.GetUserInfo(r)
	if userInfo != nil {
		uid = userInfo.UID
	}
	//接入缓存提高查询速度
	redisKey := fmt.Sprintf(consts.WelfareAdvancedUserTasksKey, uid)
	redisVal, _ := l.svcCtx.Redis.Get(redisKey)
	if redisVal != "" {
		_ = json.Unmarshal([]byte(redisVal), &respData)
	}
	//判断当最大奖励描述字段存在时证明时有效值 缓存中存在直接返回
	if respData != nil {
		resp = respData
		return
	}
	if userInfo == nil || userInfo.UID == 0 {
		regionKey, err := service.GetUnLoginUserBelongRegion(l.svcCtx, l.ctx, r)
		if err != nil {
			l.Logger.Errorf("GetAdvancedTasks GetUnLoginUserBelongRegion is error: %v", err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		//根据地区获取任务列表 进阶
		welfareTaskList, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryWelfareTaskCfgList(l.ctx, regionKey, []int{consts.UserTaskTypeNewbieAdvanced}, 0)
		if err != nil {
			l.Logger.Errorf("GetAdvancedTasks NewWelfareMysqlDBUtil is error ，Params is：%s, err is:%v", regionKey, err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		tasks := make([]*welfare_task_cfgs.WelfareTaskCfgs, 0)
		for _, taskItem := range welfareTaskList {
			advancedTaskInfo := &dao.AdvancedTaskInfo{}
			err := json.Unmarshal([]byte(taskItem.ExtraTaskInfo.String), advancedTaskInfo)
			if err != nil {
				l.Logger.Errorf("GetAdvancedTasks json.Unmarshal([]byte(taskItem.ExtraTaskInfo.String) is error ，Params is：%s,%d,%s, err is:%v", regionKey, taskItem.Id, taskItem.ExtraTaskInfo.String, err)
				return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
			}
			for _, taskCenter := range advancedTaskInfo.SubTasks {
				tasks = append(tasks, &welfare_task_cfgs.WelfareTaskCfgs{
					Id:            taskItem.Id,
					TaskRegionKey: regionKey,
					TaskId:        taskCenter.TaskId,
					Type:          taskItem.Type,
					TaskType:      int64(taskCenter.TaskType),
				})
			}
		}

		//定义进阶任务最大奖励
		allRewardNum := int64(0)
		//获取任务中心的任务ID列表
		taskCenterIds := make([]int64, len(tasks))
		for i, taskInfo := range tasks {
			taskCenterIds[i] = taskInfo.TaskId
		}
		l.Logger.Info("taskCenterIds 数据是：", taskCenterIds)
		//获取任务中心任务信息
		taskCenterMap := map[int64]*task.Task{}
		if len(taskCenterIds) > 0 {
			taskCenterMap, err = service.GetTaskListByTaskIds(l.svcCtx, l.ctx, 0, taskCenterIds)
			if err != nil {
				l.Logger.Errorf("GetAdvancedTasks GetTaskListByTaskIds is error ，Params is：%v, err is:%v", taskCenterIds, err)
				return nil, consts.GetErrorMsg(r, consts.ErrApiError)
			}
		}
		taskList := []*types.AdvancedGearTask{}
		for _, taskInfo := range tasks {
			taskCenterInfo := taskCenterMap[taskInfo.TaskId]
			if taskCenterInfo == nil {
				l.Logger.Errorf("GetAdvancedTasks taskCenterInfo is nil ，taskCenterInfo is：%v, task_center_id is:%d", taskCenterInfo, taskInfo.TaskId)
				return nil, consts.GetErrorMsg(r, consts.ErrApiError)
			}
			if taskInfo.TaskId > 0 && (taskCenterMap[taskInfo.TaskId] == nil || taskCenterMap[taskInfo.TaskId].Title == "") {
				l.Logger.Warnf("GetAdvancedTasks center task failed, taskCenterId is:%d 任务中心数据存在或状态异常", taskInfo.TaskId)
				continue
			}
			prizeList := make([]*types.PrizeInfo, 0, 10)
			taskDepositNum := int64(0)
			taskTradingNum := int64(0)
			//从规则中获取奖品，任务入金，任务交易信息
			if len(taskCenterInfo.RuleInfo) < 2 {
				//只打印不报错
				l.Logger.Warnf("GetAdvancedTasks taskCenterInfo.RuleInfo len failed ，taskCenterInfo is：%v, err is:%v", taskCenterInfo, err)
			} else {
				for _, ruleInfo := range taskCenterInfo.RuleInfo {
					if len(ruleInfo.Conditions) > 0 {
						//入金规则
						if ruleInfo.Mark == consts.TaskCenterMarkAppRecharge {
							taskDepositNum = ruleInfo.Conditions[0].Min
							if len(ruleInfo.Conditions[0].ConditionDetail) > 1 || taskDepositNum == 0 {
								for _, conditionDetail := range ruleInfo.Conditions[0].ConditionDetail {
									if utils.ContainsArray(consts.GetTaskCenterMinMarks(), conditionDetail.Mark) {
										taskDepositNum = conditionDetail.Min
									}
								}
							}
						}
						//交易规则
						if ruleInfo.Mark == consts.TaskCenterMarkAppFuturesSpotAssembly {
							taskTradingNum = ruleInfo.Conditions[0].Min
							if len(ruleInfo.Conditions[0].ConditionDetail) > 1 || taskTradingNum == 0 {
								for _, conditionDetail := range ruleInfo.Conditions[0].ConditionDetail {
									if utils.ContainsArray(consts.GetTaskCenterMinMarks(), conditionDetail.Mark) {
										taskDepositNum = conditionDetail.Min
									}
								}
							}
						}
						p := map[string]interface{}{}
						prizeExt := task.PrizeExt{}
						//只查询卡劵信息
						if ruleInfo.Conditions[0].PrizeType == consts.PrizeTypeCoupon {
							_ = json.Unmarshal([]byte(ruleInfo.Conditions[0].PrizeInfo), &p)
							prizeExt = task.PrizeExt{
								CouponID:     int64(p["coupon_id"].(float64)),
								CouponSource: p["coupon_source"].(string),
							}
						}

						if prizeExt.CouponID > 0 && prizeExt.CouponSource != "" {
							couponResp, err := service.GetCouponInfo(l.svcCtx, l.ctx, prizeExt.CouponID, prizeExt.CouponSource)
							if err != nil {
								l.Logger.Errorf("GetAdvancedTasks GetCouponInfo is error ，Params is：%d ,%s, err is:%v", prizeExt.CouponID, prizeExt.CouponSource, err)
								return nil, consts.GetErrorMsg(r, consts.ErrApiError)
							}
							cardType := 0
							validDays := 0
							if couponResp.CouponExtInfo != nil {
								cardType = utils.InterfaceToInt(couponResp.CouponExtInfo["card_type"])
								validDays = utils.InterfaceToInt(couponResp.CouponExtInfo["valid_days"])
							}
							resp.AllRewardCurrency = "USDT"
							if couponResp.Currency != "" && couponResp.Currency != "USDT" {
								//目前福利中心新人任务的币种都是一样的，所以总奖励的币种直接覆盖，后续如果需要修改直接更改AllBeginnerCurrency字段
								resp.AllRewardCurrency = couponResp.Currency
							}
							market, ok := couponResp.CouponExtInfo["market"].(string)
							if !ok {
								market = ""
							}
							prizeList = append(prizeList, &types.PrizeInfo{
								PrizeType:     int64(consts.GetCouponTypeByTypeAndSubType(couponResp.CouponType, market)),
								PrizeNum:      couponResp.Amount,
								CardType:      cardType,
								ValidDays:     validDays,
								PrizeCurrency: couponResp.Currency,
							})
						}
					} else {
						//只打印不报错
						l.Logger.Warnf("GetAdvancedTasks len(ruleInfo.Conditions) is error ，taskCenterInfo is：%v, err is:%v", taskCenterInfo, err)
					}
				}
			}
			gearAllPrizeNum := 0
			for _, prize := range prizeList {
				//排除vip
				if prize.PrizeType != consts.PointsShopTypeVipCard {
					prizeNum := utils.ParseInt(prize.PrizeNum)
					gearAllPrizeNum += prizeNum
				}
			}
			taskList = append(taskList, &types.AdvancedGearTask{
				IsFinishGear:    0,
				WelfareTaskID:   taskInfo.Id,
				TaskCenterID:    taskInfo.TaskId,
				GearAllPrizeNum: gearAllPrizeNum,
				PrizeList:       prizeList,
				TaskDepositNum:  taskDepositNum,
				UserDepositNum:  0,
				TaskTradingNum:  taskTradingNum,
				UserTradingNum:  0,
			})
		}
		for i, taskInfo := range taskList {
			//最大奖励是最后一个任务的奖励
			if i == len(taskList)-1 {
				allRewardNum += int64(taskInfo.GearAllPrizeNum)
			}
		}
		resp.AllRewardNum = allRewardNum
		resp.GearTasks = taskList
	} else {
		if req.ConstID == "" {
			l.Logger.Infof("GetAdvancedTasks req invalid parameter: %s", req.ConstID)
			return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
		}
		uid = userInfo.UID
		//登陆状态根据用户信息查询查询任务列表
		//赋值描述中获取的最大奖励
		userTasks, err := dao.NewWelfareMysqlDBUtil(l.svcCtx.WelfareDB).QueryUserTaskList(l.ctx, userInfo.UID, []int{consts.UserTaskTypeNewbieAdvanced}, 0, 0)
		if err != nil {
			l.Logger.Errorf("GetAdvancedTasks QueryUserTaskList is err,params is:%d ，err is：%v", userInfo.UID, err)
			return nil, consts.GetErrorMsg(r, consts.ErrDbError)
		}
		taskCenterIds := make([]int64, len(userTasks))
		//获取第一个进阶任务的任务ID和业务ID
		firstTaskCenterId := int64(0)
		firstUserTakId := int64(0)
		for i, userTask := range userTasks {
			if firstTaskCenterId == 0 {
				firstTaskCenterId = userTask.TaskCenterId
				firstUserTakId = userTask.Id
			}
			taskCenterIds[i] = userTask.TaskCenterId
		}
		//获取任务中心任务信息
		taskCenterMap := map[int64]*task.Task{}
		if len(taskCenterIds) > 0 {
			taskCenterMap, err = service.GetTaskListByTaskIds(l.svcCtx, l.ctx, uid, taskCenterIds)
			if err != nil {
				l.Logger.Errorf("GetAdvancedTasks GetTaskListByTaskIds is error ，Params is：%v, err is:%v", taskCenterIds, err)
				return nil, consts.GetErrorMsg(r, consts.ErrApiError)
			}
		}

		//获取多规则任务用户的净入金和交易量
		mulRuleRecordScheduleReq := &task.RecordScheduleRequest{
			UserID:       int64(userInfo.UID),
			TaskID:       firstTaskCenterId,
			BusinessType: consts.WelfareTaskBusinessType,
			BusinessID:   strconv.FormatInt(firstUserTakId, 10),
		}
		mulRuleRecordScheduleResp, err := task.NewClient().MulRuleRecordSchedule(l.ctx, mulRuleRecordScheduleReq)
		if err != nil {
			//只打印不返回
			mulRuleRecordScheduleReqByte, _ := json.Marshal(mulRuleRecordScheduleReq)
			l.Logger.Warnf("GetAdvancedTasks task center MulRuleRecordSchedule is error:, params is : %s | err:%v", string(mulRuleRecordScheduleReqByte), err)
			//return nil, consts.GetErrorMsg(r, consts.ErrApiError)
		}
		//用户净入金量
		userRechargeNum := float64(0)
		//用户交易量
		userFuturesSpotAssemblyNum := float64(0)
		if mulRuleRecordScheduleResp != nil && mulRuleRecordScheduleResp.RuleList != nil {
			for _, ruleInfo := range mulRuleRecordScheduleResp.RuleList {
				if ruleInfo.Mark == consts.TaskCenterMarkAppRecharge {
					userRechargeNum = ruleInfo.Amount
				}
				if ruleInfo.Mark == consts.TaskCenterMarkAppFuturesSpotAssembly {
					userFuturesSpotAssemblyNum = ruleInfo.Amount
				}
			}
		}

		//定义进阶任务最大奖励
		allRewardNum := int64(0)
		taskList := []*types.AdvancedGearTask{}
		for _, userTask := range userTasks {
			taskInfo := &welfare_task_cfgs.WelfareTaskCfgs{}
			err = json.Unmarshal([]byte(userTask.Memo), taskInfo)
			if err != nil {
				l.Logger.Errorf("GetAdvancedTasks json.Unmarshal([]byte(userTask.Memo),taskInfo) is err,params is:%s ，err is：%v", userTask.Memo, err)
				return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
			}
			taskCenterInfo := taskCenterMap[userTask.TaskCenterId]
			if taskCenterInfo == nil {
				l.Logger.Errorf("GetAdvancedTasks taskCenterInfo is nil ，taskCenterInfo is：%v, task_center_id is:%d", taskCenterInfo, userTask.TaskCenterId)
				return nil, consts.GetErrorMsg(r, consts.ErrApiError)
			}
			if userTask.TaskCenterId > 0 && (taskCenterMap[userTask.TaskCenterId] == nil || taskCenterMap[userTask.TaskCenterId].Title == "") {
				l.Logger.Warnf("GetAdvancedTasks center task failed, taskCenterId is:%d,user is:%d, 任务中心数据存在或状态异常", userTask.TaskCenterId, uid)
				continue
			}
			prizeList := make([]*types.PrizeInfo, 0, 10)
			taskDepositNum := int64(0)
			taskTradingNum := int64(0)
			//从规则中获取奖品，任务入金，任务交易信息
			if len(taskCenterInfo.RuleInfo) < 2 {
				//只打印不报错
				l.Logger.Warnf("GetAdvancedTasks taskCenterInfo.RuleInfo len error ，taskCenterInfo is：%v, err is:%v", taskCenterInfo, err)
			} else {
				for _, ruleInfo := range taskCenterInfo.RuleInfo {
					if len(ruleInfo.Conditions) > 0 {
						//入金规则
						if ruleInfo.Mark == consts.TaskCenterMarkAppRecharge {
							taskDepositNum = ruleInfo.Conditions[0].Min
							if len(ruleInfo.Conditions[0].ConditionDetail) > 1 || taskDepositNum == 0 {
								for _, conditionDetail := range ruleInfo.Conditions[0].ConditionDetail {
									if utils.ContainsArray(consts.GetTaskCenterMinMarks(), conditionDetail.Mark) {
										taskDepositNum = conditionDetail.Min
									}
								}
							}
						}
						//入金规则
						if ruleInfo.Mark == consts.TaskCenterMarkAppFuturesSpotAssembly {
							taskTradingNum = ruleInfo.Conditions[0].Min
							if len(ruleInfo.Conditions[0].ConditionDetail) > 1 || taskTradingNum == 0 {
								for _, conditionDetail := range ruleInfo.Conditions[0].ConditionDetail {
									if utils.ContainsArray(consts.GetTaskCenterMinMarks(), conditionDetail.Mark) {
										taskDepositNum = conditionDetail.Min
									}
								}
							}
						}
						p := map[string]interface{}{}
						//只有卡劵的时候才解析卡劵信息
						prizeExt := task.PrizeExt{}
						if ruleInfo.Conditions[0].PrizeType == consts.PrizeTypeCoupon {
							_ = json.Unmarshal([]byte(ruleInfo.Conditions[0].PrizeInfo), &p)
							prizeExt = task.PrizeExt{
								CouponID:     int64(p["coupon_id"].(float64)),
								CouponSource: p["coupon_source"].(string),
							}
						}

						if prizeExt.CouponID > 0 && prizeExt.CouponSource != "" {
							couponResp, err := service.GetCouponInfo(l.svcCtx, l.ctx, prizeExt.CouponID, prizeExt.CouponSource)
							if err != nil {
								l.Logger.Errorf("GetAdvancedTasks GetCouponInfo is error ，Params is：%d ,%s, err is:%v", prizeExt.CouponID, prizeExt.CouponSource, err)
								return nil, consts.GetErrorMsg(r, consts.ErrApiError)
							}
							cardType := 0
							validDays := 0
							if couponResp.CouponExtInfo != nil {
								cardType = utils.InterfaceToInt(couponResp.CouponExtInfo["card_type"])
								validDays = utils.InterfaceToInt(couponResp.CouponExtInfo["valid_days"])
							}
							resp.AllRewardCurrency = "USDT"
							if couponResp.Currency != "" && couponResp.Currency != "USDT" {
								//目前福利中心新人任务的币种都是一样的，所以总奖励的币种直接覆盖，后续如果需要修改直接更改AllBeginnerCurrency字段
								resp.AllRewardCurrency = couponResp.Currency
							}
							market, ok := couponResp.CouponExtInfo["market"].(string)
							if !ok {
								market = ""
							}
							prizeList = append(prizeList, &types.PrizeInfo{
								PrizeType:     int64(consts.GetCouponTypeByTypeAndSubType(couponResp.CouponType, market)),
								PrizeNum:      couponResp.Amount,
								CardType:      cardType,
								ValidDays:     validDays,
								PrizeCurrency: couponResp.Currency,
							})
						}
					} else {
						//只打印不报错
						l.Logger.Warnf("GetAdvancedTasks taskCenterInfo.RuleInfo.Conditions len error ，taskCenterInfo is：%v, err is:%v", taskCenterInfo, err)
					}
				}
			}
			//定义当前档位是否完成
			isFinishGear := 0
			//交易量和入金量必须都大于任务的配置数量才算完成
			if userRechargeNum >= float64(taskDepositNum) && userFuturesSpotAssemblyNum >= float64(taskTradingNum) {
				isFinishGear = 1
			}
			gearAllPrizeNum := 0
			for _, prize := range prizeList {
				//排除vip
				if prize.PrizeType != consts.PointsShopTypeVipCard {
					prizeNum := utils.ParseInt(prize.PrizeNum)
					gearAllPrizeNum += prizeNum
				}
			}
			taskList = append(taskList, &types.AdvancedGearTask{
				IsFinishGear:    isFinishGear,
				WelfareTaskID:   taskInfo.Id,
				TaskCenterID:    taskInfo.TaskId,
				PrizeList:       prizeList,
				GearAllPrizeNum: gearAllPrizeNum,
				TaskDepositNum:  taskDepositNum,
				UserDepositNum:  userRechargeNum,
				TaskTradingNum:  taskTradingNum,
				UserTradingNum:  userFuturesSpotAssemblyNum,
			})
		}
		for i, taskInfo := range taskList {
			if i == len(taskList)-1 {
				allRewardNum = int64(taskInfo.GearAllPrizeNum)
			}
		}
		resp.AllRewardNum = allRewardNum
		resp.GearTasks = taskList
	}
	byteVal, err := json.Marshal(resp)
	if err == nil {
		//将结果缓存1分钟
		_ = l.svcCtx.Redis.Setex(redisKey, string(byteVal), 60)
	}
	return
}
