package dao

import (
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	"context"
	"errors"
	"gateio_service_welfare_go/internal/models/welfare_tasks"
)

// tIds 本地任务ID列表
func (w *WelfareMysqlDBUtil) FindListByConditions(ctx context.Context, baseType, taskType, status int, taskId, sourceId int64, tIds []int64) ([]*welfare_tasks.WelfareTasks, error) {
	resp := []*welfare_tasks.WelfareTasks{}
	query := w.DB.WithContext(ctx).Model(&welfare_tasks.WelfareTasks{})
	// 根据条件动态构建查询
	if baseType > 0 {
		query = query.Where("`base_type` = ?", baseType)
	}
	if taskType > 0 {
		query = query.Where("`task_type` = ?", taskType)
	}
	if status > 0 {
		query = query.Where("`status` = ?", status)
	}
	if taskId > 0 {
		query = query.Where("`task_sys_id` = ?", taskId)
	}
	if sourceId > 0 {
		query = query.Where("`source_id` = ?", sourceId)
	}
	if len(tIds) > 0 {
		query = query.Where("`id` in ?", tIds)
	}
	err := query.Order("weight_sort ASC,id ASC").Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}
