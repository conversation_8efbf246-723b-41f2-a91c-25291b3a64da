package dao

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"gorm.io/gorm"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_points_shops_current_limit"
	"gateio_service_welfare_go/internal/svc"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

const (
	// Redis缓存过期时间常量
	redisCurrentLimitTTL = 86400 // 24小时
	// 数据库操作错误前缀
	errDBOperation    = "database operation failed"
	errRedisOperation = "redis operation failed"
)

// HandleRedisCurrentLimit 处理Redis当前限制（减库存操作）
// 返回 true 表示库存扣减成功，false 表示库存不足
func (w *WelfareMysqlDBUtil) HandleRedisCurrentLimit(svcCtx *svc.ServiceContext, ctx context.Context, prizeIdNew int, typeVal int, typeNumNew int, diff int) (bool, error) {
	if err := w.validatePrizeParams(prizeIdNew, typeVal, typeNumNew); err != nil {
		return false, err
	}
	if diff <= 0 {
		return false, fmt.Errorf("diff must be positive, got: %d", diff)
	}

	redisKey := w.GetHandleRedisKey(prizeIdNew, typeVal, typeNumNew)

	// 确保Redis中有库存数据
	if _, err := w.GetRedisCurrentLimit(svcCtx, ctx, prizeIdNew, typeVal, typeNumNew); err != nil {
		return false, fmt.Errorf("failed to initialize redis current limit: %w", err)
	}

	// 原子性减库存操作
	finalStock, err := svcCtx.Redis.IncrbyCtx(ctx, redisKey, int64(-diff))
	if err != nil {
		return false, fmt.Errorf("%s: failed to decrement stock: %w", errRedisOperation, err)
	}

	// 库存不足，回滚操作
	if finalStock < 0 {
		if _, rollbackErr := svcCtx.Redis.IncrbyCtx(ctx, redisKey, int64(diff)); rollbackErr != nil {
			logc.Errorf(ctx, "Failed to rollback inventory for key %s: %v", redisKey, rollbackErr)
		}
		return false, nil
	}

	return true, nil
}

// GetRedisCurrentLimit 获取Redis库存
// 如果Redis中不存在，会从数据库加载并缓存
func (w *WelfareMysqlDBUtil) GetRedisCurrentLimit(svcCtx *svc.ServiceContext, ctx context.Context, prizeIdNew int, typeVal int, typeNumNew int) (int, error) {
	if err := w.validatePrizeParams(prizeIdNew, typeVal, typeNumNew); err != nil {
		return 0, err
	}

	redisKey := w.GetHandleRedisKey(prizeIdNew, typeVal, typeNumNew)

	exists, err := svcCtx.Redis.ExistsCtx(ctx, redisKey)
	if err != nil {
		return 0, fmt.Errorf("%s: failed to check key existence: %w", errRedisOperation, err)
	}

	if !exists {
		return w.initializeRedisStock(svcCtx, ctx, redisKey, prizeIdNew, typeVal, typeNumNew)
	}

	return w.getRedisStockValue(svcCtx, ctx, redisKey)
}

// initializeRedisStock 初始化Redis库存
func (w *WelfareMysqlDBUtil) initializeRedisStock(svcCtx *svc.ServiceContext, ctx context.Context, redisKey string, prizeId, typeVal, typeNum int) (int, error) {
	// 从数据库获取初始库存
	num, err := w.GetCurrentLimitByWhere(svcCtx, ctx, prizeId, typeVal, typeNum)
	if err != nil {
		return 0, fmt.Errorf("failed to get current limit from database: %w", err)
	}

	// 设置Redis缓存
	if err := svcCtx.Redis.Setex(redisKey, strconv.Itoa(num), redisCurrentLimitTTL); err != nil {
		return 0, fmt.Errorf("%s: failed to set cache: %w", errRedisOperation, err)
	}

	return num, nil
}

// getRedisStockValue 从Redis获取库存值
func (w *WelfareMysqlDBUtil) getRedisStockValue(svcCtx *svc.ServiceContext, ctx context.Context, redisKey string) (int, error) {
	numStr, err := svcCtx.Redis.GetCtx(ctx, redisKey)
	if err != nil {
		return 0, fmt.Errorf("%s: failed to get cache: %w", errRedisOperation, err)
	}

	num, err := strconv.Atoi(numStr)
	if err != nil {
		return 0, fmt.Errorf("failed to parse redis value '%s': %w", numStr, err)
	}

	return num, nil
}

// GetHandleRedisKey 获取处理Redis键
func (w *WelfareMysqlDBUtil) GetHandleRedisKey(prizeIdNew int, typeVal int, typeNumNew int) string {
	return fmt.Sprintf(consts.WelfarePrizeCurrentLimitKey, prizeIdNew, typeVal, typeNumNew)
}

// IncrRedisCurrentLimit 增加Redis当前限制
func (w *WelfareMysqlDBUtil) IncrRedisCurrentLimit(svcCtx *svc.ServiceContext, ctx context.Context, prizeIdNew int, typeVal int, typeNumNew int, diff int) (int64, error) {
	if err := w.validatePrizeParams(prizeIdNew, typeVal, typeNumNew); err != nil {
		return 0, err
	}

	redisKey := w.GetHandleRedisKey(prizeIdNew, typeVal, typeNumNew)
	result, err := svcCtx.Redis.IncrbyCtx(ctx, redisKey, int64(diff))
	if err != nil {
		return 0, fmt.Errorf("%s: failed to increment: %w", errRedisOperation, err)
	}

	return result, nil
}

// DelRedisCurrentLimit 删除Redis当前限制缓存
func (w *WelfareMysqlDBUtil) DelRedisCurrentLimit(svcCtx *svc.ServiceContext, ctx context.Context, prizeIdNew int, typeVal int, typeNumNew int) error {
	if err := w.validatePrizeParams(prizeIdNew, typeVal, typeNumNew); err != nil {
		return err
	}

	redisKey := w.GetHandleRedisKey(prizeIdNew, typeVal, typeNumNew)
	if _, err := svcCtx.Redis.DelCtx(ctx, redisKey); err != nil {
		return fmt.Errorf("%s: failed to delete key: %w", errRedisOperation, err)
	}

	return nil
}

// GetCurrentLimitByWhere 获取当前剩余额度
// typeVal: 类型 1点卡 2vip1 3合约体验金 4startup券 5量化体验金 6vip+1
// typeNum: type数量
func (w *WelfareMysqlDBUtil) GetCurrentLimitByWhere(svcCtx *svc.ServiceContext, ctx context.Context, prizeId int, typeVal int, typeNum int) (int, error) {
	if err := w.validatePrizeParams(prizeId, typeVal, typeNum); err != nil {
		return 0, err
	}

	var num int64
	err := w.DB.WithContext(ctx).Model(&welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{}).
		Where("prize_id = ? AND type = ? AND type_num = ?", prizeId, typeVal, typeNum).
		Pluck("num", &num).Error

	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return 0, fmt.Errorf("%s: failed to query current limit: %w", errDBOperation, err)
	}

	return int(num), nil
}

// GetCurrentLimitByWhere 获取当前剩余额度
func (w *WelfareMysqlDBUtil) GetCurrentLimitNumByPrizeId(ctx context.Context, prizeId int64) (int, error) {
	if prizeId <= 0 {
		return 0, fmt.Errorf("invalid prizeId: %d", prizeId)
	}

	var num int64
	err := w.DB.WithContext(ctx).Model(&welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{}).
		Where("prize_id = ?", prizeId).
		Pluck("num", &num).Error

	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return 0, fmt.Errorf("%s: failed to query current limit: %w", errDBOperation, err)
	}

	return int(num), nil
}

// GetCurrentLimitByWhere 获取当前剩余额度
func (w *WelfareMysqlDBUtil) GetCurrentLimitByPrizeId(ctx context.Context, prizeId int64) (*welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit, error) {
	if prizeId <= 0 {
		return nil, fmt.Errorf("invalid prizeId: %d", prizeId)
	}

	resp := &welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{}
	err := w.DB.WithContext(ctx).Model(&welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{}).
		Where("prize_id = ?", prizeId).First(resp).Error

	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}

	return resp, nil
}

// GetCurrentLimitAll 查询当前全部额度
func (w *WelfareMysqlDBUtil) GetCurrentLimitAll(ctx context.Context) ([]*welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit, error) {
	var results []*welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit

	err := w.DB.WithContext(ctx).Model(&welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{}).
		Find(&results).Error
	if err != nil {
		return nil, fmt.Errorf("%s: failed to query all limits: %w", errDBOperation, err)
	}

	return results, nil
}

// UpdateCurrentLimitNum 补库存（直接设置库存数量）
func (w *WelfareMysqlDBUtil) UpdateCurrentLimitNum(ctx context.Context, prizeId int, typeVal int, typeNum int, num int) error {
	if err := w.validatePrizeParams(prizeId, typeVal, typeNum); err != nil {
		return err
	}
	if num < 0 {
		return fmt.Errorf("stock number cannot be negative: %d", num)
	}

	err := w.DB.WithContext(ctx).Model(&welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{}).
		Where("prize_id = ? AND type = ? AND type_num = ?", prizeId, typeVal, typeNum).
		Update("num", num).Error
	if err != nil {
		return fmt.Errorf("%s: failed to update stock: %w", errDBOperation, err)
	}

	return nil
}

// IncrCurrentLimitByPrizeId 增加库存
// 如果记录不存在会自动创建，如果存在则增加库存
func (w *WelfareMysqlDBUtil) IncrCurrentLimitByPrizeId(ctx context.Context, prizeId int, typeVal int, typeNum int, num int) error {
	if err := w.validatePrizeParams(prizeId, typeVal, typeNum); err != nil {
		return err
	}

	// 先尝试更新已存在的记录
	result := w.DB.WithContext(ctx).Model(&welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{}).
		Where("prize_id = ? AND type = ? AND type_num = ?", prizeId, typeVal, typeNum).
		Update("num", gorm.Expr("num + ?", num))

	if result.Error != nil {
		return fmt.Errorf("%s: failed to increment stock: %w", errDBOperation, result.Error)
	}

	// 如果没有记录被更新，说明记录不存在，需要创建新记录
	if result.RowsAffected == 0 {
		return w.createNewStockRecord(ctx, prizeId, typeVal, typeNum, num)
	}

	return nil
}

// createNewStockRecord 创建新的库存记录
func (w *WelfareMysqlDBUtil) createNewStockRecord(ctx context.Context, prizeId, typeVal, typeNum, num int) error {
	data := &welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{
		PrizeId: int64(prizeId),
		Type:    int64(typeVal),
		TypeNum: int64(typeNum),
		Num:     int64(num),
	}

	if err := w.DB.WithContext(ctx).Create(data).Error; err != nil {
		return fmt.Errorf("%s: failed to create stock record: %w", errDBOperation, err)
	}

	return nil
}

// DecrCurrentLimitByPrizeId 减少库存
// 只有在库存充足的情况下才会执行减库存操作
func (w *WelfareMysqlDBUtil) DecrCurrentLimitByPrizeId(ctx context.Context, prizeId int, typeVal int, typeNum int, num int) error {
	if err := w.validatePrizeParams(prizeId, typeVal, typeNum); err != nil {
		return err
	}

	result := w.DB.WithContext(ctx).Model(&welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{}).
		Where("prize_id = ? AND type = ? AND type_num = ? AND num >= ?", prizeId, typeVal, typeNum, num).
		Update("num", gorm.Expr("num - ?", num))

	if result.Error != nil {
		return fmt.Errorf("%s: failed to decrement stock: %w", errDBOperation, result.Error)
	}

	// 检查是否有行被更新
	if result.RowsAffected == 0 {
		return fmt.Errorf("insufficient inventory or record not found for prize_id=%d, type=%d, type_num=%d, required=%d",
			prizeId, typeVal, typeNum, num)
	}

	return nil
}

// validatePrizeParams 验证奖品相关参数
func (w *WelfareMysqlDBUtil) validatePrizeParams(prizeId, typeVal, typeNum int) error {
	//if prizeId <= 0 {
	//	return fmt.Errorf("invalid prize_id: %d", prizeId)
	//}
	//if typeVal <= 0 {
	//	return fmt.Errorf("invalid type: %d", typeVal)
	//}
	//if typeNum < 0 {
	//	return fmt.Errorf("invalid type_num: %d", typeNum)
	//}
	return nil
}

func (w *WelfareMysqlDBUtil) InsertCurrentLimit(ctx context.Context, data *welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit) (int64, error) {
	err := w.DB.WithContext(ctx).Create(&data).Error
	return data.Id, err
}

func (w *WelfareMysqlDBUtil) UpdateCurrentLimit(ctx context.Context, data *welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit) (bool, error) {
	err := w.DB.WithContext(ctx).Model(&welfare_points_shops_current_limit.WelfarePointsShopsCurrentLimit{}).Where("prize_id = ?", data.PrizeId).Updates(data).Update("num", data.Num).Error
	if err != nil {
		return false, err
	}
	return true, nil
}
