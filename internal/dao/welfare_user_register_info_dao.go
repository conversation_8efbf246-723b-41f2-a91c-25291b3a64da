package dao

import (
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_user_register_info"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
)

// 查询用户注册快照单条信息
func (w *WelfareMysqlDBUtil) GetWelfareUserRegisterInfo(svcCtx *svc.ServiceContext, ctx context.Context, uid int64) (*welfare_user_register_info.WelfareUserRegisterInfo, error) {
	var resp welfare_user_register_info.WelfareUserRegisterInfo
	//先从缓存中获取
	redisKey := fmt.Sprintf(consts.WelfareUserRegisterInfoKey, uid)
	redisVal, _ := svcCtx.Redis.Get(redisKey)
	if redisVal != "" {
		_ = json.Unmarshal([]byte(redisVal), &resp)
		if resp.Uid == uid {
			return &resp, nil
		}
	}
	err := w.DB.WithContext(ctx).Model(&welfare_user_register_info.WelfareUserRegisterInfo{}).Where("`uid` = @uid", sql.Named("uid", uid)).Take(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	//置换缓存有结果的数据
	if resp.Uid > 0 {
		byteVal, err := json.Marshal(resp)
		if err == nil {
			//缓存7天 7*24*60*60 = 604800
			_ = svcCtx.Redis.Setex(fmt.Sprintf(consts.TaskCenterTaskInfoKey, uid), string(byteVal), 604800)
		}
	}
	return &resp, nil
}

// 查询用户注册快照单条信息
func (w *WelfareMysqlDBUtil) GetWelfareUserRegisterDbDetail(svcCtx *svc.ServiceContext, ctx context.Context, uid int64) (*welfare_user_register_info.WelfareUserRegisterInfo, error) {
	var resp welfare_user_register_info.WelfareUserRegisterInfo
	err := w.DB.WithContext(ctx).Model(&welfare_user_register_info.WelfareUserRegisterInfo{}).Where("`uid` = @uid", sql.Named("uid", uid)).Take(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return &resp, nil
}

func (w *WelfareMysqlDBUtil) InsertUserRegisterInfo(ctx context.Context, data *welfare_user_register_info.WelfareUserRegisterInfo) (int64, error) {
	err := w.DB.WithContext(ctx).Create(&data).Error
	return data.Id, err
}

// 根据字段和条件更新快照信息
func (w *WelfareMysqlDBUtil) UpdateUserRegisterInfo(svcCtx *svc.ServiceContext, ctx context.Context, uid int64, updateMap map[string]interface{}) error {
	if len(updateMap) < 1 {
		return nil
	}
	err := w.DB.WithContext(ctx).Model(&welfare_user_register_info.WelfareUserRegisterInfo{}).Where("uid = ?", uid).Updates(updateMap).Error
	if err != nil {
		return err
	}
	utils.DelayDelRedisKey(svcCtx, fmt.Sprintf(consts.WelfareUserRegisterInfoKey, uid), 200, 3)
	return nil
}

// 查询用户快照列表，用于定时任务查询
func (w *WelfareMysqlDBUtil) QueryUserRegisterList(svcCtx *svc.ServiceContext, ctx context.Context, lastId, advancedEndTime int64, pageSize int) ([]*welfare_user_register_info.WelfareUserRegisterInfo, error) {
	resp := []*welfare_user_register_info.WelfareUserRegisterInfo{}
	err := w.DB.WithContext(ctx).Model(&welfare_user_register_info.WelfareUserRegisterInfo{}).
		Select([]string{"id", "uid", "newbie_end_time", "advanced_end_time"}).Where("id > ?", lastId).
		Where("finish_advanced_receive = ?", consts.FinishAdvancedReceiveNo).
		Where("advanced_end_time < ?", advancedEndTime).Where("advanced_end_time > 0").
		Order("id asc").Limit(pageSize).Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}
