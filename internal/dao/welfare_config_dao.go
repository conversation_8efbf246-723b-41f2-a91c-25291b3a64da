package dao

import (
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	"context"
	"database/sql"
	"errors"
	"gateio_service_welfare_go/internal/models/welfare_config"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
)

// 根据名称获取配置信息
func (d *WelfareMysqlDBUtil) GetWelfareConfigByName(ctx context.Context, name string) (*welfare_config.WelfareConfig, error) {
	var resp welfare_config.WelfareConfig
	err := d.DB.WithContext(ctx).Model(&welfare_config.WelfareConfig{}).Where("`name` = @name", sql.Named("name", name)).Take(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return &resp, nil
}

func (d *WelfareMysqlDBUtil) SetWelfareConfig(ctx context.Context, name, value string) (int64, error) {
	configItem, err := d.GetWelfareConfigByName(ctx, name)
	if err != nil {
		return 0, err
	}

	if configItem.Id > 0 {
		return d.UpdateConfigData(ctx, configItem.Id, value)
	}

	return d.InsertConfigData(ctx, name, value)
}

func (d *WelfareMysqlDBUtil) InsertConfigData(ctx context.Context, name, value string) (int64, error) {
	data := &welfare_config.WelfareConfig{
		Name:   name,
		Config: value,
	}
	err := d.DB.WithContext(ctx).Create(data).Error
	return data.Id, err
}

func (d *WelfareMysqlDBUtil) UpdateConfigData(ctx context.Context, id int64, value string) (int64, error) {
	if id == 0 || value == "" {
		return 0, errors.New("invalid params")
	}
	err := d.DB.WithContext(ctx).Model(&welfare_config.WelfareConfig{}).Where("id = ?", id).Update("config", value).Error
	if err != nil {
		return 0, err
	}
	return id, nil
}

func (w *WelfareMysqlDBUtil) updateOrCreate(ctx context.Context, id, status int64) (bool, error) {
	if id == 0 || status == 0 {
		return false, errors.New("invalid params")
	}
	err := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{}).Where("id = ?", id).Update("status", status).Error
	if err != nil {
		return false, err
	}
	return true, nil
}
