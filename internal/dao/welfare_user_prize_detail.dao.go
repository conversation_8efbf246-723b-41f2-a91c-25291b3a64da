package dao

import (
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

// 福利中心奖品记录详情
type WelfareUserPrizeDetail struct {
	// 自增主键ID
	ID uint `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	// 用户UID
	UID uint64 `gorm:"not null;default:0;column:uid" json:"uid"`
	// welfare_user_tasks表的主键ID
	UserPrizeID uint64 `gorm:"not null;default:0;column:user_prize_id" json:"user_prize_id"`
	// 备注信息
	Memo string `gorm:"type:text;not null;column:memo" json:"memo"`
	// 详情信息
	Detail string `gorm:"type:text;not null;column:detail;charset:utf8mb4" json:"detail"`
	// 创建时间
	CreatedAt time.Time `gorm:"not null;default:current_timestamp;column:created_at" json:"created_at"`
	// 更新时间（自动更新）
	UpdatedAt time.Time `gorm:"not null;default:current_timestamp;column:updated_at;autoUpdateTime" json:"updated_at"`
}

// 获取分表名
func GetSubTableName(baseTable string, t time.Time) string {
	return fmt.Sprintf("%s_%d", baseTable, t.Year())
}

// 插入奖品详情
func (w *WelfareMysqlDBUtil) InsertUserPrizeDetail(ctx context.Context, uid int64, userPrizeId int64, memo, detail interface{}, createdAt, updatedAt time.Time) (int64, error) {
	// 序列化 memo 和 detail 为 JSON
	memoJson, _ := json.Marshal(memo)
	detailJson, _ := json.Marshal(detail)

	data := WelfareUserPrizeDetail{
		UID:         uint64(uid),
		UserPrizeID: uint64(userPrizeId),
		Memo:        string(memoJson),
		Detail:      string(detailJson),
	}

	currentTime := time.Now()
	// 判断 createdAt 是否为零值（未设置）
	if !createdAt.IsZero() {
		data.CreatedAt = createdAt
		currentTime = createdAt
	}
	if !updatedAt.IsZero() {
		data.UpdatedAt = updatedAt
	}

	// 获取分表名
	tableName := GetSubTableName("welfare_user_prize_detail", currentTime)

	err := w.DB.WithContext(ctx).Table(tableName).Create(&data).Error

	return int64(data.ID), err
}

func (w *WelfareMysqlDBUtil) UpdateExchangeDetailById(ctx context.Context, detailRecordId int, detail map[string]string, time time.Time) (bool, error) {
	tableName := GetSubTableName("welfare_user_prize_detail", time)
	detailByte, _ := json.Marshal(detail)
	err := w.DB.WithContext(ctx).Table(tableName).Where("id = ?", detailRecordId).Update("detail", string(detailByte)).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

func (w *WelfareMysqlDBUtil) GetUserPrizeDetail(ctx context.Context, uid int64, userPrizeId int64) (*WelfareUserPrizeDetail, error) {
	tableName := GetSubTableName("welfare_user_prize_detail", time.Now())
	resp := &WelfareUserPrizeDetail{}
	err := w.DB.WithContext(ctx).Table(tableName).Where("uid = ?", uid).
		Where("user_prize_id = ?", userPrizeId).First(resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, err
}
