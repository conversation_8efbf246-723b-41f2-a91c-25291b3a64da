package dao

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_user_points"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"strconv"

	"gorm.io/gorm"
)

// GetPointsByUid gets user points from cache or database
func (d *WelfareMysqlDBUtil) GetPointsByUid(svcCtx *svc.ServiceContext, ctx context.Context, uid int64) (int64, error) {
	redisKey := fmt.Sprintf(consts.UserPoints, uid)

	// Try to get from Redis first
	pointsStr, err := svcCtx.Redis.Get(redisKey)
	if err == nil && pointsStr != "" {
		points, err := strconv.ParseInt(pointsStr, 10, 64)
		if err == nil {
			return points, nil
		}
	}

	// Get from database
	var points int64
	err = d.DB.WithContext(ctx).Model(&welfare_user_points.WelfareUserPointsNew{}).
		Select("points").
		Where("uid = ?", uid).
		Pluck("points", &points).Error
	if err != nil {
		return 0, err
	}

	// 将用户积分缓存到Redis中,缓存时间为10分钟(600秒)
	// 即使缓存失败也不影响主流程,只记录错误日志
	if err := svcCtx.Redis.Setex(redisKey, strconv.FormatInt(points, 10), 600); err != nil {
		logc.Infof(ctx, "Failed to cache points in Redis: %v\n", err)
	}

	return points, nil
}

func (d *WelfareMysqlDBUtil) IncrUserPoints(ctx context.Context, uid int, points int64) (int64, error) {
	userPointModel := &welfare_user_points.WelfareUserPointsNew{}
	err := d.DB.WithContext(ctx).Where("uid = ?", uid).First(userPointModel).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		logc.Errorf(ctx, "IncrUserPoints query error: %v", err)
		return 0, err
	}
	//如果记录不存在则创建
	if userPointModel.Id == 0 {
		userPointData := &welfare_user_points.WelfareUserPointsNew{
			Uid:    int64(uid),
			Points: points,
		}
		err = d.DB.WithContext(ctx).Model(userPointModel).Create(userPointData).Error
		if err != nil {
			logc.Errorf(ctx, "IncrUserPoints Create is err: %v", err)
			return 0, err
		}
		*userPointModel = *userPointData
	} else {
		err = d.DB.WithContext(ctx).Model(userPointModel).Update("points", gorm.Expr("points + ?", points)).Error
		if err != nil {
			logc.Errorf(ctx, "IncrUserPoints Update is err: %v", err)
			return 0, err
		}
	}
	return userPointModel.Id, nil
}

// DecrUserPoints 减少用户积分
func (d *WelfareMysqlDBUtil) DecrUserPoints(ctx context.Context, uid int64, points int64) error {

	result := d.DB.WithContext(ctx).Model(&welfare_user_points.WelfareUserPointsNew{}).
		Where("uid = ?", uid).
		Update("points", gorm.Expr("points - ?", points))

	if result.Error != nil {
		logc.Errorf(ctx, "Failed to decrement points without check for uid %d: %v", uid, result.Error)
		return fmt.Errorf("failed to decrement points: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		logc.Warnf(ctx, "No user points record found for uid %d", uid)
		return fmt.Errorf("user points record not found for uid %d", uid)
	}

	logc.Infof(ctx, "Successfully decremented %d points without check for uid %d", points, uid)
	return nil
}

// DecrUserPoints 减少用户并修改用户积分的清0时间
func (d *WelfareMysqlDBUtil) UpdateUserDecrPoints(ctx context.Context, uid int64, points int64, cleanTime int64) error {
	updateMap := map[string]interface{}{}
	updateMap["points"] = gorm.Expr("points - ?", points)
	updateMap["clean_point_time"] = cleanTime
	err := d.DB.WithContext(ctx).Model(&welfare_user_points.WelfareUserPointsNew{}).
		Where("uid = ?", uid).Updates(updateMap).Error
	if err != nil {
		logc.Errorf(ctx, "Failed to UpdateUserDecrPoints points without check for uid %d: %v", uid, err)
		return err
	}
	return nil
}

// DelRedisUserPoints 删除用户积分缓存
func (d *WelfareMysqlDBUtil) DelRedisUserPoints(svcCtx *svc.ServiceContext, uid int64) {
	utils.DelayDelRedisKey(svcCtx, fmt.Sprintf(consts.UserPoints, uid), 200, 3)
}
