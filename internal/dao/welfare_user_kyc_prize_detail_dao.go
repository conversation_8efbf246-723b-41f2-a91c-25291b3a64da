package dao

import (
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	"context"
	"errors"
	"gateio_service_welfare_go/internal/models/welfare_user_kyc_prize_detail"
)

func (w *WelfareMysqlDBUtil) GetPrizeDetailByKycAndTaskId(ctx context.Context, photoidEncryption string, taskCenterId int64) (*welfare_user_kyc_prize_detail.WelfareUserKycPrizeDetail, error) {
	var resp *welfare_user_kyc_prize_detail.WelfareUserKycPrizeDetail
	query := w.DB.WithContext(ctx).Model(&welfare_user_kyc_prize_detail.WelfareUserKycPrizeDetail{})
	query = query.Where("`photoid_encryption` = ?", photoidEncryption)
	query = query.Where("`task_center_id` = ?", taskCenterId)
	err := query.First(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 插入函数
func (w *WelfareMysqlDBUtil) InsertUserKycPrizeDetail(ctx context.Context, uid, prizeID, taskCenterId int, photoIDEncryption, prizeSource string) (int, error) {
	record := welfare_user_kyc_prize_detail.WelfareUserKycPrizeDetail{
		Uid:               int64(uid),
		PhotoidEncryption: photoIDEncryption,
		PrizeSource:       prizeSource,
		PrizeId:           int64(prizeID),
		TaskCenterId:      int64(taskCenterId),
	}

	err := w.DB.WithContext(ctx).Create(&record).Error

	if err != nil || record.Id <= 0 {
		return 0, err
	}
	return int(record.Id), nil
}
