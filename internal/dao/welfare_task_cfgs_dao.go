package dao

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"

	"gateio_service_welfare_go/internal/consts"

	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
	"bitbucket.org/gateio/gateio-lib-base-go/environment"
)

// 注册任务额外信息
type RegisterTaskInfo struct {
	Source       string   `json:"source"`        // 用户source
	CouponId     int64    `json:"coupon_id"`     // 卡券id
	ExtraRewards []Reward `json:"extra_rewards"` // 额外奖励
}
type Reward struct {
	RewardId      int64  `json:"extra_reward_id"`       // 奖励id
	RewardType    int32  `json:"extra_reward_type"`     // 奖励类型 1.卡券 2.积分
	RewardSubType int32  `json:"extra_reward_sub_type"` // 奖励子类型 如券类型
	RewardName    string `json:"extra_reward_name"`     // 奖励名称
	RewardValue   string `json:"extra_reward_value"`    // 奖励价值
}

// 其他入门任务额外信息
type WelcomeTaskInfo struct {
	LimitDays    int32    `json:"limit_days"`    // 限时天数
	LimitRewards []Reward `json:"limit_rewards"` // 限时奖励
}

// 进阶任务额外信息
type AdvancedTaskInfo struct {
	SubTasks []AdvancedSubTaskInfo `json:"sub_tasks"` // 子任务列表
}

type AdvancedSubTaskInfo struct {
	TaskId        int64      `json:"task_id"`        // 任务id
	TaskName      string     `json:"task_name"`      // 任务名称
	TaskType      int32      `json:"task_type"`      // 任务类型
	EffectiveTime string     `json:"effective_time"` // 生效时间
	TaskRules     []TaskRule `json:"task_rules"`     // 规则
	Rewards       []Reward   `json:"rewards"`        // 奖励
	Extra_Rewards []Reward   `json:"extra_rewards"`  // 额外奖励
}

type TaskRule struct {
	RuleDesc string `json:"rule_desc"` // 规则描述
	Goal     int32  `json:"goal"`      // 规则目标
}

// 根据地区和类型查询福利中心任务列表
func (w *WelfareMysqlDBUtil) QueryWelfareTaskCfgList(ctx context.Context, regionId string, welfareTaskTypes []int, identityId int) ([]*welfare_task_cfgs.WelfareTaskCfgs, error) {
	resp := []*welfare_task_cfgs.WelfareTaskCfgs{}
	query := w.DB.WithContext(ctx).Model(&welfare_task_cfgs.WelfareTaskCfgs{}).
		Where("status in (?)", []int{consts.EffectiveStatusValid, consts.EffectiveStatusReview}).
		Where("online_status = ?", consts.StatusOnline)
	// 预发环境只查pre_env = 1的数据
	if environment.IsPre() {
		query = query.Where("pre_env = 1")
	} else {
		query = query.Where("pre_env = 0")
	}
	if regionId != "" {
		query = query.Where("task_region_key = ?", regionId)
	}
	if identityId != 0 {
		query = query.Where("task_identity_id = ?", identityId)
	}
	if len(welfareTaskTypes) > 0 {
		query = query.Where("type in (?)", welfareTaskTypes)
	}
	// 正序
	err := query.Order("sort asc").Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	taskTypeMap := map[int64][]*welfare_task_cfgs.WelfareTaskCfgs{}
	for _, taskInfo := range resp {
		//将注册任务合并到入门任务中
		taskType := taskInfo.Type
		if taskType == consts.UserTaskTypeNewbieRegister {
			taskType = consts.UserTaskTypeNewbieGuide
		}
		if taskTypeMap[taskType] == nil {
			taskTypeMap[taskType] = []*welfare_task_cfgs.WelfareTaskCfgs{}
		}
		taskTypeMap[taskType] = append(taskTypeMap[taskType], taskInfo)
	}
	welfareTaskTypeList := []int{}
	for _, taskType := range welfareTaskTypes {
		//过滤掉参数中的注册任务
		if taskType != consts.UserTaskTypeNewbieRegister {
			welfareTaskTypeList = append(welfareTaskTypeList, taskType)
		}
	}
	//用模块去区分是否需要普通地区兜底
	for _, welfareTaskType := range welfareTaskTypeList {
		if taskTypeMap[int64(welfareTaskType)] == nil || len(taskTypeMap[int64(welfareTaskType)]) == 0 {
			typeResp := []*welfare_task_cfgs.WelfareTaskCfgs{}
			queryVal := w.DB.WithContext(ctx).Model(&welfare_task_cfgs.WelfareTaskCfgs{}).
				Where("status in (?)", []int{consts.EffectiveStatusValid, consts.EffectiveStatusReview}).
				Where("online_status = ?", consts.StatusOnline).
				Where("task_region_key = ?", consts.NewbieTaskRegionCommon)
			// 预发环境只查pre_env = 1的数据
			if environment.IsPre() {
				queryVal = queryVal.Where("pre_env = 1")
			} else {
				queryVal = queryVal.Where("pre_env = 0")
			}
			if welfareTaskType == consts.UserTaskTypeNewbieGuide {
				queryVal = queryVal.Where("type in (?)", []int{consts.UserTaskTypeNewbieGuide, consts.UserTaskTypeNewbieRegister})
			} else {
				queryVal = queryVal.Where("type = ?", welfareTaskType)
			}
			if identityId != 0 {
				queryVal = queryVal.Where("task_identity_id = ?", identityId)
			}
			// 正序
			err = queryVal.Order("sort asc").Find(&typeResp).Error
			if err != nil && !errors.Is(err, gormc.ErrNotFound) {
				return nil, err
			}
			resp = append(resp, typeResp...)
		}
	}
	list := make([]*welfare_task_cfgs.WelfareTaskCfgs, 0, len(resp))
	for _, v := range resp {
		// 待审核状态 过滤掉待审核并且没有快照信息的任务
		if v.Status == consts.EffectiveStatusReview {
			cfg := &welfare_task_cfgs.WelfareTaskCfgs{}
			_ = json.Unmarshal([]byte(v.ApprovedSnapshot.String), cfg)
			if cfg.Id > 0 {
				list = append(list, cfg)
			}
		} else {
			list = append(list, v)
		}
	}
	return list, nil
}

// 根据条件获取任务详情
func (w *WelfareMysqlDBUtil) GetWelfareTaskCfgInfo(ctx context.Context, regionId string, welfareTaskType, identityId, taskType int) (*welfare_task_cfgs.WelfareTaskCfgs, error) {
	resp := &welfare_task_cfgs.WelfareTaskCfgs{}
	query := w.DB.WithContext(ctx).Model(&welfare_task_cfgs.WelfareTaskCfgs{}).
		Where("status in (?)", []int{consts.EffectiveStatusValid, consts.EffectiveStatusReview}).Where("online_status = ?", consts.StatusOnline)
	// 预发环境只查pre_env = 1的数据
	if environment.IsPre() {
		query = query.Where("pre_env = 1")
	} else {
		query = query.Where("pre_env = 0")
	}
	if regionId != "" {
		query = query.Where("task_region_key = ?", regionId)
	}
	if identityId != 0 {
		query = query.Where("task_identity_id = ?", identityId)
	}
	if welfareTaskType > 0 {
		query = query.Where("`type` = ?", welfareTaskType)
	}
	if taskType > 0 {
		query = query.Where("task_type = ?", taskType)
	}
	// 正序
	err := query.Order("sort asc").First(resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	// 待审核状态 过滤掉待审核并且没有快照信息的任务
	if resp.Status == consts.EffectiveStatusReview {
		cfg := &welfare_task_cfgs.WelfareTaskCfgs{}
		_ = json.Unmarshal([]byte(resp.ApprovedSnapshot.String), cfg)
		if cfg.Id > 0 {
			return cfg, nil
		} else {
			// 返回空数据
			return &welfare_task_cfgs.WelfareTaskCfgs{}, nil
		}
	}
	return resp, nil
}

// 根据task_id获取任务列表
func (w *WelfareMysqlDBUtil) QueryWelfareTaskCfgListByIds(ctx context.Context, welfare_task_ids []int64) ([]*welfare_task_cfgs.WelfareTaskCfgs, error) {
	if len(welfare_task_ids) == 0 {
		return nil, errors.New("no ids provided")
	}
	resp := []*welfare_task_cfgs.WelfareTaskCfgs{}
	query := w.DB.WithContext(ctx).Model(&welfare_task_cfgs.WelfareTaskCfgs{})

	err := query.Where("id in (?)", welfare_task_ids).Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 根据task_id获取任务列表
func (w *WelfareMysqlDBUtil) QueryWelfareTaskCfgListById(ctx context.Context, welfare_task_id int64) (*welfare_task_cfgs.WelfareTaskCfgs, error) {
	if welfare_task_id == 0 {
		return nil, errors.New("no ids provided")
	}
	resp := &welfare_task_cfgs.WelfareTaskCfgs{}
	query := w.DB.WithContext(ctx).Model(&welfare_task_cfgs.WelfareTaskCfgs{})

	err := query.Where("`id` = ?", welfare_task_id).First(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (w *WelfareMysqlDBUtil) QueryWelfareTaskCfgListByStatus(ctx context.Context, regionId string, welfareTaskTypes []int, effectiveStatus int, identityId int) ([]*welfare_task_cfgs.WelfareTaskCfgs, error) {
	resp := []*welfare_task_cfgs.WelfareTaskCfgs{}
	query := w.DB.WithContext(ctx).Model(&welfare_task_cfgs.WelfareTaskCfgs{})
	// 预发环境只查pre_env = 1的数据
	if environment.IsPre() {
		query = query.Where("pre_env = 1")
	} else {
		query = query.Where("pre_env = 0")
	}
	if regionId != "" {
		query = query.Where("task_region_key = ?", regionId)
	}
	if effectiveStatus != 0 {
		if effectiveStatus == consts.EffectiveStatusReview {
			query = query.Where("status = ?", consts.ReviewStatusInit)
		} else if effectiveStatus == consts.EffectiveStatusValid {
			query = query.Where("status = ?", consts.ReviewStatusApproval).Where("online_status = ?", consts.StatusOnline)
		} else if effectiveStatus == consts.EffectiveStatusInValid {
			query = query.Where("status = ?", consts.ReviewStatusApproval).Where("online_status = ?", consts.StatusOffline)
		}
	}
	if identityId != 0 {
		query = query.Where("task_identity_id = ?", identityId)
	}
	if len(welfareTaskTypes) > 0 {
		query = query.Where("type in (?)", welfareTaskTypes)
	}
	// 正序
	err := query.Order("sort asc").Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (w *WelfareMysqlDBUtil) QueryWelfareTaskCfgListByRegionAndType(ctx context.Context, regionId string, welfareTaskType int, identityId int) ([]*welfare_task_cfgs.WelfareTaskCfgs, error) {
	resp := []*welfare_task_cfgs.WelfareTaskCfgs{}
	query := w.DB.WithContext(ctx).Model(&welfare_task_cfgs.WelfareTaskCfgs{})
	// 预发环境只查pre_env = 1的数据
	if environment.IsPre() {
		query = query.Where("pre_env = 1")
	} else {
		query = query.Where("pre_env = 0")
	}
	if regionId != "" {
		query = query.Where("task_region_key = ?", regionId)
	}
	if identityId != 0 {
		query = query.Where("task_identity_id = ?", identityId)
	}
	if welfareTaskType != 0 {
		query = query.Where("Type = ?", welfareTaskType)
	}
	// 正序
	err := query.Order("sort asc").Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 获取最大sort
func (w *WelfareMysqlDBUtil) GetMaxSort(ctx context.Context) (int64, error) {
	var maxSort sql.NullInt64
	// 查询最大 sort 值
	err := w.DB.WithContext(ctx).
		Model(&welfare_task_cfgs.WelfareTaskCfgs{}).
		Select("MAX(sort)").
		Scan(&maxSort).Error
	// 如果 maxSort.Valid 为 false，表示结果为 NULL，设置默认值为 0
	if !maxSort.Valid {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}

	return maxSort.Int64, nil
}

func (w *WelfareMysqlDBUtil) InsertWelfareTaskCfg(ctx context.Context, data *welfare_task_cfgs.WelfareTaskCfgs) (int64, error) {
	err := w.DB.WithContext(ctx).Create(&data).Error
	return data.Id, err
}

func (w *WelfareMysqlDBUtil) UpdateWelfareTaskCfg(ctx context.Context, data *welfare_task_cfgs.WelfareTaskCfgs) (bool, error) {
	if data.Id == 0 {
		return false, errors.New("invalid params")
	}

	err := w.DB.WithContext(ctx).Model(&welfare_task_cfgs.WelfareTaskCfgs{}).Where("id = ?", data.Id).Updates(data).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

func (w *WelfareMysqlDBUtil) UpdateWelfareTaskCfgByVersion(ctx context.Context, data *welfare_task_cfgs.WelfareTaskCfgs) (bool, error) {
	if data.Id == 0 {
		return false, errors.New("invalid params")
	}

	err := w.DB.WithContext(ctx).Model(&welfare_task_cfgs.WelfareTaskCfgs{}).Where("id = ?", data.Id).Where("version = ?", data.Version).Updates(data).Error
	if err != nil {
		return false, err
	}
	return true, nil
}
