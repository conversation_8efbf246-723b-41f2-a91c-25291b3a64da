package dao

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"errors"
	"fmt"
	"time"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
	"gateio_service_welfare_go/internal/consts"
)

// 状态常量
const (
	StatusInit    = "INIT"    // 初始化状态
	StatusSuccess = "SUCCESS" // 成功
	StatusFailed  = "FAILED"  // 失败
)

// WelfareExchangeRecord 对应数据库中的福利奖品记录分表
// 表名规则：welfare_exchange_records_0, welfare_exchange_records_1, ...welfare_exchange_records_9
type WelfareExchangeRecord struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement;column:id;comment:自增主键ID"`                                                     // 自增主键ID
	UID         int64     `json:"uid" gorm:"not null;default:0;column:uid;comment:用户ID"`                                                           // 用户ID
	PrizeID     int       `json:"prize_id" gorm:"not null;default:0;column:prize_id;comment:奖品ID"`                                                 // 奖品ID
	Points      int64     `json:"points" gorm:"not null;default:0;column:points;comment:消耗的积分"`                                                    // 消耗的积分
	Status      string    `json:"status" gorm:"type:varchar(50);not null;default:INIT;column:status;comment:发放状态 INIT:初始化状态;SUCCESS:成功;FAILED:失败"` // 发放状态：INIT-初始化 SUCCESS-成功 FAILED-失败
	PrizeSource string    `json:"prize_source" gorm:"type:varchar(50);not null;default:exchange;column:prize_source;comment:奖品来源"`                 // 奖品来源：兑换奖品：exchange，新人任务：newbie_task_new  限时任务 ：limited_time_tasks  每日任务：daily task
	Type        int       `json:"type" gorm:"not null;default:0;column:type;comment:奖励类型"`                                                         // 奖励类型：1-点卡 2-VIP1 3-合约体验金 4-startup券 5-量化体验金 6-VIP+1 7-理财体验金 8-合约体验券 9-高息理财
	TypeNum     int       `json:"type_num" gorm:"not null;default:0;column:type_num;comment:奖励类型对应的数量"`                                            // 奖励类型对应的数量
	TaskID      int64     `json:"task_id" gorm:"not null;default:0;column:task_id;comment:任务ID"`                                                   // 任务ID（如果是任务奖励）
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime;column:created_at;comment:创建时间"`                                                 // 创建时间
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime;column:updated_at;comment:更新时间"`                                                 // 更新时间（自动更新）
}

// 获取福利奖品记录分表名称
func GetWelfareExchangeRecordTableName(uid int) string {
	val := uid % 10
	return fmt.Sprintf("welfare_exchange_records_%d", val)
}

// GetExchangeRecordsByTime 获取用户时间内兑换记录
// uid: 用户ID
// prizeId: 奖品ID
// startTime: 开始时间 (格式: "2006-01-02 15:04:05")
// endTime: 结束时间 (格式: "2006-01-02 15:04:05")
func (w *WelfareMysqlDBUtil) GetExchangeRecordsByTime(ctx context.Context, uid int, prizeId int, startTime, endTime string) ([]*WelfareExchangeRecord, error) {
	var records []*WelfareExchangeRecord

	// 获取分表名
	tableName := GetWelfareExchangeRecordTableName(uid)

	// 构建查询
	query := w.DB.WithContext(ctx).Table(tableName).
		Where("uid = ?", uid).
		Where("prize_id = ?", prizeId).
		Where("status = ?", StatusSuccess).
		Where("prize_source = ?", consts.RecordPrizeSourceExchange)

	// 添加时间范围条件
	if startTime != "" && endTime != "" {
		query = query.Where("created_at BETWEEN ? AND ?", startTime, endTime)
	} else if startTime != "" {
		query = query.Where("created_at >= ?", startTime)
	} else if endTime != "" {
		query = query.Where("created_at <= ?", endTime)
	}

	// 执行查询
	err := query.Find(&records).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "GetExchangeRecordsByTime failed: uid=%d, prizeId=%d, startTime=%s, endTime=%s, err=%v",
			uid, prizeId, startTime, endTime, err)
		return nil, err
	}

	return records, nil
}

// 根据页码查询记录列表
func (w *WelfareMysqlDBUtil) QueryExchangeRecordsByPage(ctx context.Context, uid int, prizeSource, startTime, sortVal string, page, prePage int) ([]*WelfareExchangeRecord, int64, error) {
	resp := []*WelfareExchangeRecord{}
	var count int64
	tableName := GetWelfareExchangeRecordTableName(uid)
	query := w.DB.WithContext(ctx).Table(tableName).Where("uid = ?", uid).Where("status = ?", "SUCCESS")
	if prizeSource != "" && prizeSource != "all" {
		query = query.Where("prize_source = ?", prizeSource)
	}
	if startTime != "" {
		query = query.Where("updated_at >= ?", startTime)
	}
	err := query.Count(&count).Error
	if err != nil {
		logc.Errorf(ctx, "QueryExchangeRecordsByPage count is error: %v,uid %d, prizeSource %s, startTime %s, sortVal %s, page %d, prePage %d", err, uid, prizeSource, startTime, sortVal, page, prePage)
		return nil, 0, err
	}
	err = query.Order(fmt.Sprintf("updated_at %s", sortVal)).Offset((page - 1) * prePage).Limit(prePage).Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "QueryExchangeRecordsByPage list is error: %v,uid %d, prizeSource %s, startTime %s, sortVal %s, page %d, prePage %d", err, uid, prizeSource, startTime, sortVal, page, prePage)
		return nil, 0, err
	}
	return resp, count, nil
}

// 插入奖品详情
func (w *WelfareMysqlDBUtil) InsertExchangeRecord(ctx context.Context, uid, prizeId, points int64, status, prizeSource string, prizeType, typeNum, taskId int64) (int64, error) {

	data := WelfareExchangeRecord{
		UID:         uid,
		PrizeID:     int(prizeId),
		Points:      points,
		Status:      status,
		PrizeSource: prizeSource,
		Type:        int(prizeType),
		TypeNum:     int(typeNum),
		TaskID:      taskId,
	}

	// 获取分表名
	tableName := GetWelfareExchangeRecordTableName(int(uid))

	err := w.DB.WithContext(ctx).Table(tableName).Create(&data).Error

	return data.ID, err
}

func (w *WelfareMysqlDBUtil) UpdateExchangeStatusById(ctx context.Context, uid, recordId int, status string) (bool, error) {
	tableName := GetWelfareExchangeRecordTableName(uid)
	err := w.DB.WithContext(ctx).Table(tableName).Where("id = ?", recordId).Update("status", status).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

func (w *WelfareMysqlDBUtil) GetSuccRecordByUidAndUserTaskId(ctx context.Context, uid, userTaskId int64) (*WelfareExchangeRecord, error) {
	var resp *WelfareExchangeRecord
	tableName := GetWelfareExchangeRecordTableName(int(uid))
	err := w.DB.WithContext(ctx).Table(tableName).Where("`uid` = ?", uid).
		Where("`task_id` = ?", userTaskId).
		Where("`status` = ?", consts.RecordStatusSuccess).
		First(&resp).Error

	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (w *WelfareMysqlDBUtil) GetRecordByUidAndId(ctx context.Context, uid, id int64) (*WelfareExchangeRecord, error) {
	var resp *WelfareExchangeRecord
	tableName := GetWelfareExchangeRecordTableName(int(uid))
	err := w.DB.WithContext(ctx).Table(tableName).Where("`id` = ?", id).First(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}
