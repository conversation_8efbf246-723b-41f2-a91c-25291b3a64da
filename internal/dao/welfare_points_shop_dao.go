package dao

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gateio/gateio-lib-base-go/environment"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_points_shop"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

// GetPrizeInfoById 获取奖品信息
func (w *WelfareMysqlDBUtil) GetPrizeInfoById(ctx context.Context, id int) (*welfare_points_shop.WelfarePointsShop, error) {
	resp := &welfare_points_shop.WelfarePointsShop{}
	query := w.DB.WithContext(ctx).Model(&welfare_points_shop.WelfarePointsShop{}).
		Where("`id` = ? AND `status` = ?", id, consts.PointsShopStatusOnline)
	// 预发环境只查pre_env = 1的数据
	if environment.IsPre() {
		query = query.Where("pre_env = 1")
	} else {
		query = query.Where("pre_env = 0")
	}
	err := query.Order("id desc").First(resp).Error

	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "GetLastCheckIn failed: %v", err)
		return nil, fmt.Errorf("GetPrizeInfoById failed: %w", err)
	}
	return resp, nil
}

// QueryPrizeInfo 获取奖品信息
func (w *WelfareMysqlDBUtil) QueryPrizeInfo(ctx context.Context, prizeIds []int) ([]*welfare_points_shop.WelfarePointsShop, error) {
	resp := []*welfare_points_shop.WelfarePointsShop{}
	err := w.DB.WithContext(ctx).Model(&welfare_points_shop.WelfarePointsShop{}).
		Where("id in (?)", prizeIds).
		Order("id desc").
		Find(&resp).Error

	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "QueryPrizeInfo failed: %v", err)
		return nil, err
	}
	return resp, nil
}

// GetAllPrizeList 获取所有奖品列表
func (w *WelfareMysqlDBUtil) GetAllPrizeList(ctx context.Context) ([]*welfare_points_shop.WelfarePointsShop, error) {
	var resp []*welfare_points_shop.WelfarePointsShop
	query := w.DB.WithContext(ctx).Model(&welfare_points_shop.WelfarePointsShop{}).
		Where("status = ?", consts.PointsShopStatusOnline)

	// 预发环境只查pre_env = 1的数据
	if environment.IsPre() {
		query = query.Where("pre_env = 1")
	}
	err := query.Order("sort DESC,id DESC").Find(&resp).Error

	if err != nil {
		return nil, err
	}

	return resp, nil
}

// 获取最大sort
func (w *WelfareMysqlDBUtil) ShopGetMaxSort(ctx context.Context) (int64, error) {
	var maxSort sql.NullInt64

	// 查询最大 sort 值
	err := w.DB.WithContext(ctx).
		Model(&welfare_points_shop.WelfarePointsShop{}).
		Select("MAX(sort)").
		Scan(&maxSort).Error
	// 如果 maxSort.Valid 为 false，表示结果为 NULL，设置默认值为 0
	if !maxSort.Valid {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}

	return maxSort.Int64, nil
}

func (w *WelfareMysqlDBUtil) InsertWelfarePointsShop(ctx context.Context, data *welfare_points_shop.WelfarePointsShop) (int64, error) {
	err := w.DB.WithContext(ctx).Create(&data).Error
	return data.Id, err
}

func (w *WelfareMysqlDBUtil) UpdateWelfarePointsShopAndPrizeMaxNum(ctx context.Context, data *welfare_points_shop.WelfarePointsShop) (bool, error) {
	if data.Id == 0 {
		return false, errors.New("invalid params")
	}
	err := w.DB.WithContext(ctx).Model(&welfare_points_shop.WelfarePointsShop{}).Where("id = ?", data.Id).Updates(data).Error
	if err != nil {
		return false, err
	}
	err = w.DB.WithContext(ctx).Model(&welfare_points_shop.WelfarePointsShop{}).Where("id = ?", data.Id).Update("prize_max_num", data.PrizeMaxNum).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

func (w *WelfareMysqlDBUtil) UpdateWelfarePointsShop(ctx context.Context, data *welfare_points_shop.WelfarePointsShop) (bool, error) {
	if data.Id == 0 {
		return false, errors.New("invalid params")
	}

	err := w.DB.WithContext(ctx).Model(&welfare_points_shop.WelfarePointsShop{}).Where("id = ?", data.Id).Updates(data).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetPrizeInfoByOneId 获取奖品信息
func (w *WelfareMysqlDBUtil) GetPrizeInfoByOneId(ctx context.Context, id int) (*welfare_points_shop.WelfarePointsShop, error) {
	resp := &welfare_points_shop.WelfarePointsShop{}
	err := w.DB.WithContext(ctx).Model(&welfare_points_shop.WelfarePointsShop{}).Where("`id` = ?", id).First(resp).Error

	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "GetPrizeInfoByOneId failed: %v", err)
		return nil, fmt.Errorf("GetPrizeInfoByOneId failed: %w", err)
	}
	return resp, nil
}
