package dao

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	"context"
	"errors"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/svc"
	"strconv"
)

const (
	//定义参数中的枚举常量
	ParamsTaskStatusInProgress    = 1 //  任务进行中，用户正在完成任务的过程中
	ParamsTaskStatusPendingReward = 2 //  任务已完成但尚未发放奖励
	ParamsTaskStatusRewarded      = 3 //  任务已完成且奖励已发放
	ParamsTaskStatusExpired       = 4 //  任务已过期，用户无法再完成或获得奖励
	//定义数据库中的枚举常量
	TaskStatusProcessing = 1 //  任务进行中，用户正在完成任务的过程中
	TaskStatusCompleted  = 2 //  任务已完成，所有条件均已满足
	TaskStatusSettling   = 3 //  任务结算中，奖励正在处理但尚未发放
	TaskStatusSettled    = 4 //  任务已结算，奖励已成功发放
	TaskStatusExpired    = 5 //  任务已过期，无法继续完成或获取奖励
)

// 获取单个任务信息
func (w *WelfareMysqlDBUtil) GetUserTask(ctx context.Context, uid, baseType, taskType int64, tid int64) (*welfare_user_tasks.WelfareUserTasks, error) {
	var resp welfare_user_tasks.WelfareUserTasks
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{})
	// 根据条件动态构建查询
	if uid > 0 {
		query = query.Where("`uid` = ?", uid)
	}
	if baseType > 0 {
		query = query.Where("`type` = ?", baseType)
	}
	if taskType > 0 {
		query = query.Where("`task_type` = ?", taskType)
	}
	if tid > 0 {
		query = query.Where("`task_id` = ?", tid)
	}
	err := query.First(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return &resp, nil
}

// 根据条件获取任务列表
func (w *WelfareMysqlDBUtil) QueryUserTaskList(ctx context.Context, uid int, typeList []int, typeVal, status int) ([]*welfare_user_tasks.WelfareUserTasks, error) {
	resp := []*welfare_user_tasks.WelfareUserTasks{}
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{})
	// 根据条件动态构建查询
	if uid > 0 {
		query = query.Where("`uid` = ?", uid)
	}
	if len(typeList) > 0 {
		query = query.Where("`type` in (?)", typeList)
	}
	if typeVal > 0 {
		query = query.Where("`type` = ?", typeVal)
	}
	if status > 0 {
		query = query.Where("`status` = ?", status)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (w *WelfareMysqlDBUtil) InsertUserTask(ctx context.Context, data *welfare_user_tasks.WelfareUserTasks) (int64, error) {
	err := w.DB.WithContext(ctx).Create(&data).Error
	return data.Id, err
}

func (w *WelfareMysqlDBUtil) DeleteUserTask(ctx context.Context, userTaskId int64) error {
	err := w.DB.WithContext(ctx).Where("id = ?", userTaskId).Delete(&welfare_user_tasks.WelfareUserTasks{}).Error
	return err
}

// 根据页码查询任务记录列表
func (w *WelfareMysqlDBUtil) QueryTaskRecordsByPage(ctx context.Context, uid, status int, startTime, sortVal string, page, prePage int) ([]*welfare_user_tasks.WelfareUserTasks, int64, error) {
	resp := []*welfare_user_tasks.WelfareUserTasks{}
	var count int64
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{}).
		Where("uid = ?", uid).Where("record_show < ?", consts.WelfareUserShowRecordNo)
	//根据枚举值定义查询条件
	if status > 0 {
		query = query.Where("`status` = ?", status)
	}
	if startTime != "" {
		query = query.Where("created_at >= ?", startTime)
	}
	err := query.Count(&count).Error
	if err != nil {
		logc.Errorf(ctx, "QueryTaskRecordsByPage count is error: %v,uid %d, status %d, startTime %s, sortVal %s, page %d, prePage %d", err, uid, status, startTime, sortVal, page, prePage)
		return nil, 0, err
	}
	err = query.Order(fmt.Sprintf("id %s", sortVal)).Offset((page - 1) * prePage).Limit(prePage).Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "QueryTaskRecordsByPage count is error: %v,uid %d, status %d, startTime %s, sortVal %s, page %d, prePage %d", err, uid, status, startTime, sortVal, page, prePage)
		return nil, 0, err
	}
	return resp, count, nil
}

// 根据数据ID列表查询用户任务记录
func (w *WelfareMysqlDBUtil) QueryTaskRecordsByIds(ctx context.Context, uid int, ids []int64) ([]*welfare_user_tasks.WelfareUserTasks, error) {
	resp := []*welfare_user_tasks.WelfareUserTasks{}
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{}).
		Where("uid = ?", uid)
	if len(ids) > 0 {
		query = query.Where("id in (?)", ids)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "QueryTaskRecordsByIds count is error: %v,uid %d, ids %v", err, uid, ids)
		return nil, err
	}
	return resp, nil
}

func (w *WelfareMysqlDBUtil) GetUserTaskById(ctx context.Context, id int64) (*welfare_user_tasks.WelfareUserTasks, error) {
	var resp welfare_user_tasks.WelfareUserTasks
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{})
	query = query.Where("`id` = ?", id)
	err := query.First(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return &resp, nil
}

func (w *WelfareMysqlDBUtil) UpdateTaskStatus(ctx context.Context, svc *svc.ServiceContext, uid, id, status int64) (bool, error) {
	if id == 0 || status == 0 {
		return false, errors.New("invalid params")
	}
	updateMap := map[string]interface{}{
		"status": status,
	}
	if status == consts.StatusSettlement {
		updateMap["record_show"] = consts.WelfareUserShowRecordYes
	}
	err := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{}).Where("id = ?", id).Updates(updateMap).Error
	if err != nil {
		return false, err
	}

	_ = svc.Redis.Setex(fmt.Sprintf(consts.WelfareUidAndTaskIdAndStatus, uid, id), strconv.FormatInt(status, 10), 60)

	return true, nil
}

func (w *WelfareMysqlDBUtil) UpdateTaskData(ctx context.Context, svc *svc.ServiceContext, uid int64, data *welfare_user_tasks.WelfareUserTasks) (bool, error) {
	if data.Id == 0 {
		return false, errors.New("invalid params")
	}
	err := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{}).Where("id = ?", data.Id).Updates(data).Error
	if err != nil {
		return false, err
	}

	if data.Status > 0 {
		_ = svc.Redis.Setex(fmt.Sprintf(consts.WelfareUidAndTaskIdAndStatus, uid, data.Id), strconv.FormatInt(data.Status, 10), 60)
	}

	return true, nil
}

func (w *WelfareMysqlDBUtil) GetUserTaskByUId(ctx context.Context, uid int64, status []int, ttype int) ([]*welfare_user_tasks.WelfareUserTasks, error) {
	var resp []*welfare_user_tasks.WelfareUserTasks
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{})
	query = query.Where("`uid` = ?", uid)
	if len(status) > 0 {
		query = query.Where("`status` in ?", status)
	}
	if ttype > 0 {
		query = query.Where("`type` = ?", ttype)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (w *WelfareMysqlDBUtil) GetUserTaskByUIdAndTaskCenterId(ctx context.Context, uid, taskCenterId int64) (*welfare_user_tasks.WelfareUserTasks, error) {
	var resp *welfare_user_tasks.WelfareUserTasks
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{})
	query = query.Where("`uid` = ?", uid)
	query = query.Where("`task_center_id` = ?", taskCenterId).Order("id desc")
	err := query.First(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (w *WelfareMysqlDBUtil) GetUserTaskByUIdAndTaskId(ctx context.Context, uid, taskId int64) (*welfare_user_tasks.WelfareUserTasks, error) {
	var resp *welfare_user_tasks.WelfareUserTasks
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{})
	query = query.Where("`uid` = ?", uid)
	query = query.Where("`task_id` = ?", taskId).Order("id desc")
	err := query.First(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (w *WelfareMysqlDBUtil) GetUserTaskByUIdAndWelfareTaskId(ctx context.Context, uid, welfareTaskId int64) (*welfare_user_tasks.WelfareUserTasks, error) {
	var resp *welfare_user_tasks.WelfareUserTasks
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{})
	query = query.Where("`uid` = ?", uid)
	query = query.Where("`task_id` = ?", welfareTaskId).Order("id desc")
	err := query.First(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (w *WelfareMysqlDBUtil) GetDailyTaskAllByUid(ctx context.Context, uid int64) (map[int64]*welfare_user_tasks.WelfareUserTasks, error) {
	var resp []*welfare_user_tasks.WelfareUserTasks
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{})
	err := query.Where("`uid` = ?", uid).
		Where("`type` = ?", consts.UserTaskTypeVeteranDaily).
		Order("id desc").Find(&resp).Error

	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}

	result := map[int64]*welfare_user_tasks.WelfareUserTasks{}
	for _, v := range resp {
		if _, ok := result[v.TaskCenterId]; !ok {
			result[v.TaskCenterId] = v
		}
	}

	return result, nil
}

func (w *WelfareMysqlDBUtil) GetLimitTaskAllByUid(ctx context.Context, uid int64) (map[int64]*welfare_user_tasks.WelfareUserTasks, error) {
	var resp []*welfare_user_tasks.WelfareUserTasks
	query := w.DB.WithContext(ctx).Model(&welfare_user_tasks.WelfareUserTasks{})
	err := query.Where("`uid` = ?", uid).
		Where("`type` = ?", consts.UserTaskTypeVeteranLimited).
		Order("id desc").Find(&resp).Error

	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}

	result := map[int64]*welfare_user_tasks.WelfareUserTasks{}
	for _, v := range resp {
		if _, ok := result[v.TaskCenterId]; !ok {
			result[v.TaskCenterId] = v
		}
	}

	return result, nil
}
