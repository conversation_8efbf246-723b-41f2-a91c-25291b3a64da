package dao

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"errors"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_check_in_logs"
	"gateio_service_welfare_go/internal/utils"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

// QueryCheckInList 获取签到列表信息
func (w *WelfareMysqlDBUtil) QueryCheckInList(ctx context.Context, uid int, day int, status int) ([]*welfare_check_in_logs.WelfareCheckInLogs, error) {
	resp := make([]*welfare_check_in_logs.WelfareCheckInLogs, 0)
	query := w.DB.WithContext(ctx).Model(&welfare_check_in_logs.WelfareCheckInLogs{})

	// 根据条件动态构建查询
	if uid > 0 {
		query = query.Where("`uid` = ?", uid)
	}
	if day > 0 {
		query = query.Where("`day` = ?", day)
	}
	if status != 0 {
		query = query.Where("`status` != ?", status)
	}

	if err := query.Order("id desc").Find(&resp).Error; err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "QueryCheckInList failed: %v", err)
		return nil, fmt.Errorf("query check-in list failed: %w", err)
	}
	return resp, nil
}

// GetLastCheckIn 获取最后一个签到信息
func (w *WelfareMysqlDBUtil) GetLastCheckIn(ctx context.Context, uid int, status int) (*welfare_check_in_logs.WelfareCheckInLogs, error) {
	resp := &welfare_check_in_logs.WelfareCheckInLogs{}
	err := w.DB.WithContext(ctx).Model(&welfare_check_in_logs.WelfareCheckInLogs{}).
		Where("`uid` = ? AND `status` != ?", uid, status).
		Order("id desc").
		First(resp).Error

	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "GetLastCheckIn failed: %v", err)
		return nil, fmt.Errorf("get last check-in failed: %w", err)
	}
	return resp, nil
}

// UpdateCheckInInfo 根据字段和条件更新快照信息
func (w *WelfareMysqlDBUtil) UpdateCheckInInfo(ctx context.Context, id int64, updateMap map[string]interface{}) error {
	if len(updateMap) == 0 {
		return nil
	}

	if err := w.DB.WithContext(ctx).Model(&welfare_check_in_logs.WelfareCheckInLogs{}).
		Where("id = ?", id).
		Updates(updateMap).Error; err != nil {
		logc.Errorf(ctx, "UpdateCheckInInfo failed: %v", err)
		return fmt.Errorf("update check-in info failed: %w", err)
	}
	return nil
}

// InsertCheckIn 插入签到记录
func (w *WelfareMysqlDBUtil) InsertCheckIn(ctx context.Context, task *CheckInConfig, uid, isCycleLastDay int) (int64, error) {
	today := utils.GetYmdDay(0)
	prizeId, _ := w.GetPrizeInfo(task)

	welfareCheckInLog := &welfare_check_in_logs.WelfareCheckInLogs{
		CheckInTaskId:  int64(task.WeekDay),
		Uid:            int64(uid),
		Day:            int64(today),
		IsCycleLastDay: int64(isCycleLastDay),
		PrizeId:        int64(prizeId),
		PrizeType:      int64(task.PrizeType),
		PrizeTypeNum:   int64(task.PrizeTypeNum),
		Status:         0,
	}

	if err := w.DB.WithContext(ctx).Create(welfareCheckInLog).Error; err != nil {
		logc.Errorf(ctx, "InsertCheckIn create record failed: %v", err)
		return 0, fmt.Errorf("create check-in record failed: %w", err)
	}

	return welfareCheckInLog.Id, nil
}

func (w *WelfareMysqlDBUtil) UpdateCheckInStatus(ctx context.Context, checkInId int64, status int) (int64, error) {

	if err := w.UpdateCheckInInfo(ctx, checkInId, map[string]interface{}{"status": status}); err != nil {
		logc.Errorf(ctx, "Update check-in status failed: %v", err)
		return 0, err
	}

	return checkInId, nil
}

// GetPrizeInfo 获取奖品信息
func (w *WelfareMysqlDBUtil) GetPrizeInfo(task *CheckInConfig) (prizeId int, source string) {
	if task.PrizeType != consts.CheckInCoupon {
		return 0, ""
	}

	if utils.CheckDev() {
		return consts.GetRealCouponId(consts.CheckInCouponId), consts.GetRealSource(consts.CheckInSource)
	}
	return consts.CheckInCouponId, consts.CheckInSource
}
