package dao

import (
	"context"
	"errors"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_region_cfgs"

	"bitbucket.org/gatebackend/gorm-zero/gormc"
)

// 根据地区查询新客时间
func (w *WelfareMysqlDBUtil) GetRegionCfgInfo(ctx context.Context, regionId string) (*welfare_region_cfgs.WelfareRegionCfgs, error) {
	resp := &welfare_region_cfgs.WelfareRegionCfgs{}
	err := w.DB.WithContext(ctx).Model(&welfare_region_cfgs.WelfareRegionCfgs{}).
		Where("task_region_key = ?", regionId).First(resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

// 查询列表
func (w *WelfareMysqlDBUtil) QueryRegionCfgList(ctx context.Context, customerType int) ([]*welfare_region_cfgs.WelfareRegionCfgs, error) {
	customerTypes := []int{consts.CustomerTypeAll, customerType}
	resp := []*welfare_region_cfgs.WelfareRegionCfgs{}
	query := w.DB.WithContext(ctx).Model(&welfare_region_cfgs.WelfareRegionCfgs{})
	if customerType != 0 {
		query = query.Where("customer_type  IN ?", customerTypes)
	}
	err := query.Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}

func (w *WelfareMysqlDBUtil) UpdateRegionCfg(ctx context.Context, data *welfare_region_cfgs.WelfareRegionCfgs) (bool, error) {
	if data.Id == 0 {
		return false, errors.New("invalid params")
	}

	err := w.DB.WithContext(ctx).Model(&welfare_region_cfgs.WelfareRegionCfgs{}).Where("id = ?", data.Id).Updates(data).Error
	if err != nil {
		return false, err
	}
	return true, nil
}
