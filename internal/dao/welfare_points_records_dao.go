package dao

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_points_records"
)

const (
	ActionIncr = "incr"
	ActionDecr = "decr"
	//参数中积分变化的来源类型
	ParamSourceTypeChallenge           = 1  // 挑战任务，用户可参与的各类挑战活动
	ParamSourceTypeWelfare             = 2  // 福利任务，完成后可获得额外福利的任务
	ParamSourceTypeInvite              = 3  // 邀请任务，通过邀请好友获得奖励的任务
	ParamSourceTypeNewUser             = 4  // 新人任务，包含新客入门、进阶等系列任务
	ParamSourceTypeCheckIn             = 5  // 签到任务，每日签到获取奖励的任务
	ParamSourceTypeTGCheckIn           = 6  // Telegram签到任务，在Telegram平台完成的签到任务
	ParamSourceTypeLimitedTime         = 7  // 老福利中心限时任务，包含老客限时任务等特定时间段开放的任务
	ParamSourceTypeGameCenter          = 8  // 游戏中心任务，游戏中心内的通用任务
	ParamSourceTypeGameCenterInvite    = 9  // 游戏中心邀请任务，游戏中心内的邀请类任务
	ParamSourceTypeNewCustomerRegister = 10 // 新客注册任务，新用户注册后触发的任务
	ParamSourceTypeDaily               = 11 // 常规任务 新福利中心老客每日
	ParamSourceTypeLimitedTimeEven     = 12 // 专属任务 新福利中心老客限时
	ParamSourceTypePointsExpire        = 13 // 积分过期，系统自动处理积分过期的任务
	ParamSourceTypePointsUpgrade       = 14 // 积分升级，积分等级提升相关的任务
	ParamSourceTypePointsExchange      = 99 // 积分兑换，用户使用积分兑换奖品的任务
)

// 根据页码查询积分记录列表
func (w *WelfareMysqlDBUtil) QueryPointRecordsByPage(ctx context.Context, uid, sourceType int, startTime, action, sortVal string, page, prePage int) ([]*welfare_points_records.WelfarePointsRecords, int64, error) {
	resp := []*welfare_points_records.WelfarePointsRecords{}
	var count int64
	query := w.DB.WithContext(ctx).Model(&welfare_points_records.WelfarePointsRecords{}).
		Where("uid = ?", uid)
	//根据参数类型查询变化来源
	if sourceType > 0 {
		//1, 2, 3, 5, 6, 7, 8, 9, 10,99 参数值与数据库的值相同
		if sourceType == ParamSourceTypeChallenge || sourceType == ParamSourceTypeWelfare || sourceType == ParamSourceTypeInvite ||
			sourceType == ParamSourceTypeCheckIn || sourceType == ParamSourceTypeTGCheckIn || sourceType == ParamSourceTypeLimitedTime ||
			sourceType == ParamSourceTypeGameCenter || sourceType == ParamSourceTypeGameCenterInvite ||
			sourceType == ParamSourceTypeNewCustomerRegister || sourceType == ParamSourceTypePointsExchange {
			query = query.Where("type = ?", sourceType)
		}
		if sourceType == ParamSourceTypeNewUser {
			query = query.Where("type = ? or type = ? or type = ?", consts.ExSourceTypeNewUser, consts.ExSourceTypeOnboarding, consts.ExSourceTypeAdvanced)
		}
		if sourceType == ParamSourceTypeDaily {
			query = query.Where("type = ?", consts.ExSourceTypeDaily)
		}
		if sourceType == ParamSourceTypeLimitedTimeEven {
			query = query.Where("type = ?", consts.ExSourceTypeLimitedTimeEvent)
		}
		if sourceType == ParamSourceTypePointsExpire {
			query = query.Where("type = ?", consts.ExSourceTypePointsExpire)
		}
		if sourceType == ParamSourceTypePointsUpgrade {
			query = query.Where("type = ? or type = ?", consts.ExSourceTypePointsClearBeforeExpansion, consts.ExSourceTypePointsIncreaseAfterExpansion)
		}
	}
	if startTime != "" {
		query = query.Where("created_at >= ?", startTime)
	}
	if action != "" {
		query = query.Where("action = ?", action)
	}
	err := query.Count(&count).Error
	if err != nil {
		logc.Errorf(ctx, "QueryPointRecordsByPage count is error: %v,uid %d, sourceType %d, startTime %s, action %s, sortVal %s, page %d, prePage %d", err, uid, sourceType, startTime, action, sortVal, page, prePage)
		return nil, 0, err
	}
	err = query.Order(fmt.Sprintf("id %s", sortVal)).Offset((page - 1) * prePage).Limit(prePage).Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		logc.Errorf(ctx, "QueryPointRecordsByPage list is error: %v,uid %d, sourceType %d, startTime %s, action %s, sortVal %s, page %d, prePage %d", err, uid, sourceType, startTime, action, sortVal, page, prePage)
		return nil, 0, err
	}
	return resp, count, nil
}

func (w *WelfareMysqlDBUtil) InsertPointsRecords(ctx context.Context, uid int, action string, points int64, memo interface{}, Type, taskId, taskType, correlationId int) (int64, error) {
	memoBytes, _ := json.Marshal(memo)
	pointRecord := &welfare_points_records.WelfarePointsRecords{
		Uid:           int64(uid),
		Action:        action,
		Points:        points,
		Type:          int64(Type),
		TaskId:        int64(taskId),
		TaskType:      int64(taskType),
		CorrelationId: int64(correlationId),
		Memo:          string(memoBytes),
	}
	err := w.DB.WithContext(ctx).Model(&welfare_points_records.WelfarePointsRecords{}).Create(pointRecord).Error
	if err != nil {
		return 0, err
	}
	return pointRecord.Id, nil
}
