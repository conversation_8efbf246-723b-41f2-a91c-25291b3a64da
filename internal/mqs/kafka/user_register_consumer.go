package kafka

import (
	"context"
	"gateio_service_welfare_go/internal/config"
	"gateio_service_welfare_go/internal/consts/con_kafka"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"sync"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"github.com/twmb/franz-go/pkg/kgo"
)

// KafkaConsumer 表示一个 Kafka 消费者
type UserRegisterKafkaConsumer struct {
	client *kgo.Client
	ctx    context.Context
	cancel context.CancelFunc
	wg     *sync.WaitGroup
	logx.Logger
	groupId string
	topic   []string
	svcCtx  *svc.ServiceContext
}

// StartAllConsumer 启动所有kafka消费者
func UserRegisterStartAllConsumer(svcCtx *svc.ServiceContext, c *config.KafkaConf) {
	// 启动合约定向转化活动--任务系统回调消费者
	var wg sync.WaitGroup

	// 定义不同消费组的 topic 映射
	consumerGroups := map[string][]string{
		con_kafka.WelfareReceiveNewbieTaskGroup: {
			con_kafka.UserCenterRegisterTopic,
		},
		// 可以添加更多消费组和对应的 topic
		// "other_group": {
		//     "other_topic1",
		//     "other_topic2",
		// },
	}

	// 为每个消费组创建对应的消费者
	for groupId, topics := range consumerGroups {
		consumer := NewUserRegisterKafkaConsumer(svcCtx, c, &wg, topics, groupId)
		if consumer != nil {
			consumer.Start()
		}
	}
	go func() {
		wg.Wait()
		logx.Info("All kafka userCenter consumers have completed")
	}()
}

// NewKafkaConsumer 创建一个新的 Kafka 消费者客户端
func NewUserRegisterKafkaConsumer(svcCtx *svc.ServiceContext, c *config.KafkaConf,
	wg *sync.WaitGroup, topics []string, groupId string) *UserRegisterKafkaConsumer {
	// 获取对应环境正确的topic名称
	topicList := make([]string, 0, len(topics))
	for _, topic := range topics {
		topicTemp := utils.UnifyTopic(topic)
		topicList = append(topicList, topicTemp)
	}

	ctx, cancel := context.WithCancel(context.Background())
	client, err := kgo.NewClient(
		kgo.SeedBrokers(c.Brokers...),
		kgo.ConsumerGroup(groupId),
		kgo.ConsumeTopics(topicList...),
		kgo.FetchMaxBytes(int32(c.FetchMaxBytes)),         // 每次抓取最大字节数
		kgo.FetchMinBytes(int32(c.FetchMinBytes)),         // 每次抓取最小字节数
		kgo.FetchMaxWait(c.FetchMaxWait),                  // 抓取最大等待时间
		kgo.ConsumeResetOffset(kgo.NewOffset().AtStart()), // 从最早的偏移量开始消费
		kgo.DisableAutoCommit(),                           // 禁用自动提交偏移量
	)
	if err != nil {
		cancel()
		logx.Infof("初始化userCenter kafka消费者失败, topic:%v, groupId:%s ", topics, groupId)
		return nil
	}
	logx.Infof("初始化userCenter kafka消费者成功, topic:%v, groupId:%s ", topics, groupId)
	return &UserRegisterKafkaConsumer{
		client:  client,
		ctx:     ctx,
		cancel:  cancel,
		wg:      wg,
		Logger:  logx.WithContext(ctx),
		groupId: groupId,
		topic:   topicList,
		svcCtx:  svcCtx,
	}
}

func (c *UserRegisterKafkaConsumer) Start() {
	c.wg.Add(1)
	go c.consumeMessages()
}

// ConsumeMessages 开始消费 Kafka 消息
func (c *UserRegisterKafkaConsumer) consumeMessages() {
	defer func() {
		c.cancel()
		c.wg.Done()
		c.Close()
	}()

	for {
		select {
		case <-c.ctx.Done():
			c.Logger.Infof("consumeMessages userCenter kafka消费者停止")
			return
		default:
			fetches := c.client.PollFetches(c.ctx)
			if c.ctx.Err() != nil {
				logx.Infof("consumeMessages 轮训获取userCenter kafka数据失败, err:%v ", c.ctx.Err())
				break
			}

			if errs := fetches.Errors(); len(errs) > 0 {
				for _, err := range errs {
					logx.Infof("userCenter consumeMessages 从主题 %s 分区 %d 拉取消息时出错: %v", err.Topic, err.Partition, err.Err)
				}
				continue
			}

			records := fetches.Records()
			if len(records) == 0 {
				logx.Infof("userCenter consumeMessages 从消费者获取到的消息数据为空，主题：%v", c.topic)
				continue
			}

			c.Logger.Infof("userCenter consumeMessages 消费者组 %s, 本次收到 %d 条消息", c.groupId, len(records))
			for _, record := range records {
				msgStr := string(record.Value)
				c.Logger.Infof("userCenter consumeMessages 消费者收到消息: %s, MsgNum:%d, Topic:%s, GroupId:%s ", string(record.Value), len(record.Value), record.Topic, c.groupId)

				// 根据不同的 topic 和消费组处理消息
				switch {
				case record.Topic == utils.UnifyTopic(con_kafka.UserCenterRegisterTopic) && c.groupId == con_kafka.WelfareReceiveNewbieTaskGroup:
					c.Logger.Infof("userCenter consumeMessages 收到用户注册消息: %s, MsgNum:%d, Topic:%s, GroupId:%s", string(record.Value), len(record.Value), record.Topic, c.groupId)
					//用户注册消息处理方法
					UserRegisterDeal(c.ctx, c.svcCtx, msgStr)
					// 可以添加更多的 case 来处理其他 topic 和消费组的消息
				}
			}

			if err := c.client.CommitRecords(c.ctx, records...); err != nil {
				c.Logger.Errorf("userCenter consumeMessages 提交偏移量失败, err:%v", err)
			}
		}
	}
}

// Close 关闭消费者客户端
func (c *UserRegisterKafkaConsumer) Close() {
	c.client.Close()
	c.Logger.Infof("userCenter 消费者已关闭")
}
