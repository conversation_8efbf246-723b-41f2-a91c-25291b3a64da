package kafka

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/risk"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/consts/con_kafka"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/models/welfare_user_register_info"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service"
	kafkaService "gateio_service_welfare_go/internal/service/kafka_producer"
	prizeService "gateio_service_welfare_go/internal/service/prize_service"

	"database/sql"
	//prizeService "gateio_service_welfare_go/internal/service/prize_service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"github.com/shopspring/decimal"
	"time"
)

// UserRegisterDeal 用户注册消息处理方法
func UserRegisterDeal(ctx context.Context, svcCtx *svc.ServiceContext, msgStr string) {
	userRegister := con_kafka.UserRegister{}
	err := json.Unmarshal([]byte(msgStr), &userRegister)
	if err != nil {
		logc.Errorf(ctx, "UserRegisterDeal msgStr Unmarshal Msg err: %v", err)
		return
	}
	logc.Infof(ctx, "UserRegisterDeal，消息解析成功，收到用户注册消息：%s", msgStr)
	if userRegister.UID <= 0 || userRegister.Timest == "" {
		logc.Errorf(ctx, "UserRegisterDeal failed ，Params failed：%s", msgStr)
		return
	}
	if userRegister.SubWebsiteID == requestools.WebsiteRegionTurkey {
		logc.Infof(ctx, "UserRegisterDeal user is tr user ，Params is：%s", msgStr)
		return
	}
	createTime := time.Now()
	if userRegister.Timest != "" {
		t, err := time.Parse(consts.GoInitializeTime, userRegister.Timest)
		if err != nil {
			logc.Errorf(ctx, "UserRegisterDeal time.Parse is err,UserRegisterDeal uid is:%d ,params is:%s ，err is：%v", userRegister.UID, userRegister.Timest, err)
		} else {
			createTime = t
		}
	}
	t, err := service.GetWelfareConfig(svcCtx, ctx, consts.WelfareRefactorTime)
	if err != nil {
		logc.Errorf(ctx, "UserRegisterDeal GetWelfareConfig is err: %v,UserRegisterDeal uid is:%d ,", err, userRegister.UID)
		return
	}
	if t == "" {
		logc.Errorf(ctx, "UserRegisterDeal welfare_refactor_time is nil;UserRegisterDeal uid is:%d ,name is:%s ,time is: %s", userRegister.UID, consts.WelfareRefactorTime, t)
		return
	}
	timestampT, err := utils.TransformDateStringToTime(t)
	if err != nil {
		logc.Errorf(ctx, "UserRegisterDeal TransformDateStringToTime is err: %v", err)
		return
	}
	//注册时间小于时间T值直接返回，不做处理，PHP项目中会处理小于时间T值的信息
	if createTime.Unix() <= timestampT.Unix() {
		return
	}
	//先获取用户区域，再做其他处理
	region, err := service.GetUserBelongRegion(svcCtx, ctx, userRegister.UID, nil, userRegister.RegisterCountryID, userRegister.ResidenceCountryID, nil)
	if err != nil {
		logc.Errorf(ctx, "UserRegisterDeal GetUserBelongRegion is failed ，UserRegisterDeal uid is:%d ,Params is：%s, err is:%v", userRegister.UID, msgStr, err)
		return
	}
	//先做黑名单验证
	isBlackCountry, err := service.IsBlackCountry(svcCtx, ctx, userRegister.RegisterCountryID, userRegister.ResidenceCountryID)
	if err != nil {
		logc.Errorf(ctx, "UserRegisterDeal IsBlackCountry is failed ，UserRegisterDeal uid is:%d ,Params is：%s, err is:%v", userRegister.UID, msgStr, err)
		return
	}
	//先验证快照是否存在
	registerInfo, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetWelfareUserRegisterDbDetail(svcCtx, ctx, userRegister.UID)
	if err != nil {
		logc.Errorf(ctx, "UserRegisterDeal GetWelfareUserRegisterInfo is err,UserRegisterDeal uid is:%d ,params is:%d ，err is：%v", userRegister.UID, userRegister.UID, err)
		return
	}
	//如果快照不存在则插入快照
	if registerInfo == nil || registerInfo.Uid == 0 {
		userRegisterInfo := &welfare_user_register_info.WelfareUserRegisterInfo{
			Uid:                   userRegister.UID,
			RegisterCountryId:     userRegister.RegisterCountryID,
			ResidenceCountryId:    userRegister.ResidenceCountryID,
			CreatedAt:             createTime,
			FinishAdvancedReceive: consts.FinishAdvancedReceiveNo,
			Region:                region,
			IsBlack:               int64(isBlackCountry),
		}
		//先记录用户快照
		err = welfare_user_register_info.NewWelfareUserRegisterInfoModel(svcCtx.WelfareDB).Insert(ctx, userRegisterInfo)
		if err != nil {
			byteVal, _ := json.Marshal(userRegisterInfo)
			logc.Errorf(ctx, "UserRegisterDeal WelfareUserRegisterInfo.Insert is err,UserRegisterDeal uid is:%d ,params is:%s ，err is：%v", userRegister.UID, string(byteVal), err)
			return
		}
	}
	//用户在黑名单中
	if isBlackCountry == 1 {
		logc.Infof(ctx, "UserRegisterDeal receiveTasks failed user is black country ，UserRegisterDeal uid is:%d ,Params is：%s", userRegister.UID, msgStr)
		return
	} else {
		//用户不在黑名单中可以记录快照，领取任务
		logc.Infof(ctx, "UserRegisterDeal uid is: %d , region is：%s", userRegister.UID, region)
		//根据地区获取用户的新客时间
		regionCfgInfo, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetRegionCfgInfo(ctx, region)
		if err != nil {
			logc.Errorf(ctx, "UserRegisterDeal GetRegionCfgInfo is failed ，UserRegisterDeal uid is:%d ,Params is：%s, err is:%v", userRegister.UID, region, err)
			return
		}
		//新客期不能为0
		if regionCfgInfo == nil || regionCfgInfo.DayNum <= 0 {
			logc.Infof(ctx, "UserRegisterDeal regionCfgInfo is err ，UserRegisterDeal uid is:%d ,Params failed：%s", userRegister.UID, region)
			return
		}
		//定义用户注册时间
		//用户新客结束时间戳
		newbieEndTime := createTime.AddDate(0, 0, int(regionCfgInfo.DayNum)).Unix()
		//进阶任务结算时间戳
		advancedEndTime := createTime.AddDate(0, 0, int(regionCfgInfo.DayNum)+consts.AdvancedTaskDelayDays).Unix()
		//根据地区获取任务列表 注册，入门，进阶
		taskList, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).QueryWelfareTaskCfgList(ctx, region, []int{consts.UserTaskTypeNewbieRegister, consts.UserTaskTypeNewbieGuide, consts.UserTaskTypeNewbieAdvanced}, 0)
		if err != nil {
			logc.Errorf(ctx, "UserRegisterDeal QueryWelfareTaskCfgList is failed ，UserRegisterDeal uid is:%d ,Params is：%s, err is:%v", userRegister.UID, region, err)
			return
		}
		if taskList == nil || len(taskList) == 0 {
			logc.Errorf(ctx, "UserRegisterDeal taskList len is 0 ，UserRegisterDeal uid is:%d ,Params is：%s, err is:%v", userRegister.UID, region, err)
			return
		}
		//获取任务中心的任务数据
		taskCenterIds := make([]int64, 0)
		for _, taskItem := range taskList {
			welfareTaskList := []*welfare_task_cfgs.WelfareTaskCfgs{taskItem}
			if taskItem.Type == consts.UserTaskTypeNewbieAdvanced {
				advancedTaskInfo := &dao.AdvancedTaskInfo{}
				err := json.Unmarshal([]byte(taskItem.ExtraTaskInfo.String), advancedTaskInfo)
				if err != nil {
					logc.Errorf(ctx, "UserRegisterDeal json.Unmarshal([]byte(taskItem.ExtraTaskInfo.String) is err,params is:%d,%d ,%s，err is：%v", userRegister.UID, taskItem.Id, taskItem.ExtraTaskInfo.String, err)
					return
				}
				welfareTaskList = make([]*welfare_task_cfgs.WelfareTaskCfgs, len(advancedTaskInfo.SubTasks))
				for i, taskCenter := range advancedTaskInfo.SubTasks {
					welfareTaskList[i] = &welfare_task_cfgs.WelfareTaskCfgs{
						Id:            taskItem.Id,
						TaskRegionKey: region,
						TaskId:        taskCenter.TaskId,
						Type:          taskItem.Type,
						TaskType:      consts.UserTaskTaskTypeAdvanced,
					}
				}
			}
			for _, cfg := range welfareTaskList {
				if cfg.TaskId > 0 {
					taskCenterIds = append(taskCenterIds, cfg.TaskId)
				}
			}
		}
		//验证任务是否存在或状态异常
		taskCenterMap := map[int64]*task.Task{}
		if len(taskCenterIds) > 0 {
			taskCenterMap, err = service.GetTaskListByTaskIds(svcCtx, ctx, 0, taskCenterIds)
			if err != nil {
				logc.Errorf(ctx, "UserRegisterDeal GetTaskListByTaskIds is err,uid is:%d, taskCenterIds is:%v ,err is：%v", userRegister.UID, taskCenterIds, err)
				return
			}
		}
		registerTask := &welfare_task_cfgs.WelfareTaskCfgs{}
		registerUserTask := &welfare_user_tasks.WelfareUserTasks{}
		//开启事务
		tx := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.Begin()
		//遍历领取任务
		for _, taskItem := range taskList {
			//下载任务手动领取
			if taskItem.TaskType == consts.UserTaskTaskTypeAppDownload {
				continue
			}
			//原php中有阶段Stage条件，在此处去掉
			welfareUserTask, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetUserTask(ctx, userRegister.UID, 0, 0, taskItem.Id)
			if err != nil {
				tx.Rollback()
				logc.Errorf(ctx, "UserRegisterDeal GetUserTask is err,params is:%d,%d ，err is：%v", userRegister.UID, taskItem.Id, err)
				return
			}

			//验证是否已经插入
			if welfareUserTask != nil && welfareUserTask.Uid > 0 {
				continue
			}
			welfareTaskList := []*welfare_task_cfgs.WelfareTaskCfgs{taskItem}
			if taskItem.Type == consts.UserTaskTypeNewbieAdvanced {
				advancedTaskInfo := &dao.AdvancedTaskInfo{}
				err := json.Unmarshal([]byte(taskItem.ExtraTaskInfo.String), advancedTaskInfo)
				if err != nil {
					tx.Rollback()
					logc.Errorf(ctx, "UserRegisterDeal json.Unmarshal([]byte(taskItem.ExtraTaskInfo.String) is err,params is:%d,%d ,%s，err is：%v", userRegister.UID, taskItem.Id, taskItem.ExtraTaskInfo.String, err)
					return
				}
				welfareTaskList = make([]*welfare_task_cfgs.WelfareTaskCfgs, len(advancedTaskInfo.SubTasks))
				for i, taskCenter := range advancedTaskInfo.SubTasks {
					welfareTaskList[i] = &welfare_task_cfgs.WelfareTaskCfgs{
						Id:            taskItem.Id,
						TaskRegionKey: region,
						TaskId:        taskCenter.TaskId,
						Type:          taskItem.Type,
						TaskType:      consts.UserTaskTaskTypeAdvanced,
					}
				}
			}
			for _, cfg := range welfareTaskList {
				if cfg.TaskId > 0 && (taskCenterMap[cfg.TaskId] == nil || taskCenterMap[cfg.TaskId].Title == "") {
					logc.Warnf(ctx, "UserRegisterDeal center task failed,uid is:%d, taskCenterId is:%d 任务中心数据存在或状态异常", userRegister.UID, cfg.TaskId)
					continue
				}
				//去掉数据占用较多的字段 奖励信息和快照信息,注册任务没有额外奖励去掉额外奖励信息
				cfg.Reward = sql.NullString{Valid: false}
				cfg.ApprovedSnapshot = sql.NullString{Valid: false}
				memoByte, _ := json.Marshal(cfg)
				//taskID是本地任务ID
				status := int64(consts.StatusInProcess)
				finishTaskTime := int64(0)
				//注册任务直接就是已完成(待领奖状态)
				if cfg.Type == consts.UserTaskTypeNewbieRegister {
					registerTask = cfg
					status = int64(consts.StatusDone)
					finishTaskTime = createTime.Unix()
				}
				recordShow := consts.WelfareUserShowRecordYes
				if cfg.Type == consts.UserTaskTypeNewbieAdvanced {
					recordShow = consts.WelfareUserShowRecordNo
				}
				welfareUserTask = &welfare_user_tasks.WelfareUserTasks{
					Uid:            userRegister.UID,
					Type:           cfg.Type,
					TaskId:         cfg.Id,
					TaskType:       cfg.TaskType,
					TaskProgress:   decimal.NewFromFloat(0),
					Status:         status,
					Points:         0,
					Memo:           string(memoByte),
					CreatedAt:      createTime,
					Stage:          0,
					FinishTaskTime: finishTaskTime,
					TaskCenterId:   cfg.TaskId,
					RecordShow:     int64(recordShow),
				}
				userTaskId, err := dao.NewWelfareMysqlTxDBUtil(tx).InsertUserTask(ctx, welfareUserTask)
				if err != nil {
					tx.Rollback()
					byteVal, _ := json.Marshal(welfareUserTask)
					logc.Errorf(ctx, "UserRegisterDeal WelfareUserTasksModel.Insert is err,params is:%s ，err is：%v", string(byteVal), err)
					return
				}
				if cfg.Type == consts.UserTaskTypeNewbieRegister {
					welfareUserTask.Id = userTaskId
					registerUserTask = welfareUserTask
				}
				//注册任务是本地任务不用推送到任务中心
				if cfg.TaskId > 0 {
					// 把业务的任务上报给任务系统
					getTaskTime := time.Now().Unix()
					kafkaProducer, err := kafkaService.NewKafkaProducer(svcCtx.KafkaConf)
					defer kafkaProducer.Close()
					if err != nil {
						//上报信息失败删除用户任务的创建
						err = dao.NewWelfareMysqlTxDBUtil(tx).DeleteUserTask(ctx, userTaskId)
						if err != nil {
							tx.Rollback()
							logc.Errorf(ctx, "UserRegisterDeal DeleteUserTask is err,params is:%d ，err is：%v", userTaskId, err)
							return
						}
						logc.Errorf(ctx, "UserRegisterDeal UserToDoContractTransformTask failed, step: kafka.NewKafkaProducer, UserId : %d, BusinessId: %d, err: %v", userRegister.UID, userTaskId, err)
					} else {
						err = kafkaProducer.TaskRecordUserReceiveTask(ctx, userRegister.UID, cfg.TaskId, consts.WelfareTaskBusinessType, userTaskId, getTaskTime, getTaskTime, newbieEndTime, 0)
						if err != nil {
							//上报信息失败删除用户任务的创建
							err = dao.NewWelfareMysqlTxDBUtil(tx).DeleteUserTask(ctx, userTaskId)
							if err != nil {
								tx.Rollback()
								logc.Errorf(ctx, "UserRegisterDeal DeleteUserTask is err,params is:%d ，err is：%v", userTaskId, err)
								return
							}
							logc.Errorf(ctx, "UserRegisterDeal UserToDoContractTransformTask failed, step: Producer.TaskRecordReceiveProducer, UserId : %d, BusinessId: %d, err: %v", userRegister.UID, userTaskId, err)
							continue
						}
						logc.Infof(ctx, "UserRegisterDeal UserToDoContractTransformTask, step: 上报任务系统的消息：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d", userRegister.UID, userTaskId, cfg.TaskId, consts.WelfareTaskBusinessType)
					}
				}
			}
		}
		tx.Commit()
		//如果注册任务存在，注册任务自动发奖
		if registerTask.Id > 0 {
			//调用新客奖励发放，发放注册任务奖励
			errCode, err := prizeService.NewPrizeNewbieService(ctx, svcCtx).ReceivePrize(registerTask, registerUserTask, &task.Task{}, "")
			if err != nil {
				//只打印不返回
				logc.Warnf(ctx, "UserRegisterDeal ReceivePrize is err，err is：%v", err)
			}
			if errCode != 0 {
				//只打印不返回
				logc.Warnf(ctx, "UserRegisterDeal ReceivePrize errCode != 0，errCode is：%d", errCode)
			}
		}
		//领取完任务更新用户快照信息
		updateMap := map[string]interface{}{}
		updateMap["region"] = region
		updateMap["newbie_end_time"] = newbieEndTime
		updateMap["advanced_end_time"] = advancedEndTime
		updateMap["finish_advanced_receive"] = consts.FinishAdvancedReceiveNo
		err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateUserRegisterInfo(svcCtx, ctx, userRegister.UID, updateMap)
		if err != nil {
			//只打印不返回
			logc.Errorf(ctx, "UserRegisterDeal UpdateUserRegisterInfo is error:, uid is:%d | updateMap is:%v err:%s", userRegister.UID, updateMap, err.Error())
		}
	}
	return
}

// TaskCallBack 用户注册消息处理方法
//
//	{
//	   "business_type": 3,
//	   "call_type": "ack", // 回调类型 ack：业务确认（只有在上报任务时，传递is_ack为1，才会触发此了行回调） done：成功 expire：失效 send_pirze：发奖
//	   "list": [
//	       {
//	           "business_type": 1, // 上报方
//	           "business_id": "1", // 业务ID， 通过此ID和用户、任务ID查处对应的记录数据
//	           "user_id": 1, // 用户ID
//	           "task_id": 1, // 任务ID
//	           "status": 1, // 任务变更状态 状态 1 未开始 2 进行中 3 已完成 4 已失效 5 奖励已发放 6 奖励发放中
//	           "received_type": 1,   //1:用户自己领取   2:后台派发
//	           "conditions": [
//	               {
//	                   "prize_type": 1, // 积分
//	                   "prize_num": 10
//
//	                   "prize_type": 2, // 业务端自定义奖励
//	                   "prize_num": 10，
//
//	                   "prize_type": 3,
//	                   "prize_num": 10，
//	                   "prize_ext": {
//	                       "coupon_id": 1, // 卡劵ID
//	                       "coupon_source": 1 // 卡劵来源
//	                   } // 奖励附属信息， 例如奖励是卡劵
//
//	                   "prize_type": 4,
//	                   "prize_num": 10，
//	                   "prize_ext": {
//	                       "prize_pool_id": 1, // 奖池ID
//	                       "draw_num": 1 // 增加的抽奖次数
//	                   } // 奖励附属信息， 例如奖励是抽奖次数
//	               }
//	           ] // 每个条件对应的奖励
//	       }
//	   ]
//	}
func TaskCallBack(ctx context.Context, svcCtx *svc.ServiceContext, msgStr string) {
	taskCallMsg := con_kafka.TaskCallBackMsg{}
	err := json.Unmarshal([]byte(msgStr), &taskCallMsg)
	if err != nil {
		logc.Infof(ctx, "TaskCallBack msgStr Unmarshal Msg err: %v", err)
		return
	}
	logc.Infof(ctx, "TaskCallBack，消息解析成功，收到用户注册消息：%s", msgStr)

	// 业务参数接收
	businessType := taskCallMsg.BusinessType
	list := taskCallMsg.List
	if businessType <= 0 || businessType != consts.WelfareTaskBusinessType {
		logc.Infof(ctx, "TaskCallBack info ，business type failed：%s", msgStr)
		return
	}

	if len(list) == 0 {
		logc.Errorf(ctx, "TaskCallConsumer failed, list empty: %s", msgStr)
		return
	}

	for _, taskItem := range list {
		if taskItem.BusinessType <= 0 || taskItem.BusinessType != consts.WelfareTaskBusinessType {
			logc.Infof(ctx, "TaskCallBack info ，business type failed：%s", msgStr)
			continue
		}

		if taskItem.ReceivedType != consts.TaskReceivedTypeWelfare {
			logc.Infof(ctx, "TaskCallBack info ，ReceivedType type failed：%s", msgStr)
			continue
		}

		businessId := taskItem.BusinessID
		taskStatus := taskItem.Status
		doneStatus := []int{consts.StatusSettlement, consts.StatusExpire}
		TaskStatusMapWelfareStatus := consts.GetTaskStatusMapWelfareStatus()

		uid := taskItem.UserID
		taskCenterId := taskItem.TaskID
		status := TaskStatusMapWelfareStatus[taskStatus]
		if status <= 0 {
			logc.Errorf(ctx, "TaskCallConsumer failed, getWelfareStatus, uid:%d|taskStatus:%d | status:%d | businessId:%s", uid, taskStatus, status, businessId)
			continue
		}

		// 判断进度为已完成或者已结算，则return
		userTaskRow, errGetUser := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetUserTaskById(ctx, utils.MustInt64(businessId))
		if errGetUser != nil || userTaskRow.TaskId <= 0 {
			logc.Errorf(ctx, "TaskCallBack failed ，taskItem info empty uid%d|businessId: %s", uid, businessId)
			continue
		}

		if utils.ContainsArray(doneStatus, int(userTaskRow.Status)) {
			logc.Infof(ctx, "TaskCallBack info ，status is done：uid:%d, businessId: %s", uid, businessId)
			continue
		}

		data := &welfare_user_tasks.WelfareUserTasks{
			Id:     utils.MustInt64(businessId),
			Status: int64(status),
		}
		//如果任务已发奖，修改展示状态
		if status == consts.StatusSettlement {
			data.RecordShow = consts.WelfareUserShowRecordYes
		}
		//如果任务是已完成的状态，没有完成的时间，需要打印错误日志，后续
		if status == consts.StatusDone {
			if taskItem.DoneTime <= 0 {
				logc.Errorf(ctx, "TaskCallConsumer failed, status=done, but doneTime is empty, uid:%d|taskStatus:%d | status:%d | businessId:%s", uid, taskStatus, status, businessId)
				resetKafkaMsg(ctx, taskCallMsg, con_kafka.GrowthTaskCallTopic)
				continue
			}
			data.FinishTaskTime = taskItem.DoneTime
		}

		tx := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.Begin()

		bolUpdate, errUpStatus := dao.NewWelfareMysqlTxDBUtil(tx).UpdateTaskData(ctx, svcCtx, uid, data)
		if errUpStatus != nil || !bolUpdate {
			tx.Rollback()
			logc.Errorf(ctx, "TaskCallConsumer failed, update status failed, uid:%d|taskStatus:%d | status:%d | businessId:%s err:%v", uid, taskStatus, status, businessId, err)
			continue
		}

		//成功，则写奖励信息
		if status == consts.StatusSettlement {

			userCenterInfo, errUserCenter := service.GetUserKycInfo(svcCtx, ctx, uid)
			if errUserCenter != nil {
				logc.Errorf(ctx, "TaskCallConsumer failed, GetUserKycInfo failed, uid:%d|businessId:%s |taskCenterId:%d, err:%v", uid, businessId, taskCenterId, errUserCenter)
				resetKafkaMsg(ctx, taskCallMsg, con_kafka.GrowthTaskCallTopic)
				continue
			}
			photoIdEncryption := userCenterInfo.PhotoidEncryption

			succRecord, errGetRecord := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetSuccRecordByUidAndUserTaskId(ctx, uid, utils.MustInt64(businessId))
			if errGetRecord != nil {
				tx.Rollback()
				logc.Errorf(ctx, "TaskCallConsumer failed, GetSuccRecordByUidAndUserTaskId failed, uid:%d | businessId:%s err:%v", uid, businessId, err)
				return
			}

			//插入奖励信息
			if succRecord.ID <= 0 {
				taskCenterInfo, errTaskCenter := service.GetTaskListByTaskIds(svcCtx, ctx, int(uid), []int64{int64(taskCenterId)})
				if errTaskCenter != nil {
					logc.Errorf(ctx, "TaskCallConsumer failed, get taskCenterInfo failed, uid:%d|businessId:%s |taskCenterId:%d |err:%v", uid, businessId, taskCenterId, errTaskCenter)
					tx.Rollback()
					continue
				}

				prizeSource := consts.GetTaskTypeMapRecordPrizeSource()[int(userTaskRow.Type)]
				rewards := service.GetTaskCenterReward(taskCenterInfo[int64(taskCenterId)])
				for _, reward := range rewards {
					if reward.RewardType != consts.PrizeTypeCoupon {
						continue
					}
					prizeNum := reward.RewardNum
					prizeId := reward.RewardCouponId

					prizeType, errGetPrizeType := service.GetCouponType(svcCtx, ctx, prizeId, reward.RewardSource)
					if errGetPrizeType != nil {
						logc.Errorf(ctx, "TaskCallConsumer failed, GetPrizeType failed, uid:%d|businessId:%s |taskCenterId:%d | couponId:%d, err:%v", uid, businessId, taskCenterId, prizeId, errTaskCenter)
						tx.Rollback()
						continue
					}

					recordId, errRecord := dao.NewWelfareMysqlTxDBUtil(tx).InsertExchangeRecord(ctx, uid, prizeId, 0, consts.RecordStatusSuccess, prizeSource, int64(prizeType), prizeNum, userTaskRow.Id)
					if errRecord != nil || recordId <= 0 {
						logc.Errorf(ctx, "TaskCallConsumer failed, InsertExchangeRecord failed, uid:%d| businessId:%s |taskCenterId:%d, err:%v", uid, businessId, taskCenterId, errRecord)
						tx.Rollback()
						continue
					}

					detail := map[string]string{
						"sendPrizeStatus": "SUCCESS",
					}

					recordDetailId, errRecordDetail := dao.NewWelfareMysqlTxDBUtil(tx).InsertUserPrizeDetail(ctx, uid, recordId, rewards[0], detail, time.Now(), time.Now())
					if errRecordDetail != nil || recordDetailId <= 0 {
						logc.Errorf(ctx, "TaskCallConsumer failed, InsertUserPrizeDetail failed, uid:%d|businessId:%s |taskCenterId:%d, err:%v", uid, businessId, taskCenterId, errRecord)
						tx.Rollback()
						continue
					}

					kycDetailId, errKycDetail := dao.NewWelfareMysqlTxDBUtil(tx).InsertUserKycPrizeDetail(ctx, int(uid), int(prizeId), taskCenterId, photoIdEncryption, prizeSource)
					if errKycDetail != nil || kycDetailId <= 0 {
						logc.Errorf(ctx, "TaskCallConsumer failed, InsertUserKycPrizeDetail failed, uid:%d|businessId:%s |taskCenterId:%d, err:%v", uid, businessId, taskCenterId, errRecord)
						tx.Rollback()
						continue
					}
				}

			}
		}
		tx.Commit()

		//删除任务列表
		utils.DelCommonRedisKey(svcCtx, uid, int(userTaskRow.Type), "")
		//删除任务接口缓存
		utils.DelayDelRedisKey(svcCtx, fmt.Sprintf(consts.TaskCenterUserTaskInfoKey, uid, taskCenterId), 0, 3)
		//删除用户领取任务的缓存
		utils.DelayDelRedisKey(svcCtx, fmt.Sprintf(consts.TaskCenterUserTaskBusinessKey, uid, taskCenterId, businessId), 0, 3)

		logc.Infof(ctx, "TaskCallConsumer, update status success, uid:%d|taskStatus:%d | status:%d | businessId:%s", uid, taskStatus, status, businessId)
	}
	return
}

func resetKafkaMsg(ctx context.Context, taskCallMsg interface{}, topic string) {
	// 消费失败，加入到补偿队列，重新消费
	msgMap := utils.StructToMap(taskCallMsg)
	param := &risk.ResetKafkaMessageRequest{
		Topic:   topic,
		Message: msgMap,
		Key:     "",
	}
	err := risk.NewClient().ResetKafkaMessage(ctx, param)
	if err != nil {
		logc.Errorf(ctx, "resetKafkaMsg failed, step: ResetKafkaMessage, param : %v, err: %v", param, err)
	} else {
		logc.Errorf(ctx, "resetKafkaMsg succ, step: ResetKafkaMessage, param : %v", param)
	}
}
