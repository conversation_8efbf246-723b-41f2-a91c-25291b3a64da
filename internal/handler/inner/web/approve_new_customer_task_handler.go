package web

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"

	"gateio_service_welfare_go/internal/logic/inner/web"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

// 新人任务审核上线/重新上线
func ApproveNewCustomerTaskHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ApproveNewCustomerTaskReq
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
			return
		}

		l := web.NewApproveNewCustomerTaskLogic(r.Context(), svcCtx)
		resp, err := l.ApproveNewCustomerTask(&req, r)
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}
