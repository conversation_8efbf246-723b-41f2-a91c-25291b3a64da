package web

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"

	"gateio_service_welfare_go/internal/logic/inner/web"
	"gateio_service_welfare_go/internal/svc"
)

// 获取商品
func GetPrizesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := web.NewGetPrizesLogic(r.Context(), svcCtx)
		resp, err := l.GetPrizes()
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}
