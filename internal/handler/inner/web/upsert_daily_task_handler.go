package web

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"

	"gateio_service_welfare_go/internal/logic/inner/web"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

// 新增/修改每日任务并提交审核
func UpsertDailyTaskHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpsertDailyTaskReq
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
			return
		}

		l := web.NewUpsertDailyTaskLogic(r.Context(), svcCtx)
		resp, err := l.UpsertDailyTask(&req, r)
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}
