package web

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"

	"gateio_service_welfare_go/internal/logic/inner/web"
	"gateio_service_welfare_go/internal/svc"
)

// 拉取多语言信息
func GetLanguageHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := web.NewGetLanguageLogic(r.Context(), svcCtx)
		resp, err := l.GetLanguage()
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}
