package web

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"

	"gateio_service_welfare_go/internal/logic/inner/web"
	"gateio_service_welfare_go/internal/svc"
)

// 获取身份（人群）枚举
func GetIdentityEnumHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := web.NewGetIdentityEnumLogic(r.Context(), svcCtx)
		resp, err := l.GetIdentityEnum()
		if err != nil {
			// code-data 响应格式
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
		} else {
			// code-data 响应格式
			xhttp.JsonBaseResponseCtx(r.Context(), w, resp)
		}
	}
}
