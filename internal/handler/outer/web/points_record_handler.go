package web

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"
	"strconv"

	"gateio_service_welfare_go/internal/logic/outer/web"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

// 分页查询积分记录
func PointsRecordHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		r.ParseForm()
		sourceTypeStr := r.FormValue("source_type")
		sourceType, _ := strconv.Atoi(sourceTypeStr)
		dayStr := r.FormValue("day")
		day, _ := strconv.Atoi(dayStr)
		perPageStr := r.FormValue("per_page")
		perPage, _ := strconv.Atoi(perPageStr)
		//每页条数默认值是10
		if perPage == 0 {
			perPage = 10
		}
		pageStr := r.FormValue("page")
		page, _ := strconv.Atoi(pageStr)
		//默认第一页
		if page == 0 {
			page = 1
		}
		sort := r.FormValue("sort")
		//默认按照时间倒叙排列
		if sort == "" {
			sort = "desc"
		}
		// 处理请求参数
		req := types.PointsRecordReq{
			SourceType: sourceType,
			Day:        day,
			PerPage:    perPage,
			Page:       page,
			Sort:       sort,
		}

		l := web.NewPointsRecordLogic(r.Context(), svcCtx)
		resp, err := l.PointsRecord(&req, r)
		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}
