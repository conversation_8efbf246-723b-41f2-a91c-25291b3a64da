package web

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"

	"gateio_service_welfare_go/internal/logic/outer/web"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

// 测试接口
func GetHelloHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		r.ParseForm()
		name := r.FormValue("name")
		// 处理请求
		req := types.GetHelloReq{
			Name: name,
		}

		l := web.NewGetHelloLogic(r.Context(), svcCtx)
		resp, err := l.Get<PERSON>ello(&req, r)
		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}
