package web

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"

	"gateio_service_welfare_go/internal/logic/outer/web"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

// 签到入口查询
func CheckInEntranceInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UniversalNoParamReq
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
			return
		}

		l := web.NewCheckInEntranceInfoLogic(r.Context(), svcCtx)
		resp, err := l.CheckInEntranceInfo(r)
		response.NewApiV1(r.Context(), w).<PERSON><PERSON>(resp, err)
	}
}
