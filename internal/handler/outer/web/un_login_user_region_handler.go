package web

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"

	"gateio_service_welfare_go/internal/logic/outer/web"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

// 获取未登录态用户区域
func UnLoginUserRegionHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UniversalNoParamReq
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
			return
		}

		l := web.NewUnLoginUserRegionLogic(r.Context(), svcCtx)
		resp, err := l.UnLoginUserRegion(&req, r)
		response.NewApiV1(r.Context(), w).<PERSON><PERSON>(resp, err)
	}
}
