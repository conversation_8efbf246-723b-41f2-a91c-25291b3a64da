package web

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"gateio_service_welfare_go/internal/logic/outer/web"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
	"net/http"
)

// 查询新客入门任务信息
func GetBeginnerTasksHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		r.ParseForm()
		constId := r.FormValue("const_id")
		// 处理请求参数
		req := types.GetBeginnerTasksReq{
			ConstID: constId,
		}
		l := web.NewGetBeginnerTasksLogic(r.Context(), svcCtx)
		resp, err := l.GetBeginnerTasks(&req, r)
		response.NewApiV1(r.Context(), w).Ren<PERSON>(resp, err)
	}
}
