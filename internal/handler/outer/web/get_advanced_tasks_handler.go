package web

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"

	"gateio_service_welfare_go/internal/logic/outer/web"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

// 查询新客进阶任务信息
func GetAdvancedTasksHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		r.ParseForm()
		constId := r.FormValue("const_id")
		// 处理请求参数
		req := types.GetBeginnerTasksReq{
			ConstID: constId,
		}
		l := web.NewGetAdvancedTasksLogic(r.Context(), svcCtx)
		resp, err := l.GetAdvancedTasks(&req, r)
		response.NewApiV1(r.Context(), w).Ren<PERSON>(resp, err)
	}
}
