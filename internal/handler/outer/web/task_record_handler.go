package web

import (
	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"net/http"
	"strconv"

	"gateio_service_welfare_go/internal/logic/outer/web"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

// 分页查询任务记录
func TaskRecordHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		r.ParseForm()
		statusStr := r.FormValue("status")
		status, _ := strconv.Atoi(statusStr)
		dayStr := r.FormValue("day")
		day, _ := strconv.Atoi(dayStr)
		perPageStr := r.FormValue("per_page")
		perPage, _ := strconv.Atoi(perPageStr)
		//每页条数默认值是10
		if perPage == 0 {
			perPage = 10
		}
		pageStr := r.FormValue("page")
		page, _ := strconv.Atoi(pageStr)
		//默认第一页
		if page == 0 {
			page = 1
		}
		sort := r.FormValue("sort")
		//默认按照时间倒叙排列
		if sort == "" {
			sort = "desc"
		}
		// 处理请求参数
		req := types.TaskRecordReq{
			Status:  status,
			Day:     day,
			PerPage: perPage,
			Page:    page,
			Sort:    sort,
		}
		l := web.NewTaskRecordLogic(r.Context(), svcCtx)
		resp, err := l.TaskRecord(&req, r)
		response.NewApiV1(r.Context(), w).Render(resp, err)
	}
}
