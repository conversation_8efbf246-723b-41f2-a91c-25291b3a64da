package web

import (
	"net/http"

	xhttp "bitbucket.org/gatebackend/go-zero/rest/http"
	"bitbucket.org/gatebackend/go-zero/rest/httpx"

	"bitbucket.org/gateio/gateio-lib-base-go/response"
	"gateio_service_welfare_go/internal/logic/outer/web"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
)

// 积分兑换奖品
func PointsExchangeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.PointsExchangeReq
		if err := httpx.Parse(r, &req); err != nil {
			xhttp.JsonErrResponseCtx(r.Context(), w, err)
			return
		}

		l := web.NewPointsExchangeLogic(r.Context(), svcCtx)
		resp, err := l.PointsExchange(&req, r)
		response.NewApiV1(r.Context(), w).<PERSON><PERSON>(resp, err)
	}
}
