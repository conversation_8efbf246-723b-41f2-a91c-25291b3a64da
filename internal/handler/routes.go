// Code generated by goctl. DO NOT EDIT.
package handler

import (
	"net/http"
	"time"

	outerapp "gateio_service_welfare_go/internal/handler/outer/app"
	outerweb "gateio_service_welfare_go/internal/handler/outer/web"
	"gateio_service_welfare_go/internal/svc"

	"bitbucket.org/gatebackend/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// 测试接口
					Method:  http.MethodGet,
					Path:    "/getHello",
					Handler: outerweb.GetHelloHandler(serverCtx),
				},
				{
					// 获取请求IP的国家ID
					Method:  http.MethodGet,
					Path:    "/getNoLoginInfo",
					Handler: outerweb.GetNoLoginInfoHandler(serverCtx),
				},
				{
					// 分页查询领奖记录
					Method:  http.MethodGet,
					Path:    "/prizeRecord",
					Handler: outerweb.PrizeRecordHandler(serverCtx),
				},
				{
					// 分页查询积分记录
					Method:  http.MethodGet,
					Path:    "/pointsRecord",
					Handler: outerweb.PointsRecordHandler(serverCtx),
				},
				{
					// 分页查询任务记录
					Method:  http.MethodGet,
					Path:    "/taskRecord",
					Handler: outerweb.TaskRecordHandler(serverCtx),
				},
				{
					// 积分兑换奖品
					Method:  http.MethodPost,
					Path:    "/pointsExchange",
					Handler: outerweb.PointsExchangeHandler(serverCtx),
				},
				{
					// 积分兑换奖品列表
					Method:  http.MethodGet,
					Path:    "/pointsPrizeList",
					Handler: outerweb.PointsPrizeListHandler(serverCtx),
				},
				{
					// 上报弹窗
					Method:  http.MethodPost,
					Path:    "/uploadPopUp",
					Handler: outerweb.UploadPopUpHandler(serverCtx),
				},
				{
					// 签到
					Method:  http.MethodPost,
					Path:    "/checkIn",
					Handler: outerweb.CheckInHandler(serverCtx),
				},
				{
					// 获取签到任务初始化信息
					Method:  http.MethodGet,
					Path:    "/checkInInit",
					Handler: outerweb.CheckInInitHandler(serverCtx),
				},
				{
					// info信息
					Method:  http.MethodGet,
					Path:    "/info",
					Handler: outerweb.CheckInEntranceInfoHandler(serverCtx),
				},
				{
					// 获取用户身份
					Method:  http.MethodGet,
					Path:    "/getUserIdentity",
					Handler: outerweb.GetUserIdentityHandler(serverCtx),
				},
				{
					// 获取新客奖励信息
					Method:  http.MethodGet,
					Path:    "/newbiePrizeGuide",
					Handler: outerweb.NewbiePrizeGuideHandler(serverCtx),
				},
				{
					// 查询新客入门任务信息
					Method:  http.MethodGet,
					Path:    "/getBeginnerTasks",
					Handler: outerweb.GetBeginnerTasksHandler(serverCtx),
				},
				{
					// 查询新客进阶任务信息
					Method:  http.MethodGet,
					Path:    "/getAdvancedTasks",
					Handler: outerweb.GetAdvancedTasksHandler(serverCtx),
				},
				{
					// 老客每日任务列表
					Method:  http.MethodGet,
					Path:    "/dailyTaskList",
					Handler: outerweb.DailyTaskListHandler(serverCtx),
				},
				{
					// 老客限时任务列表
					Method:  http.MethodGet,
					Path:    "/limitTaskList",
					Handler: outerweb.LimitTaskListHandler(serverCtx),
				},
				{
					// 领取任务
					Method:  http.MethodPost,
					Path:    "/receiveTask",
					Handler: outerweb.ReceiveTaskHandler(serverCtx),
				},
				{
					// 领取奖励
					Method:  http.MethodPost,
					Path:    "/receivePrize",
					Handler: outerweb.ReceivePrizeHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/web/v1/welfare-center"),
		rest.WithTimeout(10000*time.Millisecond),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthAllowMiddleware},
			[]rest.Route{
				{
					// 测试接口
					Method:  http.MethodGet,
					Path:    "/getHello",
					Handler: outerapp.GetHelloHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/app/v1/welfare-center"),
		rest.WithTimeout(5000*time.Millisecond),
	)
}
