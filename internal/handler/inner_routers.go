// Code generated by goctl. DO NOT EDIT.
package handler

import (
	innerweb "gateio_service_welfare_go/internal/handler/inner/web"
	"net/http"
	"time"

	"gateio_service_welfare_go/internal/svc"

	"bitbucket.org/gatebackend/go-zero/rest"
)

func RegisterInnerHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.ReplaceHtmlEntityMiddleware},
			[]rest.Route{
				{
					// 测试接口
					Method:  http.MethodGet,
					Path:    "/getInnerHello",
					Handler: innerweb.GetInnerHelloHandler(serverCtx),
				},
				{
					// 获取身份（人群）枚举
					Method:  http.MethodGet,
					Path:    "/getIdentityEnum",
					Handler: innerweb.GetIdentityEnumHandler(serverCtx),
				},
				{
					// 拉取卡券信息
					Method:  http.MethodGet,
					Path:    "/getCouponInfo",
					Handler: innerweb.GetCouponInfoHandler(serverCtx),
				},
				{
					// 拉取任务信息
					Method:  http.MethodGet,
					Path:    "/getTaskInfo",
					Handler: innerweb.GetTaskInfoHandler(serverCtx),
				},
				{
					// 拉取多语言信息
					Method:  http.MethodGet,
					Path:    "/getLanguage",
					Handler: innerweb.GetLanguageHandler(serverCtx),
				},
				{
					// 获取地区配置
					Method:  http.MethodGet,
					Path:    "/getNewCustomerStrategy",
					Handler: innerweb.GetNewCustomerStrategyHandler(serverCtx),
				},
				{
					// 新增/修改地区配置
					Method:  http.MethodPost,
					Path:    "/upsertNewCustomerStrategy",
					Handler: innerweb.UpsertNewCustomerStrategyHandler(serverCtx),
				},
				{
					// 获取入门任务
					Method:  http.MethodGet,
					Path:    "/getWelcomeTasks",
					Handler: innerweb.GetWelcomeTasksHandler(serverCtx),
				},
				{
					// 新增/修改注册任务并提交审核
					Method:  http.MethodPost,
					Path:    "/upsertRegistTask",
					Handler: innerweb.UpsertRegistTaskHandler(serverCtx),
				},
				{
					// 新增/修改其他任务并提交审核
					Method:  http.MethodPost,
					Path:    "/upsertWelcomeTask",
					Handler: innerweb.UpsertWelcomeTaskHandler(serverCtx),
				},
				{
					// 获取进阶任务
					Method:  http.MethodGet,
					Path:    "/getAdvancedTasks",
					Handler: innerweb.GetAdvancedTasksHandler(serverCtx),
				},
				{
					// 新增/修改进阶任务并提交审核
					Method:  http.MethodPost,
					Path:    "/upsertAdvancedTask",
					Handler: innerweb.UpsertAdvancedTaskHandler(serverCtx),
				},
				{
					// 新人任务 拖动排序
					Method:  http.MethodPost,
					Path:    "/sortNewCustomerTask",
					Handler: innerweb.SortNewCustomerTaskHandler(serverCtx),
				},
				{
					// 新人任务审核上线/重新上线
					Method:  http.MethodPost,
					Path:    "/approveNewCustomerTask",
					Handler: innerweb.ApproveNewCustomerTaskHandler(serverCtx),
				},
				{
					// 新人任务直接下线
					Method:  http.MethodPost,
					Path:    "/offlineNewCustomerTask",
					Handler: innerweb.OfflineNewCustomerTaskHandler(serverCtx),
				},
				{
					// 获取每日任务
					Method:  http.MethodGet,
					Path:    "/getDailyTasks",
					Handler: innerweb.GetDailyTasksHandler(serverCtx),
				},
				{
					// 新增/修改每日任务并提交审核
					Method:  http.MethodPost,
					Path:    "/upsertDailyTask",
					Handler: innerweb.UpsertDailyTaskHandler(serverCtx),
				},
				{
					// 获取限时任务
					Method:  http.MethodGet,
					Path:    "/getLimitTimeTasks",
					Handler: innerweb.GetLimitTimeTasksHandler(serverCtx),
				},
				{
					// 新增/修改限时任务并提交审核
					Method:  http.MethodPost,
					Path:    "/upsertLimitTimeTask",
					Handler: innerweb.UpsertLimitTimeTaskHandler(serverCtx),
				},
				{
					// 老客新人拖动排序
					Method:  http.MethodPost,
					Path:    "/sortOldCustomerTask",
					Handler: innerweb.SortOldCustomerTaskHandler(serverCtx),
				},
				{
					// 老客审核上线/重新上线
					Method:  http.MethodPost,
					Path:    "/approveOldCustomerTask",
					Handler: innerweb.ApproveOldCustomerTaskHandler(serverCtx),
				},
				{
					// 老客直接下线
					Method:  http.MethodPost,
					Path:    "/offlineOldCustomerTask",
					Handler: innerweb.OfflineOldCustomerTaskHandler(serverCtx),
				},
				{
					// 获取商品
					Method:  http.MethodGet,
					Path:    "/getPrizes",
					Handler: innerweb.GetPrizesHandler(serverCtx),
				},
				{
					// 新增/修改商品
					Method:  http.MethodPost,
					Path:    "/upsertPrize",
					Handler: innerweb.UpsertPrizeHandler(serverCtx),
				},
				{
					// 商品拖动排序
					Method:  http.MethodPost,
					Path:    "/sortPrize",
					Handler: innerweb.SortPrizeHandler(serverCtx),
				},
				{
					// 商品下架
					Method:  http.MethodPost,
					Path:    "/offlinePrize",
					Handler: innerweb.OfflinePrizeHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/api/inner/v1/welfare-center"),
		rest.WithTimeout(10000*time.Millisecond),
	)
}
