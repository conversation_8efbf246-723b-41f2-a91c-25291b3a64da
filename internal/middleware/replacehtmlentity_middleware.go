package middleware

import (
	"bytes"
	"encoding/json"
	"html"
	"io"
	"net/http"
)

type ReplaceHtmlEntityMiddleware struct{}

func NewReplaceHtmlEntityMiddleware() *ReplaceHtmlEntityMiddleware {
	return &ReplaceHtmlEntityMiddleware{}
}

func (m *ReplaceHtmlEntityMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if r.Header.Get("Content-Type") == "application/json" {
			bodyBytes, err := io.ReadAll(r.Body)
			if err == nil && len(bodyBytes) > 0 {
				// 反序列化为 map
				var data interface{}
				if err := json.Unmarshal(bodyBytes, &data); err == nil {
					// 递归处理所有 string 字段
					replaceHtmlEntity(&data)
					// 重新序列化
					newBody, _ := json.Marshal(data)
					// 替换 request.Body
					r.Body = io.NopCloser(bytes.NewBuffer(newBody))
					r.ContentLength = int64(len(newBody))
				} else {
					// 反序列化失败，恢复原 body
					r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
				}
			}
		}
		next(w, r)
	}
}

// 递归处理所有 string 字段
func replaceHtmlEntity(data *interface{}) {
	switch v := (*data).(type) {
	case map[string]interface{}:
		for key, val := range v {
			replaceHtmlEntity(&val)
			v[key] = val
		}
	case []interface{}:
		for i, item := range v {
			replaceHtmlEntity(&item)
			v[i] = item
		}
	case string:
		*data = html.UnescapeString(v)
	}
}
