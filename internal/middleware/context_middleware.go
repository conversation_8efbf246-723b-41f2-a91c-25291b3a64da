package middleware

import (
	"context"
	"net/http"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logc"
)

// ContextMiddleware Context处理中间件
type ContextMiddleware struct {
	timeout time.Duration
}

// NewContextMiddleware 创建Context中间件
func NewContextMiddleware(timeout time.Duration) *ContextMiddleware {
	return &ContextMiddleware{
		timeout: timeout,
	}
}

// Handle 处理请求
func (m *ContextMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 获取原始context
		ctx := r.Context()

		// 创建带超时的context
		timeoutCtx, cancel := context.WithTimeout(ctx, m.timeout)
		defer cancel()

		// 创建新的请求对象
		r = r.WithContext(timeoutCtx)

		// 记录请求开始
		startTime := time.Now()
		logc.Infof(timeoutCtx, "Request started: %s %s", r.<PERSON>, r.URL.Path)

		// 创建响应监控
		done := make(chan struct{})
		go func() {
			defer close(done)
			next(w, r)
		}()

		// 等待请求完成或超时
		select {
		case <-done:
			// 请求正常完成
			duration := time.Since(startTime)
			logc.Infof(timeoutCtx, "Request completed: %s %s duration=%v", r.Method, r.URL.Path, duration)

		case <-timeoutCtx.Done():
			// 请求超时或被取消
			duration := time.Since(startTime)
			err := timeoutCtx.Err()

			if err == context.DeadlineExceeded {
				logc.Errorf(timeoutCtx, "Request timeout: %s %s duration=%v", r.Method, r.URL.Path, duration)
				http.Error(w, "Request timeout", http.StatusRequestTimeout)
			} else if err == context.Canceled {
				logc.Warnf(timeoutCtx, "Request canceled: %s %s duration=%v", r.Method, r.URL.Path, duration)
				http.Error(w, "Request canceled", http.StatusRequestTimeout)
			}
		}
	}
}

// ContextErrorMiddleware Context错误处理中间件
type ContextErrorMiddleware struct{}

// NewContextErrorMiddleware 创建Context错误处理中间件
func NewContextErrorMiddleware() *ContextErrorMiddleware {
	return &ContextErrorMiddleware{}
}

// Handle 处理Context错误
func (m *ContextErrorMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// 检查context状态
		select {
		case <-ctx.Done():
			logc.Warnf(ctx, "Request received with already canceled context: %s %s", r.Method, r.URL.Path)
			http.Error(w, "Request context already canceled", http.StatusRequestTimeout)
			return
		default:
			// context还没有被取消，继续处理
		}

		// 设置panic恢复
		defer func() {
			if err := recover(); err != nil {
				logc.Errorf(ctx, "Panic in request handler: %s %s error=%v", r.Method, r.URL.Path, err)
				http.Error(w, "Internal server error", http.StatusInternalServerError)
			}
		}()

		next(w, r)
	}
}

// ContextStatsMiddleware Context统计中间件
type ContextStatsMiddleware struct{}

// NewContextStatsMiddleware 创建Context统计中间件
func NewContextStatsMiddleware() *ContextStatsMiddleware {
	return &ContextStatsMiddleware{}
}

// Handle 处理Context统计
func (m *ContextStatsMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// 记录请求信息
		logc.Infof(ctx, "Context stats - processing request: %s %s", r.Method, r.URL.Path)

		next(w, r)
	}
}
