package middleware

import (
	"bitbucket.org/gateio/gateio-lib-common-go/middleware"
	"bitbucket.org/gateio/gateio-lib-common-go/middleware/httphandler"
	"net/http"
)

type AuthAllowMiddleware struct {
}

func NewAuthAllowMiddleware() *AuthAllowMiddleware {
	return &AuthAllowMiddleware{}
}

func (m *AuthAllowMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return middleware.Authorize(AuthAllow())(next)
}

func AuthAllow() httphandler.AuthOptionsFunc {
	return func(op *httphandler.AuthOptions) {
		op.AllowNonAuth = true
	}
}
