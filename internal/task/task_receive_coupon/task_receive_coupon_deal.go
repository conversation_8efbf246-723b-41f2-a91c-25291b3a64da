package task_receive_coupon

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/service/kafka_producer"
	"gateio_service_welfare_go/internal/service/prize_service"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"strconv"
	"time"
)

type TaskReceiveParams struct {
	StartTime string `json:"start_time"`
}

// 手动执行需要传入时间参数作为查询的开始时间
func TaskReceiveCouponWarningDo(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {
	PrintJSON(param)
	logData("TaskReceiveCouponWarningDo start", WarningPrefix)
	traceID := trace.TraceIDFromContext(ctx)
	logc.Info(ctx, fmt.Sprintf("traceID: %s", traceID))
	boolSwitch, err := service.GetSystemSwitchByKey(svcCtx, ctx, consts.WelfareConfigCouponSend)
	if err != nil {
		logc.Errorf(ctx, "TaskReceiveCouponWarningDo GetSystemSwitchByKey is failed;err is:%v,config_key is:%v ,traceId is:%v", err.Error(), consts.WelfareConfigCouponSend, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	if boolSwitch == false {
		logc.Warnf(ctx, "TaskReceiveCouponWarningDo GetSystemSwitchByKey boolSwitch is false, config_key is:%v ,traceId is:%v", consts.WelfareConfigCouponSend, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	boolSwitch, err = service.GetSystemSwitchByKey(svcCtx, ctx, consts.WelfareConfigTaskSend)
	if err != nil {
		logc.Errorf(ctx, "TaskReceiveCouponWarningDo GetSystemSwitchByKey is failed;err is:%v,config_key is:%v ,traceId is:%v", err.Error(), consts.WelfareConfigTaskSend, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	if boolSwitch == false {
		logc.Warnf(ctx, "TaskReceiveCouponWarningDo GetSystemSwitchByKey boolSwitch is false, config_key is:%v ,traceId is:%v", consts.WelfareConfigTaskSend, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	if param.ExecutorParams == "" {
		logc.Warnf(ctx, "TaskReceiveCouponWarningDo param.ExecutorParams is failed, param.ExecutorParams is:%v ,traceId is:%v", param.ExecutorParams, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	rp := &TaskReceiveParams{}
	err = json.Unmarshal([]byte(param.ExecutorParams), rp)
	if err != nil {
		logc.Warnf(ctx, "TaskReceiveCouponWarningDo json.Unmarshal param.ExecutorParams is failed, param.ExecutorParams is:%v ,traceId is:%v,err is:%v", param.ExecutorParams, traceID, err)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	TaskReceiveCouponDo(ctx, svcCtx, traceID, rp.StartTime)
	logData("TaskReceiveCouponWarningDo end", WarningPrefix)
	return fmt.Sprintf("traceID: %s", traceID)
}

// 发送预警和奖励预警 dealType 1:定时执行预警脚本 2:给定时间参数补单脚本
func TaskReceiveCouponDo(ctx context.Context, svcCtx *svc.ServiceContext, traceID string, startTime string) {
	warningMsg := "【新福利中心】任务奖励领取异常自动补单 \n 当前时段异常的记录和补单结果如下："
	isSendMsg := false
	pageSize := 500
	for i := TableNameNumStart; i < TableNameNumEnd; i++ {
		lastId := int64(0)
		recordNum := 0
		tableName := dao.GetWelfareExchangeRecordTableName(i)
		for {
			recordList := []*dao.WelfareExchangeRecord{}
			// 构建查询
			err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.WithContext(ctx).Table(tableName).
				Where("status in (?)", []string{consts.RecordStatusInit, consts.RecordStatusFailed}).
				Where("prize_source in (?)", []string{
					consts.RecordPrizeSourceNewbieTaskNew,
					consts.RecordPrizeSourceLimitedTimeTasks,
					consts.RecordPrizeSourceLimitedTask,
					consts.RecordPrizeSourceDailyTasks,
					consts.RecordPrizeSourceCheckinTasks,
				}).
				Where("updated_at >= ?", startTime).
				Where("id > ?", lastId).Order("id asc").Limit(pageSize).
				Find(&recordList).Error
			if err != nil {
				logc.Errorf(ctx, "TaskReceiveCouponDo query WelfareExchangeRecord err is:%v, TraceID is: %s", err, traceID)
				return
			}
			if len(recordList) == 0 {
				logc.Infof(ctx, "TaskReceiveCouponDo abnormal recordList len is 0 table name is:%s, TraceID is: %s", tableName, traceID)
				break
			}

			for _, exchangeRecord := range recordList {
				lastId = exchangeRecord.ID
				recordNum++
				//获取WelfareUserPrizeDetail信息
				prizeDetail, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetUserPrizeDetail(ctx, exchangeRecord.UID, exchangeRecord.ID)
				if err != nil {
					logc.Errorf(ctx, "TaskReceiveCouponDo  GetUserPrizeDetail is err , err is：%v, uid is:%d ", err, exchangeRecord.UID)
					continue
				}
				//签到领取积分需要特殊处理
				if exchangeRecord.PrizeSource == consts.RecordPrizeSourceCheckinTasks {
					isSendMsg = true
					warningMsg += fmt.Sprintf("\n uid:%v , prize_datail_id:%v , exchange_record_id:%v", exchangeRecord.UID, prizeDetail.ID, exchangeRecord.ID)
					checkInCouponId := 0
					checkInCouponSource := ""
					if utils.CheckDev() {
						checkInCouponId = consts.GetRealCouponId(consts.CheckInCouponId)
						checkInCouponSource = consts.GetRealSource(consts.CheckInSource)
					} else {
						checkInCouponId = consts.CheckInCouponId
						checkInCouponSource = consts.CheckInSource
					}
					resp, err := service_client.NewCouponCenterCall(ctx).CheckSendCouponProgress(checkInCouponSource, fmt.Sprintf("%d_%d", exchangeRecord.UID, exchangeRecord.ID))
					if err != nil {
						logc.Errorf(ctx, "TaskReceiveCouponDo CheckSendCouponProgress is err , err is：%v, uid is:%d ", err, exchangeRecord.UID)
					}
					if resp != nil {
						warningMsg += fmt.Sprintf(" , check_send:%v", resp.SendStatus)
					}
					respByte, _ := json.Marshal(resp)
					logc.Infof(ctx, "TaskReceiveCouponDo CheckSendCouponProgress uid is: %d,user_task_id is:%d ,coupon_id is:%d  , coupon_source is：%s, CouponProgress val is:%s", exchangeRecord.UID, exchangeRecord.TaskID, checkInCouponId, checkInCouponSource, string(respByte))
					//发奖中状态直接跳过
					if resp != nil && resp.SendStatus == "SEND_INIT" {
						logc.Infof(ctx, "TaskReceiveCouponDo CheckSendCouponProgress is SEND_INIT, uid is:%d ", exchangeRecord.UID)
						continue
					}
					detail := map[string]string{
						"sendPrizeStatus": "INIT",
					}
					status := consts.RecordStatusFailed
					//卡劵中心发放成功
					if resp != nil && resp.SendStatus == "SEND_SUCCESS" {
						warningMsg += fmt.Sprintf(" , send_status:%v", "SUCCESS")
						detail["sendPrizeStatus"] = "SUCCESS"
						status = consts.RecordStatusSuccess
					} else {
						//请求卡券发放奖励
						sendCouponFormatResp := service.SendCouponPrice(ctx, exchangeRecord.UID, int64(checkInCouponId), strconv.FormatInt(exchangeRecord.ID, 10), "", checkInCouponSource)
						sendCouponFormatRespByte, _ := json.Marshal(sendCouponFormatResp)
						logc.Infof(ctx, "TaskReceiveCouponDo CheckSendCouponProgress, uid is:%d ,sendCouponFormatResp is:%s", exchangeRecord.UID, string(sendCouponFormatRespByte))
						if sendCouponFormatResp.Code == consts.Success {
							detail["sendPrizeStatus"] = "SUCCESS"
							status = consts.RecordStatusSuccess
							warningMsg += fmt.Sprintf(" , send_status:%v", "SUCCESS")
						} else {
							detail["sendPrizeStatus"] = "FAILED"
							if sendCouponFormatResp.CouponCode == -1 {
								detail["sendCouponStatus"] = "FAILED"
							}
							warningMsg += fmt.Sprintf(" , send_status:%v", "FAILD")
						}
					}
					//当调用任务中心发奖成功之后会出现INIT的情况 , 调用任务中心发奖成功之后不做处理
					if detail["sendPrizeStatus"] != "INIT" {
						// 开启事务
						tx2 := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.Begin()

						prizeService := prize_service.NewPrizeService(ctx, svcCtx)
						updateExchangeBool := prizeService.UpdateExchangeStatusById(tx2, int(exchangeRecord.UID), int(exchangeRecord.ID), int(prizeDetail.ID), status, detail, checkInCouponId, checkInCouponSource, 0, "")
						if !updateExchangeBool {
							tx2.Rollback()
							logc.Warnf(ctx, "TaskReceiveCouponDo UpdateExchangeStatusById is failed; uid is:%d ", exchangeRecord.UID)
						}
						tx2.Commit()
					}
					continue
				}
				userTask := &welfare_user_tasks.WelfareUserTasks{}
				//查询出领奖中的任务 过滤掉进阶任务，进阶任务由任务中心发奖
				err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.WithContext(ctx).
					Where("id = ?", exchangeRecord.TaskID).
					Where("uid = ?", exchangeRecord.UID).First(userTask).Error
				if err != nil {
					logc.Errorf(ctx, "TaskReceiveCouponDo get userTask err is:%v, TraceID is: %s", err, traceID)
					return
				}
				//发奖失败直接过滤不做处理
				if userTask.Status == consts.StatusExpire {
					logc.Infof(ctx, "TaskReceiveCouponDo user_task_status is:%v, TraceID is: %s", consts.StatusExpire, traceID)
					continue
				}
				isSendMsg = true
				warningMsg += fmt.Sprintf("\n uid:%v , prize_datail_id:%v , exchange_record_id:%v", exchangeRecord.UID, prizeDetail.ID, exchangeRecord.ID)
				warningMsg += fmt.Sprintf(" , user_task_id:%v", userTask.Id)
				//获取任务信息
				taskInfo := &welfare_task_cfgs.WelfareTaskCfgs{}
				err = json.Unmarshal([]byte(userTask.Memo), taskInfo)
				if err != nil {
					logc.Errorf(ctx, "TaskReceiveCouponDo json.Unmarshal([]byte(userTask.Memo),taskInfo) is err,params is:%s,%d  , err is：%v", userTask.Memo, userTask.Uid, err)
					continue
				}
				taskCenterMap := map[int64]*task.Task{}
				if userTask.TaskCenterId > 0 {
					//获取任务中心任务信息
					taskCenterMap, err = service.GetTaskListByTaskIds(svcCtx, ctx, int(userTask.Uid), []int64{userTask.TaskCenterId})
					if err != nil {
						logc.Errorf(ctx, "TaskReceiveCouponDo uid is: %d, GetTaskListByTaskIds is error  , Params is %v, err is:%v", userTask.Uid, []int64{userTask.TaskCenterId}, err)
						continue
					}
				}
				centerTask := taskCenterMap[userTask.TaskCenterId]
				if (centerTask == nil || centerTask.TaskID == 0) && userTask.Type != consts.UserTaskTypeNewbieRegister {
					logc.Errorf(ctx, "TaskReceiveCouponDo uid is: %d, GetTaskListByTaskIds is error  , Params is %v, err is:%v", userTask.Uid, []int64{userTask.TaskCenterId}, err)
					continue
				}
				//定义卡劵奖励列表
				couponList := []*CouponInfo{}
				if taskInfo.Type == consts.UserTaskTypeNewbieRegister {
					registerTaskInfo := dao.RegisterTaskInfo{}
					err = json.Unmarshal([]byte(taskInfo.ExtraTaskInfo.String), &registerTaskInfo)
					if err != nil {
						logc.Errorf(ctx, "TaskReceiveCouponDo json.Unmarshal.task.ExtraTaskInfo is err,params is:%s,%d  , err is：%v", taskInfo.ExtraTaskInfo.String, userTask.Uid, err)
						continue
					}
					if registerTaskInfo.CouponId == 0 || registerTaskInfo.Source == "" {
						logc.Errorf(ctx, "TaskReceiveCouponDo registerTaskInfo is err,params is:%s, %d  , err is：%v", taskInfo.ExtraTaskInfo.String, userTask.Uid, err)
						continue
					}
					couponList = append(couponList, &CouponInfo{
						CouponId: registerTaskInfo.CouponId,
						Source:   registerTaskInfo.Source,
					})
				} else {
					//获取奖励信息
					rewards := service.GetTaskCenterReward(centerTask)
					for _, reward := range rewards {
						//获取卡句爱你奖励信息
						if reward.RewardType == consts.PrizeTypeCoupon {
							if reward.RewardCouponId > 0 && reward.RewardSource != "" {
								couponList = append(couponList, &CouponInfo{
									CouponId: reward.RewardCouponId,
									Source:   reward.RewardSource,
								})
							}
							if reward.ExtraRewardCouponId > 0 && reward.ExtraRewardSource != "" {
								couponList = append(couponList, &CouponInfo{
									CouponId: reward.ExtraRewardCouponId,
									Source:   reward.ExtraRewardSource,
								})
							}
						}
					}
				}
				//获取当前任务奖励的信息
				couponId := int64(0)
				source := ""
				for _, couponInfo := range couponList {
					logc.Infof(ctx, "couponInfo data is: %d, %s", couponInfo.CouponId, couponInfo.Source)
					//对比出奖励信息
					if int(couponInfo.CouponId) == exchangeRecord.PrizeID {
						couponId = couponInfo.CouponId
						source = couponInfo.Source
					}
				}
				if couponId == 0 || source == "" {
					logc.Warnf(ctx, "TaskReceiveCouponDo couponId,source is nil,user_task_id is:%d", userTask.Id)
					continue
				}
				resp, err := service_client.NewCouponCenterCall(ctx).CheckSendCouponProgress(source, fmt.Sprintf("%d_%d", exchangeRecord.UID, exchangeRecord.ID))
				if err != nil {
					logc.Errorf(ctx, "TaskReceiveCouponDo CheckSendCouponProgress is err , err is：%v, uid is:%d ", err, exchangeRecord.UID)
				}
				if resp != nil {
					warningMsg += fmt.Sprintf(" , check_send:%v", resp.SendStatus)
				}
				respByte, _ := json.Marshal(resp)
				logc.Infof(ctx, "TaskReceiveCouponDo CheckSendCouponProgress uid is: %d,user_task_id is:%d ,coupon_id is:%d  , coupon_source is：%s, CouponProgress val is:%s", exchangeRecord.UID, exchangeRecord.TaskID, couponId, source, string(respByte))
				//发奖中状态直接跳过
				if resp != nil && resp.SendStatus == "SEND_INIT" {
					logc.Infof(ctx, "TaskReceiveCouponDo CheckSendCouponProgress is SEND_INIT, uid is:%d ", userTask.Uid)
					continue
				}
				detail := map[string]string{
					"sendPrizeStatus": "INIT",
				}
				status := consts.RecordStatusFailed
				//卡劵中心发放成功
				if resp != nil && resp.SendStatus == "SEND_SUCCESS" {
					warningMsg += fmt.Sprintf(" , send_status:%v", "SUCCESS")
					detail["sendPrizeStatus"] = "SUCCESS"
					status = consts.RecordStatusSuccess
				} else {
					//请求卡券发放奖励
					sendCouponFormatResp := service.SendCouponPrice(ctx, userTask.Uid, couponId, strconv.FormatInt(exchangeRecord.ID, 10), "", source)
					sendCouponFormatRespByte, _ := json.Marshal(sendCouponFormatResp)
					logc.Infof(ctx, "TaskReceiveCouponDo CheckSendCouponProgress, uid is:%d ,sendCouponFormatResp is:%s", userTask.Uid, string(sendCouponFormatRespByte))
					if sendCouponFormatResp.Code == consts.Success {
						detail["sendPrizeStatus"] = "SUCCESS"
						status = consts.RecordStatusSuccess
						warningMsg += fmt.Sprintf(" , send_status:%v", "SUCCESS")
					} else {
						detail["sendPrizeStatus"] = "FAILED"
						if sendCouponFormatResp.CouponCode == -1 {
							detail["sendCouponStatus"] = "FAILED"
						}
						warningMsg += fmt.Sprintf(" , send_status:%v", "FAILD")
					}
				}
				//当调用任务中心发奖成功之后会出现INIT的情况 , 调用任务中心发奖成功之后不做处理
				if detail["sendPrizeStatus"] != "INIT" {
					prizeSource := consts.GetTaskTypeMapRecordPrizeSource()[int(taskInfo.Type)]
					err = prize_service.UpdateRewardStatus(svcCtx, ctx, int(userTask.Uid), int(exchangeRecord.ID), int(prizeDetail.ID), status, detail, int(couponId), prizeSource, userTask.Id, int(userTask.TaskCenterId), "")
					if err != nil {
						logc.Errorf(ctx, "TaskReceiveCouponDo UpdateRewardStatus is err , err is：%v, uid id:%d", err, userTask.Uid)
						continue
					}
					//卡券发放成功，需要上报奖励 注册任务不需要上报
					if status == consts.RecordStatusSuccess && userTask.Type != consts.UserTaskTypeNewbieRegister {
						kafkaProducer, errKafkaInit := kafka_producer.NewKafkaProducer(svcCtx.KafkaConf)
						if errKafkaInit != nil {
							logc.Errorf(ctx, "TaskReceiveCouponDo NewKafkaProducer is err , err is：%v, uid id:%d", errKafkaInit, userTask.Uid)
						}
						defer kafkaProducer.Close()
						errKafka := kafkaProducer.TaskReceivePrizeSend(ctx, userTask.Uid, userTask.TaskCenterId, consts.WelfareTaskBusinessType, time.Now().Unix(), strconv.FormatInt(userTask.Id, 10))
						if errKafka != nil {
							logc.Errorf(ctx, "TaskReceiveCouponDo Producer.TaskRecordReceiveProducer failed：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d, err:%v", userTask.Uid, userTask.Id, userTask.TaskCenterId, consts.WelfareTaskBusinessType, err)
						} else {
							logc.Infof(ctx, "TaskReceiveCouponDo Producer.TaskRecordReceiveProducer succ：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d", userTask.Uid, userTask.Id, userTask.TaskCenterId, consts.WelfareTaskBusinessType)
						}
					}
				}
			}
			if len(recordList) < pageSize {
				logc.Infof(ctx, "TaskReceiveCouponDo finish table name is：%s,record num is:%d ", tableName, recordNum)
				break
			}
		}
	}
	if isSendMsg {
		SendWarningMsg(ctx, warningMsg, "online", traceID)
	}
}
