package clean_user_points

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_points_records"
	"gateio_service_welfare_go/internal/models/welfare_user_points"
	"gateio_service_welfare_go/internal/svc"
	"time"
)

const (
	CleanPointsStartTime = "2026-06-30 00:00:00"
	ActionIncr           = "incr"
)

var SumPoint struct {
	SumPoints int64 `gorm:"column:sum_points"`
}

// 可以每天执行一次，也可以使用参数加[1234,3456] uid列表执行
func CleanUserPoints(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {
	logc.Infof(ctx, "执行Welfare任务 CleanUserPoints : %d", param.JobID)

	PrintJSON(param)
	traceID := trace.TraceIDFromContext(ctx)
	logc.Info(ctx, fmt.Sprintf("traceID: %s", traceID))
	//第一清理的开始时间是25年12月31日
	if time.Now().Format(time.DateTime) < CleanPointsStartTime {
		logc.Infof(ctx, "CleanUserPoints clean have not started, now time is: %s", time.Now().Format(time.DateTime))
		return fmt.Sprintf("traceID: %s", traceID)
	}
	//公式：当前余额  ➖ 近半年获取的积分 ＞ 0 需要过期

	//当前时间戳
	nowUnix := time.Now().Unix()
	// 获取当前年份
	currentYear := time.Now().Year()

	// 获取当年6月30号时间戳
	juneUnix := time.Date(currentYear, time.June, 30, 0, 0, 0, 0, time.Local).Unix()
	//获取当年12月31号时间戳
	decemberUnix := time.Date(currentYear, time.December, 31, 0, 0, 0, 0, time.Local).Unix()
	//获取本次清0的开始时间戳
	useTimeUnix := int64(0)
	//如果当前时间小于本年的6月30日
	if nowUnix < juneUnix {
		//本次清0时间就是上一年的12月31号
		useTimeUnix = time.Date(currentYear-1, time.December, 31, 0, 0, 0, 0, time.Local).Unix()
	} else {
		//本次清0时间是本年的6月1号
		useTimeUnix = time.Date(currentYear, time.June, 30, 0, 0, 0, 0, time.Local).Unix()
	}
	//如果当前时间大于本年12月31号
	if nowUnix >= decemberUnix {
		//本次清0时间就是本年的12月31号
		useTimeUnix = time.Date(currentYear, time.December, 31, 0, 0, 0, 0, time.Local).Unix()
	}
	//获取计算近半年积分的开始时间
	getPointStartTimeStr := ""
	useTime := time.Unix(useTimeUnix, 0)
	if useTime.Month() == time.June {
		getPointStartTimeStr = time.Date(useTime.Year(), time.January, 1, 0, 0, 0, 0, time.Local).Format(time.DateTime)
	} else {
		getPointStartTimeStr = time.Date(useTime.Year(), time.July, 1, 0, 0, 0, 0, time.Local).Format(time.DateTime)
	}
	logc.Infof(ctx, "本次清0半年的结束时间是：%s，本次清0操作的开始时间是：%s", getPointStartTimeStr, useTime.Format(time.DateTime))
	//获取参数中的用户ID
	userIds := []int64{}
	if param.ExecutorParams != "" {
		err := json.Unmarshal([]byte(param.ExecutorParams), &userIds)
		if err != nil {
			logc.Errorf(ctx, "CleanUserPoints json.Unmarshal param.ExecutorParams err: %v,param.ExecutorParams is: %s", err, param.ExecutorParams)
			return fmt.Sprintf("traceID: %s", traceID)
		}
	}
	lastId := int64(0)
	pageSize := 500
	userNum := 0
	for {
		userPoints := []*welfare_user_points.WelfareUserPointsNew{}
		queryUser := svcCtx.WelfareDB.WithContext(ctx).Model(&welfare_user_points.WelfareUserPointsNew{}).
			Where("clean_point_time < ?", useTimeUnix).
			Where("id > ?", lastId)
		if len(userIds) > 0 {
			queryUser = queryUser.Where("uid in (?)", userIds)
		}
		err := queryUser.Order("id asc").Limit(pageSize).Find(&userPoints).Error
		if err != nil {
			logc.Errorf(ctx, "CleanUserPoints query userPoints is error ，Params is：%d, err is:%v", useTimeUnix, err)
			break
		}
		for _, userPoint := range userPoints {
			lastId = userPoint.Id
			userNum++
			//获取近半年的用户积分
			sumPoint := &SumPoint
			err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.Model(&welfare_points_records.WelfarePointsRecords{}).
				Where("action = ?", ActionIncr).
				Where("uid = ?", userPoint.Uid).
				Where("updated_at <= ?", time.Now().Format(time.DateTime)).
				Where("updated_at >= ?", getPointStartTimeStr).
				Select("SUM(points) as sum_points").First(sumPoint).Error
			if err != nil {
				logc.Errorf(ctx, "CleanUserPoints get sumDecrPoints is error  err is:%v, TraceId is: %s", err, traceID)
				continue
			}
			expiredPoints := userPoint.Points - sumPoint.SumPoints
			if expiredPoints > 0 {
				logc.Infof(ctx, "CleanUserPoints data is, uid is:%d , getPointStartTimeStr is : %s,user Point is: %d ,incr point is: %d ,expiredPoints is: %d ", userPoint.Uid, getPointStartTimeStr, userPoint.Points, sumPoint.SumPoints, expiredPoints)
				tx := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.Begin()
				err = dao.NewWelfareMysqlTxDBUtil(tx).UpdateUserDecrPoints(ctx, userPoint.Uid, expiredPoints, time.Now().Unix())
				if err != nil {
					tx.Rollback()
					logc.Errorf(ctx, "CleanUserPoints DecrUserPoints is error  err is:%v, TraceId is: %s", err, traceID)
					continue
				}
				// 增加积分流水
				_, err = dao.NewWelfareMysqlTxDBUtil(tx).InsertPointsRecords(ctx, int(userPoint.Uid), consts.PointsRecordActionDecr, expiredPoints, "",
					consts.ExSourceTypePointsExpire, 0, 0, 0)
				if err != nil {
					tx.Rollback()
					logc.Errorf(ctx, "CleanUserPoints InsertPointsRecords is error  err is:%v, TraceId is: %s", err, traceID)
					continue
				}
				tx.Commit()
			}
		}
		//当查询出的数据条数小于100时，证明时最后一页
		if len(userPoints) < pageSize {
			logx.Infof("CleanUserPoints finish user num is：%d,", userNum)
			break
		}
	}

	return fmt.Sprintf("traceID: %s", traceID)
}

func PrintJSON(data interface{}) {
	b, _ := json.MarshalIndent(data, "", "  ")
	fmt.Println(string(b))
}
