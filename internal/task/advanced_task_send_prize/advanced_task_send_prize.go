package advanced_task_send_prize

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"bitbucket.org/gateio/gateio-lib-common-go/engine/auth"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/rebate"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service"
	prizeService "gateio_service_welfare_go/internal/service/prize_service"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"github.com/shopspring/decimal"
	"strconv"
	"strings"
	"time"
)

const (
	DepositType                           = "in"
	TradingType                           = "out"
	RuleInfoConditionsConditionDetailMark = "business_id"
)

func AdvancedTaskSendPrize(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {
	// 定时每小时执行一次,执行之间不能超过一小时,避免出现多个协程带来死锁和数据竞争问题
	ctx, cancel := context.WithTimeout(ctx, 50*time.Minute)
	defer cancel()
	logc.Infof(ctx, "执行Welfare任务 AdvancedTaskSendPrize : %d", param.JobID)

	PrintJSON(param)
	traceID := trace.TraceIDFromContext(ctx)
	logc.Info(ctx, fmt.Sprintf("traceID: %s", traceID))
	boolSwitch, err := service.GetSystemSwitchByKey(svcCtx, ctx, consts.WelfareConfigCouponSend)
	if err != nil {
		logc.Errorf(ctx, "AdvancedTaskSendPrize GetSystemSwitchByKey is failed;err is:%v,config_key is:%v ,traceId is:%v", err.Error(), consts.WelfareConfigCouponSend, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	if boolSwitch == false {
		logc.Warnf(ctx, "AdvancedTaskSendPrize GetSystemSwitchByKey boolSwitch is false, config_key is:%v ,traceId is:%v", consts.WelfareConfigCouponSend, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	boolSwitch, err = service.GetSystemSwitchByKey(svcCtx, ctx, consts.WelfareConfigTaskSend)
	if err != nil {
		logc.Errorf(ctx, "AdvancedTaskSendPrize GetSystemSwitchByKey is failed;err is:%v,config_key is:%v ,traceId is:%v", err.Error(), consts.WelfareConfigTaskSend, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	if boolSwitch == false {
		logc.Warnf(ctx, "AdvancedTaskSendPrize GetSystemSwitchByKey boolSwitch is false, config_key is:%v ,traceId is:%v", consts.WelfareConfigTaskSend, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	lastId := int64(0)
	pageSize := 500
	nowUnix := time.Now().Unix()
	userNum := 0
	for {
		// 3. 在循环开始时检查上下文
		select {
		case <-ctx.Done():
			logc.Infof(ctx, "AdvancedTaskSendPrize context cancelled, lastId: %d", lastId)
			return fmt.Sprintf("Task cancelled, traceID: %s", traceID)
		default:
		}
		userInfos, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).QueryUserRegisterList(svcCtx, ctx, lastId, nowUnix, pageSize)
		if err != nil {
			if ctx.Err() != nil {
				return fmt.Sprintf("AdvancedTaskSendPrize Context cancelled during database operation, traceID: %s", traceID)
			}
			logc.Errorf(ctx, "AdvancedTaskSendPrize QueryUserRegisterList is error ,Params is：%d, %d, err is:%v", lastId, nowUnix, err)
			break
		}
		for _, userInfo := range userInfos {
			lastId = userInfo.Id
			userNum++
			dataMap := make(map[string]interface{})
			dataMap["user_id"] = userInfo.Uid
			dataMap["ip"] = ""
			dataMap["const_id"] = ""
			dataMap["is_async"] = 1
			isRisk, errRisk := service.GetUserRiskData(svcCtx, ctx, int(userInfo.Uid), "", consts.RiskEventCodeIndexPageCheck, dataMap, nil)
			if errRisk != nil {
				logc.Errorf(ctx, "AdvancedTaskSendPrize uid is: %d , GetUserRiskData err is:%v", userInfo.Uid, errRisk)
				continue
			}
			//命中风控
			if isRisk {
				//最终将状态更新成已处理
				updateMap := map[string]interface{}{}
				updateMap["finish_advanced_receive"] = consts.FinishAdvancedReceiveYes
				//这里的ctx使用 context.Background()的原因是怕ctx超时造成状态更新失败
				err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateUserRegisterInfo(svcCtx, context.Background(), userInfo.Uid, updateMap)
				if err != nil {
					logc.Errorf(ctx, "AdvancedTaskSendPrize UpdateUserRegisterInfo is error ,Params is：%d, %v, err is:%v", userInfo.Uid, updateMap, err)
					return
				}
				logc.Infof(ctx, "AdvancedTaskSendPrize uid is: %d , user isRisk ", userInfo.Uid)
				continue
			}
			//验证市商,子账号,代理人等信息
			userCenter := service_client.NewUserCenterCall(ctx)
			users, err := userCenter.GetUserList([]int64{userInfo.Uid})
			if err != nil {
				logc.Errorf(ctx, "AdvancedTaskSendPrize userCenter.GetUserList is error ,uid is：%d, err is:%v", userInfo.Uid, err)
				continue
			}
			if len(users) == 0 {
				logc.Errorf(ctx, "AdvancedTaskSendPrize center users len is 0 ,uid is：%d", userInfo.Uid)
				continue
			}
			req := &rebate.GetRebateSystemUserDetailRequest{
				UserId: int(userInfo.Uid),
			}
			resp, err := rebate.NewClient().GetRebateSystemUserDetail(ctx, req)
			if err != nil {
				logc.Errorf(ctx, "AdvancedTaskSendPrize rebate.NewClient().GetRebateSystemUserDetail is error ,uid is：%d, err is:%v", userInfo.Uid, err)
				continue
			}
			if resp == nil || resp.UserId == 0 {
				logc.Errorf(ctx, "AdvancedTaskSendPrize GetRebateSystemUserDetail resp is failed ,uid is：%d", userInfo.Uid)
				continue
			}
			authUser := &auth.UserInfo{
				UID:        int(userInfo.Uid),
				Verified:   int(users[0].Verified),
				IsSub:      int(users[0].IsSub),
				AgencyType: resp.AgencyType,
			}
			boolCheck, _ := service.CheckUserIdentity(svcCtx, ctx, authUser)
			//验证未通过过滤直接更新状态
			if !boolCheck {
				//最终将状态更新成已处理
				updateMap := map[string]interface{}{}
				updateMap["finish_advanced_receive"] = consts.FinishAdvancedReceiveYes
				//这里的ctx使用 context.Background()的原因是怕ctx超时造成状态更新失败
				err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateUserRegisterInfo(svcCtx, context.Background(), userInfo.Uid, updateMap)
				if err != nil {
					logc.Errorf(ctx, "AdvancedTaskSendPrize UpdateUserRegisterInfo is error ,Params is：%d, %v, err is:%v", userInfo.Uid, updateMap, err)
					return
				}
				logc.Infof(ctx, "AdvancedTaskSendPrize CheckUserIdentity is false ,uid is：%d", userInfo.Uid)
				continue
			}

			// 5. 处理每个用户前检查上下文
			select {
			case <-ctx.Done():
				logc.Infof(ctx, "AdvancedTaskSendPrize context cancelled while processing user: %d", userInfo.Uid)
				return fmt.Sprintf("Task cancelled during user processing, traceID: %s", traceID)
			default:
			}
			//查询用户进阶任务完成情况并发奖
			userTasks, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).QueryUserTaskList(ctx, int(userInfo.Uid), []int{}, consts.ExSourceTypeAdvanced, consts.StatusDone)
			if err != nil {
				if ctx.Err() != nil {
					return fmt.Sprintf("AdvancedTaskSendPrize Context cancelled during task query, traceID: %s", traceID)
				}
				logc.Errorf(ctx, "AdvancedTaskSendPrize uid is: %d, QueryUserTaskList is error , err is:%v", userInfo.Uid, err)
				break
			}
			//用户待领将的进阶任务数量为0
			if len(userTasks) == 0 {
				//没有完成的任务,则把状态更新成完成处理
				updateMap := map[string]interface{}{}
				updateMap["finish_advanced_receive"] = consts.FinishAdvancedReceiveYes
				//这里的ctx使用 context.Background()的原因是怕ctx超时造成状态更新失败
				err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateUserRegisterInfo(svcCtx, context.Background(), userInfo.Uid, updateMap)
				if err != nil {
					logc.Errorf(ctx, "AdvancedTaskSendPrize UpdateUserRegisterInfo is error ,Params is：%d, %v, err is:%v", userInfo.Uid, updateMap, err)
					return
				}
				logc.Warnf(ctx, "AdvancedTaskSendPrize uid is: %d, userTasks len is 0", userInfo.Uid)
				continue
			}
			taskCenterIds := make([]int64, len(userTasks))
			for i, userTask := range userTasks {
				taskCenterIds[i] = userTask.TaskCenterId
			}
			//获取任务中心任务信息
			taskCenterMap, err := service.GetTaskListByTaskIds(svcCtx, ctx, int(userInfo.Uid), taskCenterIds)
			if err != nil {
				if ctx.Err() != nil {
					return fmt.Sprintf("AdvancedTaskSendPrize Context cancelled during task query, traceID: %s", traceID)
				}
				logc.Errorf(ctx, "AdvancedTaskSendPrize uid is: %d, GetTaskListByTaskIds is error ,Params is %v, err is:%v", userInfo.Uid, taskCenterIds, err)
				continue
			}
			//获取入金的 business_id
			businessIds := GetCenterTaskBusinessId(taskCenterMap, ctx)
			if len(businessIds) == 0 {
				logc.Warnf(ctx, "AdvancedTaskSendPrize uid is: %d, businessIds is error ,Params is %v", userInfo.Uid, businessIds)
			}
			//任务结束时间
			startTimeStr := time.Unix(userInfo.NewbieEndTime, 0).Format(time.DateTime)
			endTimeStr := time.Unix(userInfo.AdvancedEndTime, 0).Format(time.DateTime)
			//获取总入金
			depositNum, err := service.GetUserDepositTrading(ctx, userInfo.Uid, businessIds, startTimeStr, endTimeStr, DepositType)
			if err != nil {
				if ctx.Err() != nil {
					return fmt.Sprintf("AdvancedTaskSendPrize Context cancelled during task query, traceID: %s", traceID)
				}
				logc.Errorf(ctx, "AdvancedTaskSendPrize uid is: %d, GetUserDepositTrading is error ,Params is %s , %s, err is:%v", userInfo.Uid, startTimeStr, endTimeStr, err)
				continue
			}
			//获取出金
			tradingNum, err := service.GetUserDepositTrading(ctx, userInfo.Uid, []string{}, startTimeStr, endTimeStr, TradingType)
			if err != nil {
				if ctx.Err() != nil {
					return fmt.Sprintf("AdvancedTaskSendPrize Context cancelled during task query, traceID: %s", traceID)
				}
				logc.Errorf(ctx, "AdvancedTaskSendPrize uid is: %d, GetUserDepositTrading is error ,Params is %s , %s, err is:%v", userInfo.Uid, startTimeStr, endTimeStr, err)
				continue
			}
			//入金和出金的差值
			differenceValue := decimal.NewFromFloat(depositNum).Sub(decimal.NewFromFloat(tradingNum)).InexactFloat64()
			taskCenterInfo := &task.Task{}
			registerTask := &welfare_task_cfgs.WelfareTaskCfgs{}
			registerUserTask := &welfare_user_tasks.WelfareUserTasks{}
			logc.Infof(ctx, "AdvancedTaskSendPrize uid is: %d, depositNum is:%v ,tradingNum is:%v ,differenceValue is:%v", userInfo.Uid, depositNum, tradingNum, differenceValue)
			//入金量大于等于0发放最高奖励
			if differenceValue >= 0 {
				taskInfo := &welfare_task_cfgs.WelfareTaskCfgs{}
				_ = json.Unmarshal([]byte(userTasks[len(userTasks)-1].Memo), taskInfo)
				registerUserTask = userTasks[len(userTasks)-1]
				registerTask = taskInfo
				taskCenterInfo = taskCenterMap[userTasks[len(userTasks)-1].TaskCenterId]
			} else {
				//获取多规则任务用户的净入金和交易量
				mulRuleRecordScheduleReq := &task.RecordScheduleRequest{
					UserID:       int64(userInfo.Uid),
					TaskID:       userTasks[0].TaskCenterId,
					BusinessType: consts.WelfareTaskBusinessType,
					BusinessID:   strconv.FormatInt(userTasks[0].Id, 10),
				}
				mulRuleRecordScheduleResp, err := task.NewClient().MulRuleRecordSchedule(ctx, mulRuleRecordScheduleReq)
				if err != nil {
					//只打印不返回
					mulRuleRecordScheduleReqByte, _ := json.Marshal(mulRuleRecordScheduleReq)
					logc.Errorf(ctx, "AdvancedTaskSendPrize uid is: %d, GetUserDepositTrading is error ,Params is %s , err is:%v", userInfo.Uid, string(mulRuleRecordScheduleReqByte), err)
					continue
				}
				//用户净入金量
				userRechargeNum := float64(0)
				if mulRuleRecordScheduleResp != nil && mulRuleRecordScheduleResp.RuleList != nil {
					for _, ruleInfo := range mulRuleRecordScheduleResp.RuleList {
						if ruleInfo.Mark == consts.TaskCenterMarkAppRecharge {
							userRechargeNum = ruleInfo.Amount
						}
					}
				}
				//定义用户入金量
				lastUserRechargeNum := decimal.NewFromFloat(float64(userRechargeNum)).Add(decimal.NewFromFloat(differenceValue)).InexactFloat64()
				logc.Infof(ctx, "AdvancedTaskSendPrize uid is: %d, lastUserRechargeNum is %v ", userInfo.Uid, lastUserRechargeNum)
				for i := len(userTasks) - 1; i >= 0; i-- {
					userTask := userTasks[i]
					taskCenterInfo = taskCenterMap[userTask.TaskCenterId]
					taskDepositNum := int64(0)
					//从规则中获取奖品,任务入金,任务交易信息
					if len(taskCenterInfo.RuleInfo) < 2 {
						//只打印不报错
						logc.Warnf(ctx, "AdvancedTaskSendPrize uid is: %d, taskCenterInfo.RuleInfo len error ,taskCenterInfo is：%v, err is:%v", userInfo.Uid, taskCenterInfo, err)
						break
					} else {
						for _, ruleInfo := range taskCenterInfo.RuleInfo {
							if len(ruleInfo.Conditions) > 0 {
								//入金规则
								if ruleInfo.Mark == consts.TaskCenterMarkAppRecharge {
									taskDepositNum = ruleInfo.Conditions[0].Min
									if len(ruleInfo.Conditions[0].ConditionDetail) > 1 || taskDepositNum == 0 {
										for _, conditionDetail := range ruleInfo.Conditions[0].ConditionDetail {
											if utils.ContainsArray(consts.GetTaskCenterMinMarks(), conditionDetail.Mark) {
												taskDepositNum = conditionDetail.Min
											}
										}
									}
								}
							} else {
								//只打印不报错
								logc.Warnf(ctx, "AdvancedTaskSendPrize uid is: %d, taskCenterInfo.RuleInfo.Conditions len error ,taskCenterInfo is：%v, err is:%v", userInfo.Uid, taskCenterInfo, err)
								break
							}
						}
					}
					if taskDepositNum <= 0 {
						//只打印不报错
						logc.Warnf(ctx, "AdvancedTaskSendPrize uid is: %d, taskDepositNum is 0 ,taskCenterInfo is：%v", userInfo.Uid, taskCenterInfo)
						break
					}
					logc.Infof(ctx, "AdvancedTaskSendPrize uid is: %d, lastUserRechargeNum is %v ,taskDepositNum is:%d ,task_center_id is:%d", userInfo.Uid, lastUserRechargeNum, taskDepositNum, taskCenterInfo.TaskID)
					if float64(taskDepositNum) <= lastUserRechargeNum {
						taskInfo := &welfare_task_cfgs.WelfareTaskCfgs{}
						_ = json.Unmarshal([]byte(userTask.Memo), taskInfo)
						registerUserTask = userTask
						registerTask = taskInfo
						break
					}
				}
			}
			//获取到需要发奖的档位后先验证上下文是否关闭,如果关闭则停止操作
			select {
			case <-ctx.Done():
				logc.Infof(ctx, "AdvancedTaskSendPrize ctx time out: %d, taskCenterId is：%v", userInfo.Uid, taskCenterInfo)
				return
			default:

			}
			logc.Infof(ctx, "AdvancedTaskSendPrize uid is: %d, registerTask.Id is:%v ,registerUserTask.Id is:%v, taskCenterInfo.TaskID is:%v", userInfo.Uid, registerTask.Id, registerUserTask.Id, taskCenterInfo.TaskID)
			if registerTask.Id > 0 && registerUserTask.Id > 0 && taskCenterInfo.TaskID > 0 {
				logc.Infof(ctx, "AdvancedTaskSendPrize finish uid is: %d , taskCenterId is: %d,registerTask id is: %d,registerUserTask id is:%d ,depositNum is: %v ,tradingNum is: %v, differenceValue is: %v", userInfo.Uid, taskCenterInfo.TaskID, registerTask.Id, registerUserTask.Id, depositNum, tradingNum, differenceValue)
				//调用新客奖励发放,发放注册任务奖励
				errCode, err := prizeService.NewPrizeNewbieService(ctx, svcCtx).ReceivePrize(registerTask, registerUserTask, taskCenterInfo, "")
				if err != nil {
					//只打印不返回
					logc.Errorf(ctx, "AdvancedTaskSendPrize ReceivePrize is err,err is：%v", err)
				}
				if errCode != 0 {
					//只打印不返回
					logc.Errorf(ctx, "AdvancedTaskSendPrize ReceivePrize errCode != 0,errCode is：%d", errCode)
				}
			} else {
				logc.Warnf(ctx, "AdvancedTaskSendPrize uid is: %d, registerTask,registerUserTask,taskCenterInfo not found", userInfo.Uid)
			}
			//最终将状态更新成已处理
			updateMap := map[string]interface{}{}
			updateMap["finish_advanced_receive"] = consts.FinishAdvancedReceiveYes
			//这里的ctx使用 context.Background()的原因是怕ctx超时造成状态更新失败
			err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateUserRegisterInfo(svcCtx, context.Background(), userInfo.Uid, updateMap)
			if err != nil {
				logc.Errorf(ctx, "AdvancedTaskSendPrize UpdateUserRegisterInfo is error ,Params is：%d, %v, err is:%v", userInfo.Uid, updateMap, err)
				return
			}
		}
		//当查询出的数据条数小于100时,证明时最后一页
		if len(userInfos) < pageSize {
			logc.Infof(ctx, "AdvancedTaskSendPrize finish user num is：%d,", userNum)
			break
		}
	}

	return fmt.Sprintf("traceID: %s", traceID)
}

func PrintJSON(data interface{}) {
	b, _ := json.MarshalIndent(data, "", "  ")
	fmt.Println(string(b))
}

func GetCenterTaskBusinessId(taskCenterMap map[int64]*task.Task, ctx context.Context) []string {
	businessIdList := make([]string, 0)
	for _, taskCenter := range taskCenterMap {
		if taskCenter == nil {
			continue
		}
		for _, ruleInfo := range taskCenter.RuleInfo {
			if len(ruleInfo.Conditions) > 0 {
				//入金规则
				if ruleInfo.Mark == consts.TaskCenterMarkAppRecharge {
					if len(ruleInfo.Conditions[0].ConditionDetail) > 0 {
						for _, condition := range ruleInfo.Conditions[0].ConditionDetail {
							if condition.Mark == RuleInfoConditionsConditionDetailMark {
								businessIds := strings.Split(condition.BusinessID, ",")
								for _, businessId := range businessIds {
									if businessId != "" {
										businessIdList = append(businessIdList, businessId)
									}
								}
							}
						}

					}
				}
			}
		}
	}
	businessIdList = utils.DeduplicateMap(businessIdList)
	return businessIdList
}
