package config_set

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"log"
	"time"
)

type ConfigParms struct {
	Name   string `json:"name"`
	Config string `json:"config"`
}

func ConfigSet(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {
	logData("ConfigSet start", "ConfigSet-")

	printJSON(param)
	traceID := trace.TraceIDFromContext(ctx)

	var configParas *ConfigParms
	err := json.Unmarshal([]byte(param.ExecutorParams), &configParas)
	if err != nil || configParas == nil {
		logc.Errorf(ctx, "[configSet] json.Unmarshal err: %v", err)
		return err.Error()
	}

	_, err = service.SetWelfareConfig(svcCtx, ctx, configParas.Name, configParas.Config)
	if err != nil {
		logc.Errorf(ctx, "[configSet] set config fail err: %v", err)
		return
	}

	logc.Info(ctx, fmt.Sprintf("traceID: %s", traceID))
	logc.Info(ctx, fmt.Sprintf("set config succ, name: %s, value: %s", configParas.Name, configParas.Config))
	logData("ConfigSet end", "ConfigSet-")
	return fmt.Sprintf("traceID: %s", traceID)

}

func logData(msg, prefix string) {
	logMsg := fmt.Sprintf("%s%s%s", time.Now().Format("2006-01-02 15:04:05"), prefix, msg)
	log.Println(logMsg)
}

func printJSON(data interface{}) {
	b, _ := json.MarshalIndent(data, "", "  ")
	fmt.Println(string(b))
}
