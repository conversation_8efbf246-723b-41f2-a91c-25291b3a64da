package shop

import (
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_points_shop"
	"gateio_service_welfare_go/internal/models/welfare_points_shops_limit_records"
	"gateio_service_welfare_go/internal/svc"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
)

// PatchShop 商店库存补充定时任务
func PatchShop(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {
	logc.Infof(ctx, "开始执行商店库存补充任务: %d", param.JobID)

	traceID := trace.TraceIDFromContext(ctx)
	startTime := time.Now()

	// 打印任务参数
	printJSON(param)

	// 执行库存补充逻辑
	if err := handleShopInventoryPatch(ctx, svcCtx); err != nil {
		logc.Errorf(ctx, "商店库存补充任务执行失败: %v", err)
		return fmt.Sprintf("任务失败 - traceID: %s, 耗时: %v, 错误: %v",
			traceID, time.Since(startTime), err)
	}

	logc.Infof(ctx, "商店库存补充任务执行成功，耗时: %v", time.Since(startTime))
	return fmt.Sprintf("任务成功 - traceID: %s, 耗时: %v", traceID, time.Since(startTime))
}

// handleShopInventoryPatch 处理商店库存补充核心逻辑
func handleShopInventoryPatch(ctx context.Context, svcCtx *svc.ServiceContext) error {
	logc.Info(ctx, "开始处理商店库存补充")

	dbUtil := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB)

	// 获取所有奖品配置
	pointsShop, err := dbUtil.GetAllPrizeList(ctx)
	if err != nil {
		logc.Errorf(ctx, "获取奖品列表失败: %v", err)
		return fmt.Errorf("获取奖品列表失败: %w", err)
	}

	if len(pointsShop) == 0 {
		logc.Info(ctx, "奖品列表为空，无需处理")
		return nil
	}

	// 构建奖品配置映射
	prizeConfigMap := buildPrizeConfigMap(ctx, pointsShop)
	logc.Infof(ctx, "获取到 %d 个奖品配置", len(prizeConfigMap))

	// 获取当前库存状态
	currentLimitMap, err := getCurrentLimitMap(ctx, dbUtil)
	if err != nil {
		return fmt.Errorf("获取当前库存状态失败: %w", err)
	}

	// 处理库存补充
	return processInventoryPatch(ctx, svcCtx, dbUtil, prizeConfigMap, currentLimitMap)
}

// buildPrizeConfigMap 构建奖品配置映射
func buildPrizeConfigMap(ctx context.Context, pointsShop []*welfare_points_shop.WelfarePointsShop) map[string]int64 {
	prizeConfigMap := make(map[string]int64, len(pointsShop))
	for _, shop := range pointsShop {
		// 直接访问结构体字段，无需反射
		key := fmt.Sprintf("%d_%d_%s", shop.Id, shop.PrizeSubType, shop.PrizeValue)
		prizeConfigMap[key] = shop.PrizeMaxNum
	}
	logc.Infof(ctx, "获取到库存总数记录 %v ", prizeConfigMap)
	return prizeConfigMap
}

// getCurrentLimitMap 获取当前库存映射
func getCurrentLimitMap(ctx context.Context, dbUtil *dao.WelfareMysqlDBUtil) (map[string]int64, error) {
	currentLimitAll, err := dbUtil.GetCurrentLimitAll(ctx)
	if err != nil {
		logc.Errorf(ctx, "获取当前库存限制失败: %v", err)
		return nil, err
	}

	currentLimitMap := make(map[string]int64)
	for _, v := range currentLimitAll {
		key := fmt.Sprintf("%d_%d_%d", v.PrizeId, v.Type, v.TypeNum)
		currentLimitMap[key] = v.Num
	}

	logc.Infof(ctx, "获取到个当前库存记录 %v ", currentLimitMap)
	logc.Infof(ctx, "获取到 %d 个当前库存记录", len(currentLimitMap))
	return currentLimitMap, nil
}

// processInventoryPatch 处理库存补充
func processInventoryPatch(ctx context.Context, svcCtx *svc.ServiceContext, dbUtil *dao.WelfareMysqlDBUtil,
	prizeConfigMap, currentLimitMap map[string]int64) error {

	var successCount, failCount int

	for key, maxLimit := range prizeConfigMap {
		currentLimit := currentLimitMap[key]
		diff := maxLimit - currentLimit

		logc.Infof(ctx, "处理奖品 %s | 库存上限: %d | 当前剩余: %d | 需要补充: %d",
			key, maxLimit, currentLimit, diff)

		if diff == 0 {
			continue // 无需补充
		}

		if err := patchSingleItem(ctx, svcCtx, dbUtil, key, diff); err != nil {
			logc.Errorf(ctx, "补充奖品 %s 失败: %v", key, err)
			failCount++
		} else {
			successCount++
		}
	}

	logc.Infof(ctx, "库存补充完成 - 成功: %d, 失败: %d", successCount, failCount)
	return nil
}

// patchSingleItem 补充单个奖品库存
func patchSingleItem(ctx context.Context, svcCtx *svc.ServiceContext, dbUtil *dao.WelfareMysqlDBUtil,
	key string, diff int64) error {

	// 解析key获取奖品信息
	prizeId, typeValue, typeNum, err := parseItemKey(key)
	if err != nil {
		return fmt.Errorf("解析奖品key失败: %w", err)
	}

	// 增加库存
	if err := dbUtil.IncrCurrentLimitByPrizeId(ctx, int(prizeId), int(typeValue), int(typeNum), int(diff)); err != nil {
		return fmt.Errorf("增加库存失败: %w", err)
	}

	logc.Infof(ctx, "成功增加库存 - 奖品ID: %d, 类型: %d, 数量: %d, 补充: %d",
		prizeId, typeValue, typeNum, diff)

	// 记录操作日志
	if err := insertLimitRecord(ctx, svcCtx, prizeId, typeValue, typeNum, diff); err != nil {
		logc.Errorf(ctx, "插入限制记录失败: %v", err)
		// 记录失败不影响主流程
	}

	// 清理缓存
	dbUtil.DelRedisCurrentLimit(svcCtx, ctx, int(prizeId), int(typeValue), int(typeNum))

	return nil
}

// parseItemKey 解析奖品key
func parseItemKey(key string) (prizeId, typeValue, typeNum int64, err error) {
	parts := strings.Split(key, "_")
	if len(parts) != 3 {
		return 0, 0, 0, fmt.Errorf("无效的key格式: %s", key)
	}

	prizeId, err = strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return 0, 0, 0, fmt.Errorf("解析prizeId失败: %w", err)
	}

	typeValue, err = strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return 0, 0, 0, fmt.Errorf("解析typeValue失败: %w", err)
	}

	typeNum, err = strconv.ParseInt(parts[2], 10, 64)
	if err != nil {
		return 0, 0, 0, fmt.Errorf("解析typeNum失败: %w", err)
	}

	return prizeId, typeValue, typeNum, nil
}

// insertLimitRecord 插入限制记录
func insertLimitRecord(ctx context.Context, svcCtx *svc.ServiceContext,
	prizeId, typeValue, typeNum, diff int64) error {

	record := &welfare_points_shops_limit_records.WelfarePointsShopsLimitRecords{
		PrizeId: prizeId,
		Type:    typeValue,
		TypeNum: typeNum,
		Num:     diff,
	}

	model := welfare_points_shops_limit_records.NewWelfarePointsShopsLimitRecordsModel(svcCtx.WelfareDB)
	if err := model.Insert(ctx, record); err != nil {
		return fmt.Errorf("插入限制记录失败: %w", err)
	}

	return nil
}

// printJSON 打印JSON格式的数据（用于调试）
func printJSON(data interface{}) {
	if b, err := json.MarshalIndent(data, "", "  "); err == nil {
		fmt.Println(string(b))
	}
}
