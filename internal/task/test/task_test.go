package test

import (
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"gateio_service_welfare_go/internal/task/shop"
	"testing"
)

func Test_PatchShop(t *testing.T) {
	ctx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	defer closeTestContext(ctx)
	shop.PatchShop(context.Background(), ctx, &xxljob.TaskRequest{
		ExecutorParams: "2025-05-25 00:00:00",
	})
	t.Log("finish")
}
