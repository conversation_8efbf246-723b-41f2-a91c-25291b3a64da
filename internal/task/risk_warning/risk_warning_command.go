package risk_warning

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service/prize_service"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"log"
	"time"
)

// 假设这些是常量定义
const (
	PointsMax         = 10000
	StatusSuccess     = "SUCCESS"
	StatusInit        = "INIT"
	StatusFailed      = "FAILED"
	TableNameNumStart = 0
	TableNameNumEnd   = 10
	RiskWarningPrefix = " riskWarning-"
)

// 每十分钟执行一次
func RiskWarningCommand(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {

	PrintJSON(param)
	logData("RiskWarningCommand start", RiskWarningPrefix)
	traceID := trace.TraceIDFromContext(ctx)
	logc.Info(ctx, fmt.Sprintf("traceID: %s", traceID))
	handlePointsMaxData(ctx, svcCtx, traceID)
	handleFailedRecordRows(ctx, svcCtx, traceID)
	//任务领奖异常有指定脚本补单 task_receive_coupon_warning.go
	//handleNewbieFailedPrizeRecordRows(ctx, svcCtx, traceID)

	logData("RiskWarningCommand end", RiskWarningPrefix)
	return fmt.Sprintf("traceID: %s", traceID)
}

// 获取十分钟之前的时间
func GetTimeStr(val time.Duration) string {
	return time.Now().Add(val * time.Minute).Format(time.DateTime)
}

// handlePointsMaxData 处理超过 1000 积分兑换监控预警
func handlePointsMaxData(ctx context.Context, svcCtx *svc.ServiceContext, traceID string) {
	recordList := []*dao.WelfareExchangeRecord{}
	for i := TableNameNumStart; i < TableNameNumEnd; i++ {
		list := []*dao.WelfareExchangeRecord{}
		tableName := dao.GetWelfareExchangeRecordTableName(i)
		// 构建查询
		err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.WithContext(ctx).Table(tableName).
			Where("points >= ?", PointsMax).
			Where("status = ?", StatusSuccess).
			Where("prize_source = ?", consts.RecordPrizeSourceExchange).
			Where("updated_at >= ?", GetTimeStr(-10)).
			Find(&list).Error
		if err != nil {
			logc.Errorf(ctx, "RiskWarningCommand handlePointsMaxData query WelfareExchangeRecord is error :%v, TraceID is: %s", err, traceID)
			return
		}
		recordList = append(recordList, list...)
	}

	if len(recordList) == 0 {
		return
	}

	warningMsg := fmt.Sprintf("【新福利中心】大额积分奖品兑换 \n 当前时段超过%d积分的奖品兑换成功的数量为%d", PointsMax, len(recordList))
	for _, v := range recordList {
		warningMsg += fmt.Sprintf("\n id:%v，uid:%v，prize_id:%v，points:%v，status:%v",
			v.ID, v.UID, v.PrizeID, v.Points, v.Status)
	}
	SendWarningMsg(ctx, warningMsg, "online", traceID)
}

// handleFailedRecordRows 处理福利兑换异常订单监控
func handleFailedRecordRows(ctx context.Context, svcCtx *svc.ServiceContext, traceID string) {
	recordList := []*dao.WelfareExchangeRecord{}
	for i := TableNameNumStart; i < TableNameNumEnd; i++ {
		list := []*dao.WelfareExchangeRecord{}
		tableName := dao.GetWelfareExchangeRecordTableName(i)
		// 构建查询
		err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.WithContext(ctx).Table(tableName).
			Where("status in (?)", []string{StatusInit, StatusFailed}).
			Where("prize_source = ?", consts.RecordPrizeSourceExchange).
			Where("updated_at >= ?", GetTimeStr(-20)).
			Find(&list).Error
		if err != nil {
			logc.Errorf(ctx, "RiskWarningCommand handleFailedRecordRows query WelfareExchangeRecord err is:%v, TraceID is: %s", err, traceID)
			return
		}
		recordList = append(recordList, list...)
	}

	if len(recordList) == 0 {
		return
	}

	warningMsg := "【新福利中心】积分兑换异常订单&自动补单结果"
	isSendMsg := false
	for _, v := range recordList {
		// 获取分表名
		tableName := dao.GetSubTableName("welfare_user_prize_detail", v.CreatedAt)
		// 构建查询
		prizeDetail := &dao.WelfareUserPrizeDetail{}
		err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.WithContext(ctx).Table(tableName).
			Where("uid = ?", v.UID).
			Where("user_prize_id = ?", v.ID).
			First(prizeDetail).Error
		if err != nil {
			logc.Errorf(ctx, "RiskWarningCommand handleFailedRecordRows get WelfareUserPrizeDetail err is:%v, TraceID is: %s", err, traceID)
			continue
		}

		recordData, _ := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetRecordByUidAndId(ctx, v.UID, v.ID)
		if v.Status == consts.RecordStatusInit && recordData.Status == consts.RecordStatusSuccess {
			logc.Infof(ctx, "RiskWarningCommand handleFailedRecordRows is succ, uid:%d, id:%d", v.UID, v.ID)
			continue
		}
		if v.Status == consts.RecordStatusInit && time.Now().Unix()-recordData.UpdatedAt.Unix() < 600 {
			logc.Warnf(ctx, "RiskWarningCommand handleFailedRecordRows is INIT, but time < 600s wating next job, uid:%d, id:%d", v.UID, v.ID)
			continue
		}

		detailVal := map[string]interface{}{}
		_ = json.Unmarshal([]byte(prizeDetail.Detail), &detailVal)
		//兑换成功发奖成功
		if (detailVal["sendPrizeStatus"] != nil && detailVal["sendPrizeStatus"] == "SUCCESS") && (detailVal["insertPointsRecordsStatus"] != nil && detailVal["insertPointsRecordsStatus"] != "SUCCESS") {
			//发送成功，但是新增流水失败
			isSendMsg = true
			warningMsg += fmt.Sprintf("\n [新增积分流水失败]exchange_records_id:%v，detail_id:%v，uid:%v，prize_id:%v，points:%v，status:%v", v.ID, prizeDetail.ID, v.UID, v.PrizeID, v.Points, v.Status)
		} else if (detailVal["sendPrizeStatus"] != nil && detailVal["sendPrizeStatus"] == "FAILED") && (detailVal["returnIncrPointsStatus"] != nil && detailVal["returnIncrPointsStatus"] == "FAILED") {
			//卡券发送失败，但是退积分失败
			isSendMsg = true
			warningMsg += fmt.Sprintf("\n [退积分失败]exchange_records_id:%v，detail_id:%v，uid:%v，prize_id:%v，points:%v，status:%v", v.ID, prizeDetail.ID, v.UID, v.PrizeID, v.Points, v.Status)
		} else if (detailVal["sendPrizeStatus"] != nil && detailVal["sendPrizeStatus"] != "SUCCESS") && (detailVal["returnIncrPointsStatus"] != nil && detailVal["returnIncrPointsStatus"] == "INIT") {
			//卡券超时，需要拉取状态补单
			isSendMsg = true
			handleResult := ""
			couponResult := ""
			//获取source
			prizeInfo, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetPrizeInfoByOneId(ctx, v.PrizeID)
			if err != nil {
				handleResult = fmt.Sprintf("GetPrizeInfoById failed:%v", err)
			} else {
				//查询卡券的结果
				resp, err := service_client.NewCouponCenterCall(ctx).CheckSendCouponProgress(prizeInfo.Source, fmt.Sprintf("%d_%d", v.UID, v.ID))
				if err != nil {
					//message:RecordsNotFound 直接失败，退积分
					if resp.Code == 10002 {
						couponResult = "SEND_FAILED"
						err := handleSendPrizeFailed(svcCtx, ctx, v, int(prizeDetail.ID))
						handleResult = consts.RecordStatusSuccess
						if err != nil {
							handleResult = err.Error()
						}
					} else {
						logc.Errorf(ctx, "RiskWarningCommand handleFailedRecordRows CheckSendCouponProgress is failed，err is：%v, uid is:%d, traceId:%s", err, v.UID, traceID)
						handleResult = fmt.Sprintf("CheckSendCouponProgress failed:%v", err)
					}

				} else {
					couponResult = resp.SendStatus
					switch resp.SendStatus {
					case "SEND_SUCCESS":
						err := handleSendPrizeSuccess(svcCtx, ctx, v, int(prizeDetail.ID))
						handleResult = consts.RecordStatusSuccess
						if err != nil {
							handleResult = err.Error()
						}
					case "SEND_FAILED":
						err := handleSendPrizeFailed(svcCtx, ctx, v, int(prizeDetail.ID))
						handleResult = consts.RecordStatusSuccess
						if err != nil {
							handleResult = err.Error()
						}
					}
				}
			}
			warningMsg += fmt.Sprintf("\n [卡券超时，自动补单]exchange_records_id:%v，detail_id:%v，uid:%v，prize_id:%v，points:%v，status:%v, couponResult:%s, handleResult:%s", v.ID, prizeDetail.ID, v.UID, v.PrizeID, v.Points, v.Status, couponResult, handleResult)
		}
	}
	if isSendMsg {
		SendWarningMsg(ctx, warningMsg, "online", traceID)
	}
}

func handleSendPrizeSuccess(svc *svc.ServiceContext, ctx context.Context, record *dao.WelfareExchangeRecord, recordDetailId int) error {

	detail := map[string]string{
		"decrPointsStatus":          "SUCCESS",
		"sendPrizeStatus":           "SUCCESS",
		"returnIncrPointsStatus":    "INIT",
		"insertPointsRecordsStatus": "SUCCESS",
		"decrCurrentLimitStatus":    "SUCCESS",
	}

	updateTx := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).DB.Begin()
	defer updateTx.Rollback()
	updateExchangeBool := prize_service.NewPrizeService(ctx, svc).UpdateExchangeStatusById(updateTx, int(record.UID), int(record.ID), recordDetailId, consts.RecordStatusSuccess, detail, record.PrizeID, consts.ExchangePrize, 0, "")
	if !updateExchangeBool {
		return errors.New("UpdateExchangeStatus failed")
	}

	prizeInfo, err := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).GetPrizeInfoByOneId(ctx, record.PrizeID)
	if err != nil {
		return err
	}

	if prizeInfo.Id <= 0 {
		return errors.New("GetPrizeInfoById is empty")
	}
	prizeInfo.PrizeDesc = ""

	//增加积分流水
	boolInsertPointsRecords, err := dao.NewWelfareMysqlTxDBUtil(updateTx).InsertPointsRecords(ctx, int(record.UID), consts.PointsRecordActionDecr, record.Points, prizeInfo, consts.UserTaskTypePointsExchange, int(record.TaskID), 0, int(record.ID))
	if err != nil {
		return err
	}

	if boolInsertPointsRecords <= 0 {
		return errors.New("InsertPointsRecords is failed")
	}

	updateTx.Commit()

	return nil
}

func handleSendPrizeFailed(svc *svc.ServiceContext, ctx context.Context, record *dao.WelfareExchangeRecord, recordDetailId int) error {

	detail := map[string]string{
		"decrPointsStatus":          "SUCCESS",
		"sendPrizeStatus":           "FAILED",
		"returnIncrPointsStatus":    "SUCCESS",
		"insertPointsRecordsStatus": "INIT",
		"decrCurrentLimitStatus":    "INIT",
	}

	updateTx := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).DB.Begin()
	defer updateTx.Rollback()
	updateExchangeBool := prize_service.NewPrizeService(ctx, svc).UpdateExchangeStatusById(updateTx, int(record.UID), int(record.ID), recordDetailId, consts.RecordStatusFailed, detail, record.PrizeID, consts.ExchangePrize, 0, "")
	if !updateExchangeBool {
		return errors.New("UpdateExchangeStatus failed")
	}

	boolIncrUserPoints, err := dao.NewWelfareMysqlTxDBUtil(updateTx).IncrUserPoints(ctx, int(record.UID), record.Points)
	if err != nil {
		return err
	}

	if boolIncrUserPoints <= 0 {
		return errors.New("returnUserPoints is failed")
	}

	updateTx.Commit()

	return nil
}

// handleNewbieFailedPrizeRecordRows 处理新人任务奖品异常订单监控
func handleNewbieFailedPrizeRecordRows(ctx context.Context, svcCtx *svc.ServiceContext, traceID string) {
	recordList := []*dao.WelfareExchangeRecord{}
	for i := TableNameNumStart; i < TableNameNumEnd; i++ {
		list := []*dao.WelfareExchangeRecord{}
		tableName := dao.GetWelfareExchangeRecordTableName(i)
		// 构建查询
		err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.WithContext(ctx).Table(tableName).
			Where("status in (?)", []string{StatusInit, StatusFailed}).
			Where("prize_source in (?)", []string{consts.RecordPrizeSourceNewbieTaskNew, consts.RecordPrizeSourceLimitedTimeTasks, consts.RecordPrizeSourceLimitedTask, consts.RecordPrizeSourceDailyTasks}).
			Where("updated_at >= ?", GetTimeStr(-10)).
			Find(&list).Error
		if err != nil {
			logc.Errorf(ctx, "RiskWarningCommand handleNewbieFailedPrizeRecordRows query WelfareExchangeRecord err is:%v, TraceID is: %s", err, traceID)
			return
		}
		recordList = append(recordList, list...)
	}

	if len(recordList) == 0 {
		return
	}

	warningMsg := "【新福利中心】任务奖品发放异常订单 \n 当前时段异常订单如下："
	isSendMsg := false
	for _, v := range recordList {
		if v.TaskID > 0 {
			userTask := &welfare_user_tasks.WelfareUserTasks{}
			err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.WithContext(ctx).
				Where("uid = ?", v.UID).Where("task_id = ?", v.TaskID).
				First(userTask).Error
			if err != nil {
				logc.Errorf(ctx, "RiskWarningCommand handleNewbieFailedPrizeRecordRows get userTask err is:%v, TraceID is: %s,%d,%d", err, traceID, v.UID, v.TaskID)
				return
			}
			if userTask.Type == consts.UserTaskTaskTypeRegister {
				//过滤注册任务
				continue
			}
		}
		if v.Status == dao.StatusInit {
			isSendMsg = true
			warningMsg += fmt.Sprintf("\n id:%v，uid:%v，prize_id:%v，user_task_id:%v，status:%v",
				v.ID, v.UID, v.PrizeID, v.TaskID, v.Status)
		} else if v.Status == dao.StatusFailed {
			// 获取分表名
			tableName := dao.GetSubTableName("welfare_user_prize_detail", v.CreatedAt)
			// 构建查询
			prizeDetail := &dao.WelfareUserPrizeDetail{}
			err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.WithContext(ctx).Table(tableName).
				Where("uid = ?", v.UID).
				Where("user_prize_id = ?", v.ID).
				First(prizeDetail).Error
			if err != nil {
				logc.Errorf(ctx, "RiskWarningCommand handleNewbieFailedPrizeRecordRows get WelfareUserPrizeDetail err is:%v, TraceID is: %s", err, traceID)
				continue
			}

			detailVal := map[string]interface{}{}
			if err := json.Unmarshal([]byte(prizeDetail.Detail), &detailVal); err != nil {
				logc.Warnf(ctx, "RiskWarningCommand handleNewbieFailedPrizeRecordRows unmarshal prizeDetail.Detail err is:%v, TraceID is: %s", err, traceID)
				continue
			}

			// 卡券发放异常
			if sendCouponStatus, ok := detailVal["sendCouponStatus"].(string); ok && sendCouponStatus == "FAILED" {
				isSendMsg = true
				warningMsg += fmt.Sprintf("\n detail_id:%v，uid:%v，user_exchange_id:%v，user_task_id:%v， sendCouponStatus:FAILED",
					prizeDetail.ID, prizeDetail.UID, prizeDetail.UserPrizeID, v.TaskID)
			}
		}
	}

	if isSendMsg {
		SendWarningMsg(ctx, warningMsg, "online", traceID)
	}
}

// log 记录日志
func logData(msg, prefix string) {
	logMsg := fmt.Sprintf("%s%s%s", time.Now().Format("2006-01-02 15:04:05"), prefix, msg)
	log.Println(logMsg)
}

func PrintJSON(data interface{}) {
	b, _ := json.MarshalIndent(data, "", "  ")
	fmt.Println(string(b))
}
