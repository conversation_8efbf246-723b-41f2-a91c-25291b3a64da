package risk_warning

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_check_in_logs"
	"gateio_service_welfare_go/internal/models/welfare_points_records"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"math"
	"time"
)

// 积分相关常量
const (
	DiffPoints            = 100
	PointsAllMax          = 10000
	RiskWarningHourPrefix = " riskWarningHour-"
)

type CheckInRecords struct {
	SumPoints int64 `gorm:"column:sum_points"`
	Count     int64 `gorm:"column:count"`
}

// 每小时执行一次
func RiskWarningHourCommand(ctx context.Context, svc *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {
	PrintJSON(param)
	logData("RiskWarningHourCommand start", RiskWarningHourPrefix)
	traceID := trace.TraceIDFromContext(ctx)
	logc.Info(ctx, fmt.Sprintf("traceID: %s", traceID))

	handleSendPoints(ctx, svc, traceID)
	handleExchangeRecord(ctx, svc, traceID)
	logData("RiskWarningHourCommand end", RiskWarningHourPrefix)
	return fmt.Sprintf("traceID: %s", traceID)
}

// handleSendPoints 每日发放积分、用户完成任务得的积分监控预警
func handleSendPoints(ctx context.Context, svc *svc.ServiceContext, traceID string) {
	// 获取积分记录总和
	var sumPoints int64
	arrSumPoints := make(map[int]int64)

	// 查询积分记录
	var pointsRecords []struct {
		Type      int   `gorm:"column:type"`
		SumPoints int64 `gorm:"column:sum_points"`
	}

	err := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).DB.Model(&welfare_points_records.WelfarePointsRecords{}).
		Select("type, SUM(points) as sum_points").
		Where("action = ? AND updated_at >= ?", dao.ActionIncr, GetTimeStr(-60)).
		Group("type").
		Find(&pointsRecords).Error

	if err != nil {
		logc.Errorf(ctx, "RiskWarningHourCommand handleSendPoints query pointsRecords is error  err is:%v, TraceId is: %s", err, traceID)
		return
	}

	for _, record := range pointsRecords {
		arrSumPoints[record.Type] = record.SumPoints
		sumPoints += record.SumPoints
	}
	//只保留任务获取积分和签到获取积分的记录
	// 获取用户任务积分
	var allUserPoint int64
	arrUserTaskRows := make(map[int]map[string]interface{})

	// 查询用户任务积分
	var taskRecords []struct {
		Type      int   `gorm:"column:type"`
		SumPoints int64 `gorm:"column:sum_points"`
		TaskCount int64 `gorm:"column:task_count"`
	}

	err = dao.NewWelfareMysqlDBUtil(svc.WelfareDB).DB.Model(&welfare_user_tasks.WelfareUserTasks{}).
		Select("type, SUM(points) as sum_points, COUNT(id) as task_count").
		Where("type IN ? AND status = ? AND updated_at >= ?",
			[]int{
				consts.UserTaskTypeNewbie,
				consts.UserTaskTypeWelfare,
				consts.UserTaskTypeInvite,
				consts.UserTaskTypeNewbieNew,
				consts.UserTaskTypeCheckIn,
				consts.UserTaskTypeTgCheckIn,
				consts.UserTaskTypeTimeLimit,
				consts.UserTaskTypeTgGame,
				consts.UserTaskTypeTgInvite,
				consts.UserTaskTypeNewbieRegister,
				consts.UserTaskTypeNewbieGuide,
				consts.UserTaskTypeNewbieAdvanced,
				consts.UserTaskTypeVeteranDaily,
				consts.UserTaskTypeVeteranLimited,
			},
			consts.StatusSettlement,
			GetTimeStr(-60)).
		Group("type").
		Find(&taskRecords).Error

	if err != nil {
		logc.Errorf(ctx, "RiskWarningHourCommand handleSendPoints query taskRecords is error  err is:%v, TraceId is: %s", err, traceID)
		return
	}
	for _, record := range taskRecords {
		arrUserTaskRows[record.Type] = map[string]interface{}{
			"sum_points": record.SumPoints,
			"task_count": record.TaskCount,
		}
		allUserPoint += record.SumPoints
	}

	//获取签到总积分记录
	// 查询用户任务积分
	checkInRecord := &CheckInRecords{}
	err = dao.NewWelfareMysqlDBUtil(svc.WelfareDB).DB.Model(&welfare_check_in_logs.WelfareCheckInLogs{}).
		Select("SUM(prize_type_num) as sum_points", "count(id) count").
		Where("prize_type = ? AND status = ? AND updated_at >= ?",
			consts.CheckInPoints,
			consts.CheckInStatusSuccess,
			GetTimeStr(-60)).
		First(&checkInRecord).Error

	if err != nil {
		logc.Errorf(ctx, "RiskWarningHourCommand handleSendPoints query CheckInRecords is error  err is:%v, TraceId is: %s", err, traceID)
		return
	}
	checkInTaskPoints := checkInRecord.SumPoints
	checkInTaskRows := checkInRecord.Count
	allUserPoint += checkInRecord.SumPoints
	arrUserTaskRows[consts.UserTaskTypeCheckIn] = map[string]interface{}{
		"sum_points": checkInTaskPoints,
		"task_count": checkInTaskRows,
	}
	//注册任务，入门任务，进阶任务，每日任务，限时任务获取的积分
	//邀请领取积分记录
	//签到任务-奖品类型：99
	//tg签到任务-奖品类型：99
	//活动任务积分
	//游戏中心-邀请赠积分

	// 构建告警信息
	warningMsg := "【新福利中心】发放积分&完成任务对账提示\n"
	warningMsg += fmt.Sprintf("上个小时段发放总积分：%d，任务明细如下：\n", sumPoints)

	// 添加积分明细
	for taskType, points := range arrSumPoints {
		if taskType > 0 && points > 0 {
			if _, ok := arrUserTaskRows[taskType]; ok {
				warningMsg += fmt.Sprintf("> %s: %d\n",
					GetExchangeTypeMap()[taskType], points)
			}
		}
	}

	warningMsg += fmt.Sprintf("用户完成任务得的总积分:%d，任务明细如下：\n", allUserPoint)

	// 添加任务明细
	for taskType, taskInfo := range arrUserTaskRows {
		if taskType > 0 && taskInfo["sum_points"].(int64) > 0 {
			warningMsg += fmt.Sprintf("> %s: %d | 任务数量：%d\n",
				GetExchangeTypeMap()[taskType],
				taskInfo["sum_points"].(int64),
				taskInfo["task_count"].(int64))
		}
	}

	// 检查积分差异
	//取绝对值
	diffPoints := math.Abs(float64(sumPoints - allUserPoint))
	if diffPoints >= DiffPoints || allUserPoint >= PointsAllMax {
		err = service.SendMessage(ctx, warningMsg, "online")
		if err != nil {
			logc.Errorf(ctx, "RiskWarningHourCommand handleSendPoints SendMessage is err:%v, TraceId is: %s", err, traceID)
		}
	}
	err = service.SendMessage(ctx, warningMsg, "online-reconciliation")
	if err != nil {
		logc.Errorf(ctx, "RiskWarningHourCommand handleSendPoints SendMessage is err:%v, TraceId is: %s", err, traceID)
	}
}

func GetExchangeTypeMap() map[int]string {
	return map[int]string{
		1:  "挑战任务",
		2:  "福利任务",
		3:  "邀请任务",
		4:  "新人任务",
		5:  "签到任务",
		6:  "tg签到任务",
		7:  "限时任务",
		8:  "游戏中心",
		9:  "游戏中心-邀请",
		10: "新客注册任务",
		11: "新客入门任务",
		12: "新客进阶任务",
		13: "老客每日任务",
		14: "老客限时任务",
		99: "积分兑换",
	}
}

type ExchangePrizeRecord struct {
	PrizeId   int `gorm:"column:prize_id"`
	Type      int `gorm:"column:type"`
	TypeNum   int `gorm:"column:type_num"`
	SumPoints int `gorm:"column:sum_points"`
	CountNum  int `gorm:"column:count_num"`
}

// handleExchangeRecord 处理兑换记录
func handleExchangeRecord(ctx context.Context, svc *svc.ServiceContext, traceId string) {
	endTime := time.Now().Format("2006-01-02 15:04:05")

	records := make([]*ExchangePrizeRecord, 0)
	for i := TableNameNumStart; i < TableNameNumEnd; i++ {
		tableName := dao.GetWelfareExchangeRecordTableName(i)
		list := make([]*ExchangePrizeRecord, 0)
		err := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).DB.WithContext(ctx).Table(tableName).
			Select("prize_id, type, type_num, SUM(points) as sum_points, COUNT(prize_id) as count_num").
			Where("status = ? AND prize_source = ? AND updated_at BETWEEN ? AND ?",
				StatusSuccess,
				consts.RecordPrizeSourceExchange,
				GetTimeStr(-60),
				endTime).
			Group("prize_id").
			Find(&list).Error

		if err != nil {
			logc.Errorf(ctx, "RiskWarningHourCommand handleExchangeRecord query ExchangePrizeRecord err is:%v, TraceId is: %s", err, traceId)
			return
		}
		records = append(records, list...)
	}

	if len(records) == 0 {
		return
	}
	prizeIds := make([]int, 0, len(records))
	PrizeIdMap := map[int]struct{}{}
	for _, record := range records {
		if _, ok := PrizeIdMap[record.PrizeId]; !ok {
			prizeIds = append(prizeIds, record.PrizeId)
			PrizeIdMap[record.PrizeId] = struct{}{}
		}
	}
	prizeNameMap := map[int64]string{}
	if len(prizeIds) > 0 {
		prizes, err := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).QueryPrizeInfo(ctx, prizeIds)
		if err != nil {
			logc.Errorf(ctx, "RiskWarningHourCommand handleExchangeRecord  QueryPrizeInfo err is:%v, TraceId is: %s", err, traceId)
			return
		}
		for _, prize := range prizes {
			nameMap := map[string]string{}
			_ = json.Unmarshal([]byte(prize.PrizeName), &nameMap)
			//默认使用英文配置
			prizeNameMap[prize.Id] = nameMap["cn"]
			if nameMap["cn"] == "" {
				prizeNameMap[prize.Id] = nameMap[consts.EN]
			}
		}
	}
	data := make(map[int]map[string]int)
	for _, v := range records {
		prizeID := v.PrizeId
		if _, exists := data[prizeID]; !exists {
			data[prizeID] = map[string]int{
				"prize_id":   prizeID,
				"type":       v.Type,
				"type_num":   v.TypeNum,
				"count_num":  v.CountNum,
				"sum_points": v.SumPoints,
			}
		} else {
			data[prizeID]["count_num"] += v.CountNum
			data[prizeID]["sum_points"] += v.SumPoints
		}
	}

	// 构建告警信息
	warningMsg := "【新福利中心】积分兑换奖品详情 \n"
	for _, v := range data {

		prizeName := prizeNameMap[int64(v["prize_id"])]
		warningMsg += fmt.Sprintf("「奖品ID」：%d |「奖品名称」：%s|「奖品数量」：%d|「消耗积分」：%d\n",
			v["prize_id"], prizeName, v["count_num"], v["sum_points"])
	}

	SendWarningMsg(ctx, warningMsg, "online-reconciliation", traceId)
}
