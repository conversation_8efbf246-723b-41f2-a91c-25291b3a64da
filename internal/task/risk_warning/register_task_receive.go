package risk_warning

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/service/prize_service"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"strconv"
	"time"
)

func GetTimeDayStr(val int) string {
	return time.Now().AddDate(0, 0, val).Format(time.DateTime)
}

// 每小时执行一次
func RegisterTaskReceive(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {

	PrintJSON(param)
	logData("RegisterTaskReceive start", RiskWarningPrefix)
	traceID := trace.TraceIDFromContext(ctx)
	logc.Info(ctx, fmt.Sprintf("traceID: %s", traceID))
	handleNewbieFailedRegisterRecordRows(ctx, svcCtx, traceID)

	logData("RegisterTaskReceive end", RiskWarningPrefix)
	return fmt.Sprintf("traceID: %s", traceID)
}

// handleNewbieFailedPrizeRecordRows 处理新人任务奖品异常订单监控
func handleNewbieFailedRegisterRecordRows(ctx context.Context, svcCtx *svc.ServiceContext, traceID string) {
	recordList := []*dao.WelfareExchangeRecord{}
	for i := TableNameNumStart; i < TableNameNumEnd; i++ {
		list := []*dao.WelfareExchangeRecord{}
		tableName := dao.GetWelfareExchangeRecordTableName(i)
		// 构建查询
		err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.WithContext(ctx).Table(tableName).
			Where("status in (?)", []string{StatusInit, StatusFailed}).
			Where("prize_source = ?", consts.RecordPrizeSourceNewbieTaskNew).
			Where("updated_at <= ?", GetTimeStr(-60)).
			Where("updated_at > ?", GetTimeDayStr(-2)). //避免重复提醒，只查询两天的数据
			Find(&list).Error
		if err != nil {
			logc.Errorf(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows query WelfareExchangeRecord err is:%v, TraceID is: %s", err, traceID)
			return
		}
		recordList = append(recordList, list...)
	}

	if len(recordList) == 0 {
		return
	}
	warningMsg := "【新福利中心】注册任务奖励领取异常 \n 当前时段异常的注册任务如下："
	isSendMsg := false
	//发奖中和发奖失败的，
	for _, v := range recordList {
		userTask := &welfare_user_tasks.WelfareUserTasks{}
		if v.TaskID > 0 {
			err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.WithContext(ctx).
				Where("uid = ?", v.UID).Where("id = ?", v.TaskID).
				First(userTask).Error
			if err != nil {
				logc.Errorf(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows get userTask err is:%v, TraceID is: %s,%d,%d", err, traceID, v.UID, v.TaskID)
				return
			}
			//过滤掉非注册任务
			if userTask.Type != consts.UserTaskTypeNewbieRegister {
				continue
			}
		}
		//获取WelfareUserPrizeDetail信息
		prizeDetail, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetUserPrizeDetail(ctx, v.UID, v.ID)
		if err != nil {
			logc.Errorf(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows GetUserPrizeDetail is err，err is：%v, uid is:%d ", err, v.UID)
			continue
		}
		//如果时间超过5个小时则不重试发奖直接预警，手动排查
		if v.UpdatedAt.Unix() < time.Now().Add(-5*time.Hour).Unix() {
			logc.Infof(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows warningMsg uid is: %d,user_task_id is:%d ，Status is：%s, userTask.UpdatedAt is:%s , nowTime is: %s", v.UID, v.TaskID, v.Status, userTask.UpdatedAt.Format(time.DateTime), time.Now().Add(-5*time.Hour).Format(time.DateTime))
			isSendMsg = true
			warningMsg += fmt.Sprintf("\n exchange_records_id:%v，detail_id:%v，uid:%v，prize_id:%v，user_task_id:%v，status:%v",
				v.ID, prizeDetail.ID, v.UID, v.PrizeID, v.TaskID, v.Status)
			continue
		}
		taskInfo := &welfare_task_cfgs.WelfareTaskCfgs{}
		err = json.Unmarshal([]byte(userTask.Memo), taskInfo)
		if err != nil {
			logc.Errorf(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows json.Unmarshal([]byte(userTask.Memo),taskInfo) is err,params is:%s,%d ，err is：%v", userTask.Memo, v.UID, err)
			continue
		}
		registerCouponId := int64(0)
		registerSource := ""
		if taskInfo.Type == consts.UserTaskTypeNewbieRegister {
			registerTaskInfo := dao.RegisterTaskInfo{}
			err = json.Unmarshal([]byte(taskInfo.ExtraTaskInfo.String), &registerTaskInfo)
			if err != nil {
				logc.Errorf(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows json.Unmarshal.task.ExtraTaskInfo is err,params is:%s,%d ，err is：%v", taskInfo.ExtraTaskInfo.String, v.UID, err)
				continue
			}
			if registerTaskInfo.CouponId == 0 || registerTaskInfo.Source == "" {
				logc.Errorf(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows registerTaskInfo is err,params is:%s, %d ，err is：%v", taskInfo.ExtraTaskInfo.String, v.UID, err)
				continue
			}
			registerCouponId = registerTaskInfo.CouponId
			registerSource = registerTaskInfo.Source
		} else {
			logc.Errorf(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows registerTaskInfo taskInfo is not UserTaskTypeNewbieRegister,params is:%d, %d ，err is：%v", taskInfo.Type, v.UID, err)
			continue
		}
		userTaskId := userTask.Id
		if registerCouponId != 0 && registerSource != "" {
			resp, err := service_client.NewCouponCenterCall(ctx).CheckSendCouponProgress(registerSource, fmt.Sprintf("%d_%d", v.UID, v.ID))
			if err != nil {
				logc.Errorf(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows CheckSendCouponProgress is err，err is：%v, uid is:%d ", err, v.UID)
			}
			respByte, _ := json.Marshal(resp)
			logc.Infof(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows CheckSendCouponProgress uid is: %d,user_task_id is:%d ,coupon_id is:%d ，coupon_source is：%s, CouponProgress val is:%s", v.UID, v.TaskID, registerCouponId, registerSource, string(respByte))
			//发奖中状态直接跳过
			if resp != nil && resp.SendStatus == "SEND_INIT" {
				logc.Infof(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows CheckSendCouponProgress is SEND_INIT, uid is:%d ", v.UID)
				continue
			}
			detail := map[string]string{
				"sendPrizeStatus": "INIT",
			}
			status := consts.RecordStatusFailed
			//卡劵中心发放成功
			if resp != nil && resp.SendStatus == "SEND_SUCCESS" {
				detail["sendPrizeStatus"] = "SUCCESS"
				status = consts.RecordStatusSuccess
			} else {
				//请求卡券发放奖励
				sendCouponFormatResp := service.SendCouponPrice(ctx, v.UID, registerCouponId, strconv.FormatInt(v.ID, 10), "", registerSource)
				if sendCouponFormatResp.Code == consts.Success {
					detail["sendPrizeStatus"] = "SUCCESS"
					status = consts.RecordStatusSuccess
				} else {
					detail["sendPrizeStatus"] = "FAILED"
					if sendCouponFormatResp.CouponCode == -1 {
						detail["sendCouponStatus"] = "FAILED"
					}
				}
			}
			//当调用任务中心发奖成功之后会出现INIT的情况，调用任务中心发奖成功之后不做处理
			if detail["sendPrizeStatus"] != "INIT" {
				prizeSource := consts.GetTaskTypeMapRecordPrizeSource()[int(taskInfo.Type)]
				err = prize_service.UpdateRewardStatus(svcCtx, ctx, int(v.UID), int(v.ID), int(prizeDetail.ID), status, detail, int(registerCouponId), prizeSource, userTaskId, int(userTask.TaskCenterId), "")
				if err != nil {
					logc.Errorf(ctx, "RegisterTaskReceive handleNewbieFailedRegisterRecordRows UpdateRewardStatus is err，err is：%v, uid id:%d", err, v.UID)
					continue
				}
			}
		}

	}
	if isSendMsg {
		SendWarningMsg(ctx, warningMsg, "online", traceID)
	}
}
