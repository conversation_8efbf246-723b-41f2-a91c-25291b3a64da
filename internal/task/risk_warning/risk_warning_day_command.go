package risk_warning

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/trace"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"context"
	"fmt"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_points_records"
	"gateio_service_welfare_go/internal/models/welfare_user_points"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
)

const (
	ACTION_INCR          = "incr"
	ACTION_DECR          = "decr"
	RiskWarningDayPrefix = " riskWarningDay-"
)

func GetTestUidAry() []int {
	return []int{1627225,
		14315159,
		1627288,
		1336974}
}

var SumPoint struct {
	SumPoints int64 `gorm:"column:sum_points"`
}

// 验证总积分 每天执行一次
func RiskWarningDayCommand(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string) {
	PrintJSON(param)
	logData("RiskWarningDayCommand start", RiskWarningDayPrefix)
	traceID := trace.TraceIDFromContext(ctx)
	logc.Info(ctx, fmt.Sprintf("traceID: %s", traceID))
	arrMaxList := []*welfare_user_points.WelfareUserPointsNew{}
	err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.Model(&welfare_user_points.WelfareUserPointsNew{}).
		Select("updated_at").
		Where("uid NOT IN (?)", GetTestUidAry()).
		Order("updated_at DESC").
		Limit(1).
		Find(&arrMaxList).Error
	if err != nil {
		logc.Errorf(ctx, "RiskWarningDayCommand query arrMaxList is error  err is:%v, TraceId is: %s", err, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	if len(arrMaxList) == 0 {
		logc.Infof(ctx, "RiskWarningDayCommand arrMaxList len is 0, TraceId is: %s", traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	maxUpdateTime := arrMaxList[0].UpdatedAt
	var sumUserPoints int64
	// 查询积分记录
	sumPoint := &SumPoint
	// 计算用户剩余总积分
	err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.Model(&welfare_user_points.WelfareUserPointsNew{}).
		Where("uid NOT IN (?)", GetTestUidAry()).
		Where("updated_at <= ?", maxUpdateTime).
		Select("SUM(points) as sum_points").First(sumPoint).Error
	if err != nil {
		logc.Errorf(ctx, "RiskWarningDayCommand get sumUserPoints is error  err is:%v, TraceId is: %s", err, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	sumUserPoints = sumPoint.SumPoints

	var sumIncrPoints int64
	sumPoint = &SumPoint
	// 计算增加的总积分
	err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.Model(&welfare_points_records.WelfarePointsRecords{}).
		Where("action = ?", ACTION_INCR).
		Where("uid NOT IN (?)", GetTestUidAry()).
		Where("updated_at <= ?", maxUpdateTime).
		Select("SUM(points) as sum_points").First(sumPoint).Error
	if err != nil {
		logc.Errorf(ctx, "RiskWarningDayCommand get sumIncrPoints is error  err is:%v, TraceId is: %s", err, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	// 历史积分缺少5
	sumIncrPoints = sumPoint.SumPoints
	sumIncrPoints += 5

	var sumDecrPoints int64
	sumPoint = &SumPoint
	// 计算减少的总积分
	err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).DB.Model(&welfare_points_records.WelfarePointsRecords{}).
		Where("action = ?", ACTION_DECR).
		Where("uid NOT IN (?)", GetTestUidAry()).
		Where("updated_at <= ?", maxUpdateTime).
		Select("SUM(points) as sum_points").First(sumPoint).Error
	if err != nil {
		logc.Errorf(ctx, "RiskWarningDayCommand get sumDecrPoints is error  err is:%v, TraceId is: %s", err, traceID)
		return fmt.Sprintf("traceID: %s", traceID)
	}
	sumDecrPoints = sumPoint.SumPoints
	diffPoints := sumIncrPoints - sumDecrPoints - sumUserPoints
	warningMsg := fmt.Sprintf(" 【新福利中心】总积分对账 \n 发放总积分：%d \n 消耗总积分：%d \n 剩余总积分：%d  \n 相差积分：%d",
		sumIncrPoints, sumDecrPoints, sumUserPoints, diffPoints)
	logc.Infof(ctx, "diffPoints ,warningMsg 的值是：%d, %s", diffPoints, warningMsg)
	SendWarningMsg(ctx, warningMsg, "online-reconciliation", traceID)
	if diffPoints != 0 {
		err = service.SendMessage(ctx, warningMsg, "online")
		if err != nil {
			logc.Infof(ctx, "RiskWarningDayCommand SendMessage is err：%v, TraceId is: %s", err, traceID)
		}
	}
	logData("RiskWarningDayCommand end", RiskWarningDayPrefix)
	return fmt.Sprintf("traceID: %s", traceID)
}

func SendWarningMsg(ctx context.Context, warningMsg string, business, traceID string) {
	warningMsg = " " + warningMsg
	err := service.SendMessage(ctx, warningMsg, business)
	if err != nil {
		logc.Infof(ctx, "SendWarningMsg SendMessage is err：%v, TraceId is: %s", err, traceID)
	}
}
