package task

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"fmt"
	"gateio_service_welfare_go/internal/task/advanced_task_send_prize"
	"gateio_service_welfare_go/internal/task/clean_user_points"
	"gateio_service_welfare_go/internal/task/config_set"
	"gateio_service_welfare_go/internal/task/demo"
	"gateio_service_welfare_go/internal/task/risk_warning"
	"gateio_service_welfare_go/internal/task/shop"
	"gateio_service_welfare_go/internal/task/task_receive_coupon"
	"time"

	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"gateio_service_welfare_go/internal/svc"
)

// TaskFunc 是离线任务的方法签名，在实现离线任务时，入参和出参要和此方法保持一致
type TaskFunc func(ctx context.Context, svcCtx *svc.ServiceContext, param *xxljob.TaskRequest) (msg string)

func wrapTask(f TaskFunc, svcCtx *svc.ServiceContext) xxljob.TaskFunc {
	return func(ctx context.Context, param *xxljob.TaskRequest) (msg string) {
		// 添加 panic 恢复
		defer func() {
			if r := recover(); r != nil {
				logc.Errorf(ctx, "Task panic recovered: %v", r)
				msg = fmt.Sprintf("Task panic: %v", r)
			}
		}()
		// 参数验证
		if svcCtx == nil {
			return "ServiceContext is nil"
		}
		// 执行任务
		startTime := time.Now()
		msg = f(ctx, svcCtx, param)
		// 记录执行时间
		logc.Infof(ctx, "Task completed in %v", time.Since(startTime))
		return msg
	}
}

// RegisterTasks 负责将离线任务注册到 agent 中
func RegisterTasks(server *xxljob.Server, svcCtx *svc.ServiceContext) {
	server.RegisterTask("demo", wrapTask(demo.Demo, svcCtx))
	server.RegisterTask("advanced_task_send_prize", wrapTask(advanced_task_send_prize.AdvancedTaskSendPrize, svcCtx))
	server.RegisterTask("risk_warning_command", wrapTask(risk_warning.RiskWarningCommand, svcCtx))
	server.RegisterTask("risk_warning_day_command", wrapTask(risk_warning.RiskWarningDayCommand, svcCtx))
	server.RegisterTask("risk_warning_hour_command", wrapTask(risk_warning.RiskWarningHourCommand, svcCtx))
	server.RegisterTask("clean_user_points", wrapTask(clean_user_points.CleanUserPoints, svcCtx))
	server.RegisterTask("task_receive_coupon_warning", wrapTask(task_receive_coupon.TaskReceiveCouponWarning, svcCtx))
	server.RegisterTask("task_receive_coupon_deal", wrapTask(task_receive_coupon.TaskReceiveCouponWarningDo, svcCtx))
	server.RegisterTask("config_set", wrapTask(config_set.ConfigSet, svcCtx))
	server.RegisterTask("patch_shop", wrapTask(shop.PatchShop, svcCtx))
}
