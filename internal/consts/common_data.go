package consts

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/types"
)

// 定义国家信息
var CountryMap map[int]string

func GetCountryMap() map[int]string {
	if CountryMap == nil {
		CountryMap = map[int]string{
			260: "阿富汗",
			3:   "阿尔巴尼亚",
			4:   "阿尔及利亚",
			5:   "安道尔共和国",
			1:   "安哥拉",
			6:   "安圭拉岛",
			7:   "安提瓜和巴布达",
			8:   "阿根廷",
			9:   "亚美尼亚",
			194: "阿鲁巴",
			10:  "阿森松",
			11:  "澳大利亚",
			195: "澳大利亚海外领地",
			12:  "奥地利",
			13:  "阿塞拜疆",
			14:  "巴哈马",
			15:  "巴林",
			233: "孟加拉国",
			17:  "巴巴多斯",
			256: "巴基斯坦",
			258: "白俄罗斯",
			19:  "比利时",
			20:  "伯利兹",
			21:  "贝宁",
			22:  "百慕大群岛",
			196: "不丹",
			255: "玻利维亚",
			197: "波斯尼亚和黑塞哥维那",
			24:  "博茨瓦纳",
			25:  "巴西",
			229: "英属维尔京群岛",
			26:  "文莱",
			27:  "保加利亚",
			28:  "布基纳法索",
			231: "缅甸",
			30:  "布隆迪",
			85:  "柬埔寨",
			247: "喀麦隆",
			32:  "加拿大",
			198: "佛得角",
			33:  "开曼群岛",
			240: "中非共和国",
			241: "乍得",
			36:  "智利",
			37:  "中国",
			38:  "哥伦比亚",
			199: "科摩罗群岛",
			250: "刚果民主共和国(金)",
			251: "刚果共和国(布)",
			40:  "库克群岛",
			41:  "哥斯达黎加",
			246: "科特迪瓦",
			200: "克罗地亚",
			43:  "塞浦路斯",
			44:  "捷克",
			45:  "丹麦",
			201: "迭戈加西亚群岛",
			46:  "吉布提",
			254: "多米尼克",
			47:  "多米尼加共和国",
			202: "东帝汶",
			253: "厄瓜多尔",
			49:  "埃及",
			50:  "萨尔瓦多",
			230: "迪拜酋长国",
			203: "赤道几内亚",
			204: "厄立特里亚",
			51:  "爱沙尼亚",
			52:  "埃塞俄比亚",
			205: "福克兰群岛",
			206: "法罗群岛",
			53:  "斐济",
			54:  "芬兰",
			55:  "法国",
			56:  "法属圭亚那",
			136: "法属玻利尼西亚",
			57:  "加蓬",
			58:  "冈比亚",
			59:  "格鲁吉亚",
			60:  "德国",
			61:  "加纳",
			62:  "直布罗陀",
			63:  "希腊",
			207: "格陵兰岛",
			64:  "格林纳达",
			208: "瓜德罗普",
			65:  "关岛",
			66:  "危地马拉",
			248: "几内亚",
			209: "几内亚比绍",
			68:  "圭亚那",
			69:  "海地",
			70:  "洪都拉斯",
			71:  "中国香港",
			72:  "匈牙利",
			73:  "冰岛",
			74:  "印度",
			75:  "印度尼西亚",
			243: "伊拉克",
			78:  "爱尔兰",
			79:  "以色列",
			80:  "意大利",
			82:  "牙买加",
			83:  "日本",
			84:  "约旦",
			86:  "哈萨克斯坦",
			87:  "肯尼亚",
			210: "基里巴斯",
			88:  "韩国",
			257: "北韩",
			89:  "科威特",
			90:  "吉尔吉斯坦",
			91:  "老挝",
			92:  "拉脱维亚",
			245: "黎巴嫩",
			94:  "莱索托",
			261: "利比里亚",
			262: "利比亚",
			97:  "列支敦士登",
			98:  "立陶宛",
			99:  "卢森堡",
			259: "中国澳门",
			211: "马其顿",
			101: "马达加斯加",
			102: "马拉维",
			103: "马来西亚",
			104: "马尔代夫",
			105: "马里",
			106: "马耳他",
			107: "马绍尔群岛",
			108: "马提尼克",
			213: "毛里塔尼亚",
			109: "毛里求斯",
			110: "墨西哥",
			214: "密克罗尼西亚",
			111: "摩尔多瓦",
			112: "摩纳哥",
			113: "蒙古",
			215: "黑山",
			114: "蒙特塞拉特岛",
			115: "摩洛哥",
			116: "莫桑比克",
			117: "纳米比亚",
			118: "瑙鲁",
			119: "尼泊尔",
			120: "荷属安的列斯",
			121: "荷兰",
			216: "新喀里多尼亚",
			122: "新西兰",
			123: "尼加拉瓜",
			124: "尼日尔",
			125: "尼日利亚",
			217: "纽埃岛",
			127: "挪威",
			128: "阿曼",
			218: "帕劳",
			219: "巴勒斯坦",
			130: "巴拿马",
			131: "巴布亚新几内亚",
			132: "巴拉圭",
			133: "秘鲁",
			134: "菲律宾",
			135: "波兰",
			137: "葡萄牙",
			138: "波多黎各",
			139: "卡塔尔",
			140: "留尼旺",
			141: "罗马尼亚",
			142: "俄罗斯",
			220: "卢旺达",
			143: "圣卢西亚",
			222: "圣皮埃尔和密克隆群岛",
			144: "圣文森特岛",
			235: "圣基茨和尼维斯",
			145: "东萨摩亚(美)",
			146: "西萨摩亚",
			147: "圣马力诺",
			148: "圣多美和普林西比",
			149: "沙特阿拉伯",
			150: "塞内加尔",
			223: "塞尔维亚",
			151: "塞舌尔",
			152: "塞拉利昂",
			153: "新加坡",
			154: "斯洛伐克",
			155: "斯洛文尼亚",
			156: "所罗门群岛",
			236: "索马里",
			158: "南非",
			159: "西班牙",
			160: "斯里兰卡",
			221: "圣赫勒拿岛",
			161: "圣卢西亚",
			162: "圣文森特",
			164: "苏里南",
			165: "斯威士兰",
			166: "瑞典",
			167: "瑞士",
			169: "中国台湾",
			170: "塔吉克斯坦",
			171: "坦桑尼亚",
			172: "泰国",
			173: "多哥",
			224: "托克劳群岛",
			174: "汤加",
			175: "特立尼达和多巴哥",
			176: "突尼斯",
			177: "土耳其",
			178: "土库曼斯坦",
			237: "特克斯和凯科斯群岛",
			225: "图瓦卢",
			179: "乌干达",
			180: "乌克兰",
			181: "阿拉伯联合酋长国",
			232: "美国",
			182: "英国",
			264: "泽西",
			184: "乌拉圭",
			185: "乌兹别克斯坦",
			226: "瓦努阿图",
			227: "梵蒂冈城",
			187: "越南",
			228: "瓦利斯和富图纳",
			242: "也门",
			189: "南斯拉夫",
			192: "扎伊尔",
			193: "赞比亚",
			249: "津巴布韦",
			239: "伊朗",
			186: "委内瑞拉",
			252: "古巴",
			234: "苏丹",
			238: "叙利亚",
		}
	}
	return CountryMap
}

// 定义地区信息
var RegionMap map[string]string

func GetRegionMap() map[string]string {
	if RegionMap == nil {
		RegionMap = map[string]string{
			"high_worth":        "高净值地区",
			"risk_region":       "低净值地区", // =风控
			"common_region":     "普通地区",
			"sa_region":         "SA地区",
			"compliance_region": "合规地区",
		}
	}
	return RegionMap
}

// 定义任务类型映射
var TaskMarkMapTaskType map[string]int

func GetTaskMarkMapTaskType() map[string]int {
	if TaskMarkMapTaskType == nil {
		TaskMarkMapTaskType = map[string]int{
			"spot_transaction":      UserTaskTaskTypeSpots,
			"futures_transaction":   UserTaskTaskTypeFutures,
			"invite_task":           UserTaskTaskTypeInvite,
			"transaction_robot":     UserTaskTaskTypeQuantitative,
			"uniloan":               UserTaskTaskTypeLendEarn,
			"recharge":              UserTaskTaskTypeDeposits,
			"c2c_transaction":       UserTaskTaskTypeC2c,
			"launch_pool":           UserTaskTaskTypeLanchPool,
			"spot_lever":            UserTaskTaskTypeLever,
			"app_download_v2":       UserTaskTaskTypeAppDownload, // app下载任务
			"memebox_transaction":   UserTaskTaskTypeAlpha,
			"kyc":                   UserTaskTaskTypeKyc2,                // mark为kyc的任务
			"futures_spot_assembly": UserTaskTaskTypeFuturesSpotAssembly, // 现货和合约交易
			"hodler_airdrop":        UserTaskTaskTypeHolderAirdrop,       //hodler_airdrop
			"social_media":          UserTaskTaskTypeMedia,
		}
	}
	return TaskMarkMapTaskType
}

// 卡券中心类型中文名
var TaskTypeNameMap map[int]string

func GetTaskTypeNameMap() map[int]string {
	if TaskTypeNameMap == nil {
		TaskTypeNameMap = map[int]string{
			TaskCenterTypeRegular:  "常规任务",
			TaskCenterTypeCycle:    "循环任务",
			TaskCenterTypeRepeat:   "多次任务",
			TaskCenterTypeMultiple: "用户可领取多次任务",
		}
	}
	return TaskTypeNameMap
}

// 任务系统状态和我方状态映射
var TaskStatusMapWelfareStatus map[int]int

func GetTaskStatusMapWelfareStatus() map[int]int {
	if TaskStatusMapWelfareStatus == nil {
		TaskStatusMapWelfareStatus = map[int]int{
			TaskStatusNoStart:           StatusNoStart,
			TaskStatusInProcess:         StatusInProcess,
			TaskStatusDone:              StatusDone,
			TaskStatusExpire:            StatusExpire,
			TaskStatusPrizeSettlement:   StatusSettlement,
			TaskStatusPrizeInSettlement: StatusInSettlement,
		}
	}
	return TaskStatusMapWelfareStatus
}

// 获取地区对应的描述中的最高奖励信息
var RegionMaxDescAllReward map[string]string

func GetRegionMaxDescAllReward() map[string]string {
	if RegionMaxDescAllReward == nil {
		RegionMaxDescAllReward = map[string]string{
			NewbieTaskRegionHighWorth:  "10000",
			NewbieTaskRegionRisk:       "10000",
			NewbieTaskRegionCommon:     "10000",
			NewbieTaskRegionSa:         "10000",
			NewbieTaskRegionCompliance: "10000",
		}
	}
	return RegionMaxDescAllReward
}

// 获取处理中的任务状态
func GetProcessTaskStatus() []int {
	return []int{StatusNoStart, StatusInProcess, StatusDone, StatusInSettlement}
}

// 获取终态的任务状态
func GetDoneTaskStatus() []int {
	return []int{StatusSettlement, StatusExpire}
}

// 身份信息
var IdentityMap map[int]string

func GetIdentityMap() map[int]string {
	if IdentityMap == nil {
		IdentityMap = map[int]string{
			IdentityNormal: "普通用户",
			IdentityVIP:    "VIP",
		}
	}
	return IdentityMap
}

// 任务奖励类型中文信息
var TaskRewardTypeCNMap map[int64]string

func GetTaskRewardTypeCNMap() map[int64]string {
	if TaskRewardTypeCNMap == nil {
		TaskRewardTypeCNMap = map[int64]string{
			PrizeTypePoints: "积分",
			PrizeTypeDiy:    "业务自定义",
			PrizeTypeCoupon: "卡券",
			PrizeTypePool:   "奖池",
		}
	}
	return TaskRewardTypeCNMap
}

// 卡券中心类型和我方类型映射
var CouponTypeMapWelfareType map[string]int

// 能对应上的从map里取
func GetCouponTypeMapWelfareType() map[string]int {
	if CouponTypeMapWelfareType == nil {
		CouponTypeMapWelfareType = map[string]int{
			"point":                  PointsShopTypePoint,                // 点卡
			"startup_voucher":        PointsShopTypeStartup,              // Startup劵
			"contract_bonus":         PointsShopTypeFutures,              // 合约体验金
			"contract_bonus_new":     PointsShopTypeContractBonusNew,     // 合约体验券
			"hold_bonus":             PointsShopTypeFinance,              // 理财体验金
			"robot_bonus":            PointsShopTypeQuantitativeNew,      // 新版量化体验金(机器人体验金)
			"quantitative_bonus":     PointsShopTypeQuantitative,         // 量化体验金
			"loss_protection_copier": PointsShopTypeLossProtectionCopier, // 跟单包赔券
			"vip_card":               PointsShopTypeVipCard,              // VIP体验卡
		}
	}
	return CouponTypeMapWelfareType
}

// map里没有的，从方法里拿，手续费返现券（现货/合约）
func GetCouponTypeByTypeAndSubType(typeStr, subType string) int {
	// 获取卡券类型映射
	couponTypeMap := GetCouponTypeMapWelfareType()

	// 检查type是否存在于映射中
	if val, exists := couponTypeMap[typeStr]; exists {
		return val
	}

	// 如果type不存在，进行逻辑处理
	switch typeStr {
	case "commission_rebate":
		if subType == "spot" {
			return PointsShopTypeSpotCommissionRebate
		}
		if subType == "futures" {
			return PointsShopTypeFuturesCommissionRebate
		}
		if subType == "innovation" {
			return PointsShopTypeInnovationCommissionRebate
		}
	default:
		// 默认返回一个未知类型或错误码
		return -1
	}
	// 返回默认值以避免编译错误
	return -1
}

// 根据不同任务类型转换任务额外字段为对应的类
func GetExtraTaskInfo(task *welfare_task_cfgs.WelfareTaskCfgs) (interface{}, error) {
	var result interface{}

	switch task.Type {
	case UserTaskTypeNewbieRegister:
		registerTaskInfo := &types.RegisterTaskInfo{}
		err := json.Unmarshal([]byte(task.ExtraTaskInfo.String), registerTaskInfo)
		if err != nil {
			log.Printf("ConvertExtraTaskInfo failed to unmarshal RegisterTaskInfo: %v", err)
			return nil, err
		}
		result = registerTaskInfo

	case UserTaskTypeNewbieGuide:
		welcomeTaskInfo := &types.WelcomeTaskInfo{}
		err := json.Unmarshal([]byte(task.ExtraTaskInfo.String), welcomeTaskInfo)
		if err != nil {
			log.Printf("ConvertExtraTaskInfo failed to unmarshal WelcomeTaskInfo: %v", err)
			return nil, err
		}
		result = welcomeTaskInfo

	case UserTaskTypeNewbieAdvanced:
		advancedTaskInfo := &types.AdvancedTaskInfo{}
		err := json.Unmarshal([]byte(task.ExtraTaskInfo.String), advancedTaskInfo)
		if err != nil {
			log.Printf("ConvertExtraTaskInfo failed to unmarshal AdvancedTaskInfo: %v", err)
			return nil, err
		}
		result = advancedTaskInfo

	case UserTaskTypeVeteranDaily:
		dailyTaskInfo := &types.DailyTaskInfo{}
		err := json.Unmarshal([]byte(task.ExtraTaskInfo.String), dailyTaskInfo)
		if err != nil {
			log.Printf("ConvertExtraTaskInfo failed to unmarshal DailyTaskInfo: %v", err)
			return nil, err
		}
		result = dailyTaskInfo

	case UserTaskTypeVeteranLimited:
		limitedTaskInfo := &types.DailyTaskInfo{}
		err := json.Unmarshal([]byte(task.ExtraTaskInfo.String), limitedTaskInfo)
		if err != nil {
			log.Printf("ConvertExtraTaskInfo failed to unmarshal LimitedTaskInfo: %v", err)
			return nil, err
		}
		result = limitedTaskInfo
	default:
		// 未知类型处理
		log.Printf("ConvertExtraTaskInfo encountered unknown type: %d", task.Type)
		return nil, fmt.Errorf("unknown task type: %d", task.Type)
	}

	return result, nil
}

// 根据不同任务类型获取额外奖励
func GetExtraRewardName(task *welfare_task_cfgs.WelfareTaskCfgs) string {
	result, err := GetExtraTaskInfo(task)
	if err != nil {
		log.Printf("GetExtraRewardName GetExtraTaskInfo nil: %v", err)
		return ""
	}

	if task.Type == UserTaskTypeNewbieRegister {
		registerTaskInfo, _ := result.(*types.RegisterTaskInfo)
		var rewardNames []string
		for _, reward := range registerTaskInfo.ExtraRewards {
			if reward.RewardName != "" {
				rewardNames = append(rewardNames, reward.RewardName)
			}
		}
		return strings.Join(rewardNames, ",")
	}
	return ""
}

// 根据不同任务类型获取额外奖励
func GetLimitTimeRewardName(task *welfare_task_cfgs.WelfareTaskCfgs) string {
	result, err := GetExtraTaskInfo(task)
	if err != nil {
		log.Printf("GetExtraRewardName GetExtraTaskInfo nil: %v", err)
		return ""
	}

	if task.Type == UserTaskTypeNewbieGuide {
		welcomeTaskInfo, _ := result.(*types.WelcomeTaskInfo)
		var rewardNames []string
		for _, reward := range welcomeTaskInfo.LimitRewards {
			if reward.RewardName != "" {
				rewardNames = append(rewardNames, reward.RewardName)
			}
		}
		return strings.Join(rewardNames, ",")

	}
	return ""
}

// 根据不同任务类型获取额外奖励
func GetStairsRewardName(task *welfare_task_cfgs.WelfareTaskCfgs) string {
	result, err := GetExtraTaskInfo(task)
	if err != nil {
		log.Printf("GetExtraRewardName GetExtraTaskInfo nil: %v", err)
		return ""
	}

	if task.Type == UserTaskTypeVeteranDaily || task.Type == UserTaskTypeVeteranLimited {
		dailyTaskInfo, _ := result.(*types.DailyTaskInfo)
		var rewardNames []string
		for i, stairsInfo := range dailyTaskInfo.StairsInfos {
			for _, reward := range stairsInfo.Rewards {
				if reward.RewardName != "" && i != 0 {
					rewardNames = append(rewardNames, reward.RewardName)
				}
			}
		}
		return strings.Join(rewardNames, ",")

	}
	return ""
}

/*
//卡劵类型：现金奖励券
public const  COUPON_TYPE_CASH_REWARD_VOUCHER = 'cash_reward_voucher';
//卡劵类型：加息券
public const  COUPON_TYPE_FINANCIAL_RATE = 'financial_rate';
//卡劵类型：礼品卡
public const COUPON_TYPE_GIFT_CARD = 'gift_card';
//卡券类型：跟单包赔券
public const COUPON_TYPE_LOSS_PROTECTION_COPIER = 'loss_protection_copier';
//卡券类型：创新区代币空投
public const  COUPON_TYPE_PILOT_TOKEN_AIRDROP = 'pilot_token_airdrop';
//卡券类型：Alpha代币空投
public const  COUPON_TYPE_ALPHA = 'alpha'; */

var TaskTypeMapRecordPrizeSource map[int]string

// 获取任务type对应奖品表的prizeSource
func GetTaskTypeMapRecordPrizeSource() map[int]string {
	if TaskTypeMapRecordPrizeSource == nil {
		TaskTypeMapRecordPrizeSource = map[int]string{
			UserTaskTypeNewbieRegister: RecordPrizeSourceNewbieTaskNew,
			UserTaskTypeNewbieGuide:    RecordPrizeSourceNewbieTaskNew,
			UserTaskTypeNewbieAdvanced: RecordPrizeSourceNewbieTaskNew,
			UserTaskTypeVeteranDaily:   RecordPrizeSourceDailyTasks,
			UserTaskTypeVeteranLimited: RecordPrizeSourceLimitedTask,
			UserTaskTypeCheckIn:        RecordPrizeSourceCheckinTasks,
		}
	}
	return TaskTypeMapRecordPrizeSource
}

// 获取任务生效状态
func GetTaskStatusMap(status int, onlineStatus int) int {
	if status == ReviewStatusInit {
		return EffectiveStatusReview
	} else if status == ReviewStatusApproval && onlineStatus == StatusOnline {
		return EffectiveStatusValid
	} else {
		return EffectiveStatusInValid
	}
}

// 定义获取任务交易量mark的公共函数
func GetTaskCenterMinMarks() []string {
	//交易量的mark
	return []string{
		"accumulated_amount",
		"balance",
		"first_amount",
		"first_reach_amount",
	}
}
