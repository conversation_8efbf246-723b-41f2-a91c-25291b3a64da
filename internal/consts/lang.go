package consts

// 定义语言常量
const (
	EN = "en"
	CN = "cn"
	TW = "tw"
	KR = "kr"
	VN = "vn"
	ES = "es"
	RU = "ru"
	FR = "fr"
	DE = "de"
	IT = "it"
	PT = "pt"
	BR = "br"
	TH = "th"
	ID = "id"
	TR = "tr"
	JA = "ja"
	AR = "ar"
	UK = "uk"
	NL = "nl"
)

// SupportedLangMap 定义语言对应名称的映射
var SupportedLangMap = map[string]string{
	EN: "英语",
	CN: "简体中文",
	TW: "繁体中文",
	KR: "韩语",
	VN: "越南语",
	ES: "西班牙语",
	RU: "俄语",
	FR: "法语",
	DE: "德语",
	IT: "意大利",
	PT: "葡萄牙语（葡萄牙）",
	BR: "葡萄牙语（巴西）",
	TH: "泰国语",
	ID: "印尼语",
	TR: "土耳其文",
	JA: "日语",
	AR: "阿拉伯语",
	UK: "乌克兰语",
	NL: "荷兰语",
}

var BackNeedLangMap = map[string]string{
	EN: "英语",
	CN: "简体中文",
	TW: "繁体中文",
	VN: "越南语",
	ES: "西班牙语",
	RU: "俄语",
	FR: "法语",
	PT: "葡萄牙语（葡萄牙）",
	BR: "葡萄牙语（巴西）",
	ID: "印尼语",
	JA: "日语",
	AR: "阿拉伯语",
	UK: "乌克兰语",
}

var LanguageOrder = []string{
	EN, CN, TW, KR, VN, ES, RU, FR, DE, IT, PT, BR, TH, ID, TR, JA, AR, UK, NL,
}
