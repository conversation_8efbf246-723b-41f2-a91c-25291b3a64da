package consts

// 所有的source需删除：admin前缀

// 线上source配置
const (
	// 签到source
	CheckInSource = "ehmfwvgjrm"
)

// 测试环境source配置
const (
	// 签到source-test
	CheckInSourceTest = "jumiqfqziw"
)

// TaskSourceTestMap source生产环境和测试环境的映射关系
var TaskSourceTestMap = map[string]string{
	CheckInSource: CheckInSourceTest,
}

// 获取真实的source
func GetRealSource(prodSource string) string {
	if testSource, exists := TaskSourceTestMap[prodSource]; exists {
		return testSource
	}
	return prodSource
}
