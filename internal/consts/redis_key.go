package consts

const (
	PrefixKey             = "welfare_go:"
	TestKey               = "welfare_go:test"
	NewbieTaskUserInfoKey = "welfare_go:newbieTask:userinfo:%d" // 新人任务-查询用户信息的redis-key

	FeeSettingMmWhiteListKey = "welfare_go:fee:setting:mm:white:list"
	ComplianceCheckLimitKey  = "compliance:limitBusiness:%d:%d" // 合规数据

	FirstVisitKey  = "welfare_go:first_visit:%d"         // 首次访问
	TodayCheckIn   = "welfare_go:today_check_in:%d:%d"   // 今天签到
	CheckInLock    = "welfare_go:checkIn:lock:%d"        // 签到lock
	UserPoints     = "welfare_go:userPoints:uid:%d"      // 用户积分
	PointsExchange = "welfare_go:pointsExchange:lock:%d" // 用户积分兑换

	TaskCenterTaskInfoKey         = "welfare_go:task:center:info:%d"       // 任务中心的任务详情
	TaskCenterUserTaskInfoKey     = "welfare_go:task:center:info:%d:%d"    // 任务中心的用户任务详情
	TaskCenterUserTaskBusinessKey = "welfare_go:task:center:info:%d:%d:%s" // 任务中心的用户领取任务详情

	WelfareConfigKey = "welfare_go:welfareConfig:getConfig:%s" // 福利中心配置key

	CouponInfoAmountKey = "welfare_go:coupon:info:amount:%d:%s"   //卡劵详情key
	WelfareUserRiskKey  = "welfare_go:welfare:user:risk:%d:%s:%s" //风控信息key
	WelfareReceivePrize = "welfare_go:welfare_receive_prize:%d"   //领取奖励KEY
	CouponInfoAll       = "welfare_go:coupon:info:all:%d:%s"      // 卡劵详情key

	WelfareUserRegisterInfoKey = "welfare_go:welfare:user:register:info:%d" // 用户快照key

	WelfareBeginnerUserTasksKey = "welfare_go:welfare:beginner:user:tasks:%d:%s:%d" // 新客入门任务列表缓存key 参数包含 用户ID，语言，是否是app请求
	WelfareAdvancedUserTasksKey = "welfare_go:welfare:advanced:user:tasks:%d"       // 新客进阶任务列表缓存key

	UserCenterLastReportInfoKey = "welfare_go:user:center:last:report:info:%s"  // 用户中心用户最后一次app登陆信息缓存key
	WelfarePrizeCurrentLimitKey = "welfare_go:handleRedisCurrentLimit:%d:%d:%d" // 积分兑换奖品库存

	WelfareDailyUserTasksKey     = "welfare_go:welfare:daily:user:tasks:%d:%s" //老客日常任务列表缓存 uid+lang
	WelfareLimitUserTasksKey     = "welfare_go:welfare:limit:user:tasks:%d:%s" //老客限时任务列表缓存 uid+lang
	WelfareUidAndTaskIdAndStatus = "welfare_go:uid:taskId:status:%d:%d"        //用户任务状态缓存
)
