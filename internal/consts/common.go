package consts

const (
	// 风控事件 task_center日常 index_page_check新人任务
	RiskEventCodeTaskCenter     = "task_center"
	RiskEventCodeIndexPageCheck = "index_page_check"

	GoInitializeTime = "2006-01-02 15:04:05" // go初始化时间

	NewbieTask    = "newbie_task"
	WelfareTask   = "welfare_task"
	InviteTask    = "invite_task"
	NewbieTaskNew = "newbie_task_new"
	PrizeList     = "prize_list"
	CheckInTask   = "check_in_task"
	TgCheckInTask = "tg_check_in_task"

	NewbieBeginTask    = "newbie_begin_task"    // 新人入门任务
	NewbieAdvanceTask  = "newbie_advance_task"  // 新人进阶任务
	NewbieRegisterTask = "newbie_register_task" // 新人注册任务
	DailyTask          = "daily_task"           // 老客日常任务
	LimitTask          = "limit_task"           // 老客限时任务
	PointsShop         = "points_shop"          // 积分商城

	// type 类型
	UserTaskTypeNewbie         = 1  // 1挑战任务
	UserTaskTypeWelfare        = 2  // 2福利任务
	UserTaskTypeInvite         = 3  // 3邀请任务
	UserTaskTypeNewbieNew      = 4  // 4新人任务
	UserTaskTypeCheckIn        = 5  // 5签到任务
	UserTaskTypeTgCheckIn      = 6  // 6tg签到任务
	UserTaskTypeTimeLimit      = 7  // 7限时任务
	UserTaskTypeTgGame         = 8  // 游戏中心
	UserTaskTypeTgInvite       = 9  // 游戏中心-邀请
	UserTaskTypeNewbieRegister = 10 // 新客注册任务，新用户注册后触发的任务
	UserTaskTypeNewbieGuide    = 11 // 新客入门任务，引导新用户完成基础操作的任务
	UserTaskTypeNewbieAdvanced = 12 // 新客进阶任务，帮助新用户深入了解平台功能的任务
	UserTaskTypeVeteranDaily   = 13 // 老客每日任务，老用户每天可重复完成的任务
	UserTaskTypeVeteranLimited = 14 // 老客限时任务，老用户在特定时间段内可参与的任务
	UserTaskTypePointsExchange = 99 // 积分兑换

	// task_type 任务类型：1kyc2认证 2现货 3合约 4邀请 5量化 6余币宝 7startup 8首次入金
	UserTaskTaskTypeKyc2                   = 1  // kyc2认证
	UserTaskTaskTypeSpots                  = 2  // 现货
	UserTaskTaskTypeFutures                = 3  // 合约
	UserTaskTaskTypeInvite                 = 4  // 邀请
	UserTaskTaskTypeQuantitative           = 5  // 量化-交易机器人
	UserTaskTaskTypeLendEarn               = 6  // 余币宝
	UserTaskTaskTypeStartup                = 7  // startup
	UserTaskTaskTypeDeposits               = 8  // 首次入金
	UserTaskTaskTypeAutoInvestment         = 9  // 定投理财
	UserTaskTaskTypeDualCurrencyInvestment = 10 // 双币投资
	UserTaskTaskTypeHodl                   = 11 // 理财宝
	UserTaskTaskTypeCardBuyCoin            = 12 // 双卡买币（信用卡/借记卡）
	UserTaskTaskTypeTgBind                 = 13 // tg绑定
	UserTaskTaskTypeTgRegisterKyc2         = 14 // tg注册身份认证
	UserTaskTaskTypeTgGame                 = 15 // tg-game
	UserTaskTaskTypeMedia                  = 16 // 社媒
	UserTaskTaskTypeC2c                    = 17 // C2C
	UserTaskTaskTypeLanchPool              = 18 // lanchpool  以前叫新币挖矿
	UserTaskTaskTypeTgInvite               = 19 // 游戏中心-邀请
	UserTaskTaskTypeLever                  = 20 // 逐仓杠杆
	UserTaskTaskTypeNetDeposits            = 21 // 净入金
	UserTaskTaskTypeNetFutures             = 22 // 净合约
	UserTaskTaskTypeAppDownload            = 23 // app下载任务
	UserTaskTaskTypeAlpha                  = 24 // alpha任务
	UserTaskTaskTypeHolderAirdrop          = 25 // HOLDer Airdrop任务
	UserTaskTaskTypeRegister               = 26 // 注册
	UserTaskTaskTypeAdvanced               = 27 // 福利中心进阶任务
	UserTaskTaskTypeFuturesSpotAssembly    = 28 //现货和合约交易(futures_spot_assembly)

	UserTaskTaskTypeCheckIn = 99 // 签到

	NewbieTaskRegionHighWorth  = "high_worth"        // 新人任务高净值区域
	NewbieTaskRegionRisk       = "risk_region"       // 新人任务风控管理区域
	NewbieTaskRegionCommon     = "common_region"     // 新人任务普通区域
	NewbieTaskRegionSa         = "sa_region"         // sa 地区
	NewbieTaskRegionCompliance = "compliance_region" // 合规地区

	// WelfareConfig name值
	NewbieTaskRiskCountryList      = "newbie_task_risk_country_list"       // 新人任务-风控管理区域
	NewbieTaskHighWorthCountryList = "newbie_task_high_worth_country_list" // 新人任务-高净值区域
	NewbieTaskSaCountryList        = "newbie_task_sa_country_list"         // 新人任务-SA区域
	NewbieTaskBlackCountryList     = "newbie_task_black_country_list"      // 黑名单国家
	ExchangePrize                  = "exchange_prize"                      // 兑换奖品开关
	WelfareRefactorTime            = "welfare_refactor_time"               // 新系统上线T值
	WelfareConfigPointsSend        = "welfare_config_points_send"          //领取积分开关
	WelfareConfigCouponSend        = "welfare_config_coupon_send"          //请求卡券系统开关
	WelfareConfigTaskSend          = "welfare_config_task_send"            //请求任务系统开关

	IsBlackCountryYes     = 1 // 黑名单国家
	IsBlackCountryNo      = 2 // 非黑名单国家
	UserAgencyTypePartner = 3 // 合伙人

	StatusNoStart      = 0 // 0未完成
	StatusInProcess    = 1 // 1进行中
	StatusDone         = 2 // 2未领奖
	StatusInSettlement = 3 // 3结算中
	StatusSettlement   = 4 // 4已完成
	StatusExpire       = 5 // 5过期

	NewbieTaskCfgTypeCountry    = 1 // 任务配置信息枚举值 国家
	NewbieTaskCfgTypeCrowd      = 2 // 任务配置信息枚举值 人群
	NewbieTaskCfgTypeSubWebsite = 3 // 任务配置信息枚举值 站点

	// 用户类型常量信息
	UserTypeTg = 5 // tg游客

	/**
	 * verified：2 kyc1初级认证 1 kyc2高级认证 4 kyc3 住址证明认证 3 企业认证
	 * KYC 0.5 等级，介于KYC0 和KYC1 之间的等级，需满足完成LN 扫描
	 */
	KycLv1       = 2
	KycLv2       = 1
	KycLv3       = 4
	KycLvCompany = 3
	KycLv0       = 0
	KycLv05      = 5

	// points_shop prize_sub_type
	PointsShopTypePoint               = 1  // 点卡
	PointsShopTypeVip1                = 2  // vip1
	PointsShopTypeFutures             = 3  // 合约体验金
	PointsShopTypeStartup             = 4  // startup券
	PointsShopTypeQuantitative        = 5  // 量化体验金
	PointsShopTypeVipAdd1             = 6  // vip+1
	PointsShopTypeFinance             = 7  // 理财体验金
	PointsShopTypeContractBonusNew    = 8  // 合约体验券
	PointsShopTypeFinanceHighInterest = 9  // 高息理财
	PointsShopTypeCommissionRebate10  = 10 // 10%手续费返现券
	PointsShopTypeCommissionRebate20  = 11 // 20%手续费返现券
	// 任务系统奖励
	PointsShopTypeTaskSysPoints    = 12 // 积分
	PointsShopTypeTaskSysDiy       = 13 // 业务自定义
	PointsShopTypeTaskSysCoupon    = 14 // 卡券
	PointsShopTypeTaskSysPrizePool = 15 // 奖池

	PointsShopTypeQuantitativeNew            = 16 // 新版量化体验金
	PointsShopTypeSpotCommissionRebate       = 17 // 现货手续费返现券
	PointsShopTypeFuturesCommissionRebate    = 18 // 合约手续费返现券
	PointsShopTypeLossProtectionCopier       = 19 // 跟单包赔券
	PointsShopTypeVipCard                    = 20 // VIP体验卡
	PointsShopTypeInnovationCommissionRebate = 21 // 创新区返现券

	// points_shop status 状态：1=上架, 2=下架
	PointsShopStatusOnline  = 1 // 上架
	PointsShopStatusOffline = 2 // 下架

	// 签到任务发放积分类型
	CheckInPoints        = 1 // 积分
	CheckInCoupon        = 2 // 卡券
	CheckInStatusSuccess = 1 // 成功
	CheckInStatusFailed  = 2 // 失败
	CheckInStatusProcess = 3 // 发奖中

	// 数据库中积分变化的来源类型
	ExSourceTypeChallenge                    = 1  // 挑战任务，用户可参与的各类挑战活动
	ExSourceTypeWelfare                      = 2  // 福利任务，完成后可获得额外福利的任务
	ExSourceTypeInvite                       = 3  // 邀请任务，通过邀请好友获得奖励的任务
	ExSourceTypeNewUser                      = 4  // 新人任务，专为新用户设计的入门任务
	ExSourceTypeCheckIn                      = 5  //  签到任务，每日签到获取奖励的任务
	ExSourceTypeTGCheckIn                    = 6  //  Telegram签到任务，在Telegram平台完成的签到任务
	ExSourceTypeLimitedTime                  = 7  //  限时任务，在特定时间段内开放的任务
	ExSourceTypeGameCenter                   = 8  //  游戏中心任务，游戏中心内的通用任务
	ExSourceTypeGameCenterInvite             = 9  //  游戏中心邀请任务，游戏中心内的邀请类任务
	ExSourceTypeNewCustomerRegister          = 10 //  新客注册任务，新用户注册后触发的任务
	ExSourceTypeOnboarding                   = 11 //  入门任务，引导用户完成基础操作的任务
	ExSourceTypeAdvanced                     = 12 //  进阶任务，帮助用户深入了解平台功能的任务
	ExSourceTypeDaily                        = 13 //  每日任务，每日可重复完成的任务
	ExSourceTypeLimitedTimeEvent             = 14 //  限时活动任务，特定活动期间的限时任务
	ExSourceTypePointsExpire                 = 15 //  积分过期，系统自动处理积分过期的任务
	ExSourceTypePointsClearBeforeExpansion   = 16 //  扩容前清空积分，系统扩容前的积分清理任务
	ExSourceTypePointsIncreaseAfterExpansion = 17 //  扩容增加积分，系统扩容后增加积分的任务
	ExSourceTypePointsExchange               = 99 //  积分兑换，用户使用积分兑换奖品的任务

	AdvancedTaskDelayDays = 3 // 进阶任务延时结算天数

	TaskCenterMarkAppDownload            = "app_download_v2"       // 下载任务判断mark
	TaskCenterMarkAppRecharge            = "recharge"              // 入金任务判断mark
	TaskCenterMarkAppFuturesSpotAssembly = "futures_spot_assembly" // 现货合约交易任务判断mark

	TaskReceivedTypeWelfare = 1  // 任务领取方式  1:用户自己领取   2:后台派发
	WelfareTaskBusinessType = 18 // 对应任务系统的业务枚举值

	TaskStatusNoStart           = 1 // 未开始
	TaskStatusInProcess         = 2 // 进行中
	TaskStatusDone              = 3 // 已完成
	TaskStatusExpire            = 4 // 已失效
	TaskStatusPrizeSettlement   = 5 // 奖励已发放
	TaskStatusPrizeInSettlement = 6 // 奖励发放中

	//任务生效时间
	TaskEffectiveTimeTypeFixed    = 1 //固定时间
	TaskEffectiveTimeTypeDrawDown = 2 //领取后

	// 任务中心的奖励类型信息
	PrizeTypePoints = 1 // 积分
	PrizeTypeDiy    = 2 // 业务自定义
	PrizeTypeCoupon = 3 // 卡券
	PrizeTypePool   = 4 // 奖池

	// 积分商城的商品类型
	ShopPrizeTypePoints = 1 // 卡券

	// 身份枚举
	IdentityNormal = 1 // 普通用户
	IdentityVIP    = 2 // VIP

	// 新客老客
	CustomerTypeAll       = 0 // 全体
	CustomerTypeNewbie    = 1 // 新客
	CustomerTypeVeteran   = 2 // 老客
	CustomerTypeOldNewbie = 3 // 旧福利中心新客

	// 上线状态
	StatusOnline  = 1 // 上线
	StatusOffline = 2 // 下线

	// 审核状态
	ReviewStatusInit     = 1 // 提交审核、审核中
	ReviewStatusApproval = 2 // 审核通过

	// 生效状态（由上线状态和审核状态共同决定）
	EffectiveStatusReview  = 1 // 待审核
	EffectiveStatusValid   = 2 // 已生效
	EffectiveStatusInValid = 3 // 已失效

	// 是否访问过新客福利中心（访问新客接口）1是0否
	IsVisitNewbieYes = 1
	IsVisitNewbieNo  = 0

	// 是否弹过弹窗 1是0否
	IsPopUpYes = 1
	IsPopUpNo  = 0

	RecordStatusInit    = "INIT"    // 初始化值
	RecordStatusFailed  = "FAILED"  // 失败
	RecordStatusSuccess = "SUCCESS" // 成功

	// welfare_exchange_records  prize_source
	RecordPrizeSourceExchange         = "exchange"           // 积分兑换
	RecordPrizeSourceNewbieTaskNew    = "newbie_task_new"    // 新人任务发奖
	RecordPrizeSourceLimitedTimeTasks = "limited_time_tasks" // 老客限时任务
	RecordPrizeSourceLimitedTask      = "limited_tasks"      // 限时任务
	RecordPrizeSourceDailyTasks       = "daily_tasks"        // 每日任务
	RecordPrizeSourceCheckinTasks     = "checkin_tasks"      // 签到任务

	CommonYes = 1 // 公共是
	CommonNo  = 2 // 公共否

	PointsRecordActionIncr = "incr" // 增加积分
	PointsRecordActionDecr = "decr" // 减少积分

	DataCenterUserDepositTradingAppIdDev = int64(1925159417674264576) // 获取用户任务结束后的入金和交易量的appId dev环境
	DataCenterUserDepositTradingAppIdPro = int64(1930242833009422336) // 获取用户任务结束后的入金和交易量的appId pro环境

	TaskCenterTypeRegular  = 1
	TaskCenterTypeCycle    = 2
	TaskCenterTypeRepeat   = 3
	TaskCenterTypeMultiple = 4

	// 进阶任务是否领取过奖励常量定义
	FinishAdvancedReceiveYes = 1
	FinishAdvancedReceiveNo  = 2

	WelfareUserShowRecordYes = 1 //用户任务是否展示 1:展示
	WelfareUserShowRecordNo  = 2 //用户任务是否展示 1:展示

)
