package consts

const (
	// 如果更换签到卡券id，一定要查看是否类型有更换
	CheckInCouponType = PointsShopTypeFuturesCommissionRebate
)

// 线上coupon_id配置
const (
	// 签到coupon_id
	CheckInCouponId = 2162
)

// 测试环境coupon_id配置
const (
	// 签到coupon_id-test
	CheckInCouponIdTest = 1548
)

// TaskCouponIdTestMap coupon_id生产环境和测试环境的映射关系
var TaskCouponIdTestMap = map[int]int{
	CheckInCouponId: CheckInCouponIdTest,
}

// 获取真实的coupon_id
func GetRealCouponId(prodCouponId int) int {
	if testCouponId, exists := TaskCouponIdTestMap[prodCouponId]; exists {
		return testCouponId
	}
	return prodCouponId
}
