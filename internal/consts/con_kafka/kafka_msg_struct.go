package con_kafka

// 对应任务系统的业务枚举值
const ContractTransformTaskBusinessType = 11

// 用户注册消息
type UserRegister struct {
	UID                int64  `json:"uid"`                  //用户id
	Timest             string `json:"timest"`               //注册时间错
	IpReg              string `json:"ip_reg"`               //注册IP
	RefUid             int64  `json:"ref_uid"`              //邀请人ID
	RegClient          string `json:"reg_client"`           //注册的客户端
	SubWebsiteID       int64  `json:"sub_website_id"`       //分站ID
	RegisterCountryID  int64  `json:"register_country_id"`  //注册所属国家ID
	ResidenceCountryID int64  `json:"residence_country_id"` //居住所属国家ID
}

// TaskRecordReceiveMsg 给任务系统上报任务的消息体
type TaskRecordReceiveMsg struct {
	UserId       int64 `json:"user_id"`
	TaskId       int64 `json:"task_id"`
	ReceiveTime  int64 `json:"receive_time"`
	BusinessType int64 `json:"business_type"`
	BusinessId   int64 `json:"business_id"`
	IsDone       int   `json:"is_done"`
	IsAck        int   `json:"is_ack"`
	StartTime    int64 `json:"start_time"`
	EndTime      int64 `json:"end_time"`
}

type TaskCallBackMsg struct {
	BusinessType int                `json:"business_type"`
	CallType     string             `json:"call_type"`
	List         []TaskCallBackItem `json:"list"`
}

type TaskCallBackItem struct {
	BusinessType int         `json:"business_type"`
	BusinessID   string      `json:"business_id"`
	UserID       int64       `json:"user_id"`
	TaskID       int         `json:"task_id"`
	OnlyID       string      `json:"only_id"`
	Status       int         `json:"status"`
	DoneTime     int64       `json:"done_time"`
	Conditions   []Condition `json:"conditions"`
	ReceivedType int         `json:"received_type"`
}

type Condition struct {
	PrizeType int         `json:"prize_type"`
	PrizeNum  int         `json:"prize_num"`
	Status    int         `json:"status"`
	PrizeExt  interface{} `json:"prize_ext"` // 如果 prize_ext 是空数组或内容不定，可以用 interface{}
}

type TaskPrizeSendMsg struct {
	UserID       int    `json:"user_id"`
	TaskID       int    `json:"task_id"`
	ReceiveTime  int64  `json:"receive_time"` // 假设是时间戳
	BusinessType int64  `json:"business_type"`
	BusinessID   string `json:"business_id"`
}
