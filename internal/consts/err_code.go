package consts

import (
	"fmt"
	"net/http"

	"bitbucket.org/gatebackend/go-zero/rest/errors"
	"bitbucket.org/gateio/gateio-lib-common-go/language"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
)

// 错误码规范 https://gtglobal.jp.larksuite.com/wiki/IiNjwntThibmIYkdwFTjghuCp1b
// https://gtglobal.jp.larksuite.com/wiki/UnLDwmmK1iADUlk4Ddbjhp45pVb

// 基础错误码
const (
	Success      = 0
	TgVisitor    = 1403 // tg小程序:游客提示去绑定
	ErrUserLogin = 2002 // 请先登录

	ErrSystemError             = 51500000 //系统错误
	ErrInvalidParam            = 51500001 //参数错误
	ErrDbError                 = 51500002 //数据库错误
	ErrRedisError              = 51500003 //redis错误
	ErrApiError                = 51500004 //第三方接口错误
	ManyAttempts               = 51500005 //请求过快
	ErrSuspendedForMaintenance = 51500006 //活动维护中
)

// 通用业务错误码
const (
	AttendedToday                             = 51501000 //今日已签到
	ErrStatusWrong                            = 51501001 //状态异常
	ErrUnmarshalFailed                        = 51501002 //json解析异常
	ErrPermission                             = 51501003 //无权限
	ErrRecordNotExist                         = 51501004 //记录不存在
	ErrWelfareExchangePrizeSuccess            = 51501005 //兑换成功
	ErrWelfareExchangePrizeFailed             = 51501006 //兑换失败
	ErrWelfareExchangePrizeInsufficientPoints = 51501007 //积分不足
	ErrWelfareExchangePrizeLimit              = 51501008 //兑换次数达到上限
	ErrWelfareInvalidActivity                 = 51501009 //无效活动
	ErrReveiveTaskNotInConditionMsg           = 51501010 //领取任务失败，请确认符合参与条件后重试。
	ErrReveiveTaskRiskNoticeReject            = 51501011 //领取任务失败，您的账号可能存在异常，请联系客服处理。
	ErrRiskNoticeReject                       = 51501012 //由于平台风控，您的操作失败。如有疑问可联系客服。
	ErrMustKyc2                               = 51501013 //为了保护您的账户安全，请先完成身份认证。
	ErrCheckInFailed                          = 51501014 // 签到失败，请稍后重试！
	ErrUnableToRedeem                         = 51501015 //暂无法兑换，请稍后尝试
	ErrThisRewardIsLimitedInNumber            = 51501016 //该奖品已兑完，数量有限，明日请尽早
	ErrExchangeKycUserOnce                    = 51501017 //兑换失败，每位实名认证用户仅可兑换该奖品一次。
	ErrCouponSystemError                      = ******** //卡券中心系统异常
	ErrPrizeExpired                           = ******** //奖品已过期，请兑换其他奖励！
	ErrExchangeRiskNoticeReject               = ******** //兑换失败，您的账号可能存在异常，请联系客服处理。
	ErrIdentityAgentPartner                   = ******** //经检测，您的账户是合伙人或超级代理商账号，无法参与任务
	ErrIdentitySubAccount                     = ******** //经检测，您的账户是子账号，无法参与任务
	ErrIdentityCompany                        = ******** //经检测，您的账号不符合要求，无法参与任务
	ErrUserKycRepeat                          = ******** //Kyc重复
)

// 报错码code => 翻译库(smartling_php)key
var ErrorCodeKeyMap = map[int]string{
	Success:                        "Operation success",
	ErrUserLogin:                   "Please login first",
	ErrSuspendedForMaintenance:     "Suspended for maintenance",
	ManyAttempts:                   "manyAttempts",
	ErrInvalidParam:                "invalid parameter",
	ErrSystemError:                 "Refreshbalance_Fail",
	ErrWelfareExchangePrizeSuccess: "welfare_exchange_prize_success",
	ErrWelfareExchangePrizeFailed:  "welfare_exchange_prize_failed",
	ErrWelfareExchangePrizeInsufficientPoints: "welfare_exchange_prize_insufficient_points",
	ErrWelfareExchangePrizeLimit:              "welfare_exchange_prize_limit",
	ErrWelfareInvalidActivity:                 "Invalid activity",
	ErrReveiveTaskNotInConditionMsg:           "receive_task_not_in_condition",
	ErrReveiveTaskRiskNoticeReject:            "receive_task_risk_notice_reject",
	ErrRiskNoticeReject:                       "welfare_risk_notice_reject",
	ErrMustKyc2:                               "MustKYC2",
	ErrCheckInFailed:                          "check_in_failed",
	AttendedToday:                             "Attended Today",
	ErrThisRewardIsLimitedInNumber:            "This reward is limited in number",
	ErrExchangeKycUserOnce:                    "exchange_kyc_user_once",
	ErrPrizeExpired:                           "prize_expired",
	ErrExchangeRiskNoticeReject:               "exchange_risk_notice_reject",
	ErrIdentityAgentPartner:                   "welfare_identity_agent_partner",
	ErrIdentitySubAccount:                     "welfare_identity_sub_account",
	ErrIdentityCompany:                        "welfare_identity_company",
	ErrUserKycRepeat:                          "welfare_user_kyc_repeat",
}

func GetErrorMsgWithLabel(r *http.Request, code int, label string) error {
	opts := []errors.Option{
		errors.WithLabel(label),
	}
	value, exists := ErrorCodeKeyMap[code]
	if exists {
		return errors.New(code, language.GetLangDescByKey(value, requestools.GetUserLanguage(r)), opts...)
	} else {
		return errors.New(code, language.GetLangDescByKey(ErrorCodeKeyMap[ErrSystemError], requestools.GetUserLanguage(r)), opts...)
	}
}

func GetErrorMsg(r *http.Request, code int) error {
	return GetErrorMsgWithLabel(r, code, "")
}

/**
 * 格式化error
 * 返回label字段，用于输出真实错误和请求ID，便于排查问题
 */
func ErrFormat(r *http.Request, err error, reqId string) error {
	if err == nil {
		return nil
	}
	if cm, ok := err.(*errors.CodeMsg); ok {
		return GetErrorMsgWithLabel(r, cm.Code, fmt.Sprintf("%v order_id: %v", err, reqId))
	}
	return err
}
