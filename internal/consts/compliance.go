package consts

const (
	TypeFutures       = "futures"         // 合约
	TypeQuantitative  = "quantitative"    // 量化
	TypeLendEarn      = "lend_earn"       // 余币宝
	TypeC2c           = "c2c"             // c2c
	TypeNewCoinMining = "new_coin_mining" // lanchpool
	TypeDeposits      = "deposits"        //入金
	TypeSpots         = "spots"           //现货
	TypeAlpha         = "alpha"           //alpha
	TypeLever         = "lever"           //逐仓杠杆
	TypeInvite        = "invite"          //邀请好友
	TypeCopyTrading   = "copy_trading"    //跟单
)

// MailComplianceConfig defines the mail and compliance configuration structure
type ComplianceConfig struct {
	Menus       map[int][]int
	BusinessKey []string
	MenuKeys    []string
}

// GetComplianceConfig returns the compliance configuration for different business types
func GetComplianceConfig() map[string]ComplianceConfig {
	return map[string]ComplianceConfig{
		// 合约
		TypeFutures: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {UserTaskTaskTypeFutures},
				//限时任务
				UserTaskTypeVeteranLimited: {UserTaskTaskTypeFutures},
				// 积分商城
				UserTaskTypePointsExchange: {PointsShopTypeFuturesCommissionRebate, PointsShopTypeFutures, PointsShopTypeContractBonusNew},
			},
			BusinessKey: []string{"api_futures_order"}, // 主页 行情-合约按钮
			MenuKeys:    []string{},
		},
		// 量化
		TypeQuantitative: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {UserTaskTaskTypeQuantitative},
				//限时任务
				UserTaskTypeVeteranLimited: {UserTaskTaskTypeQuantitative},
				// 积分商城
				UserTaskTypePointsExchange: {PointsShopTypeQuantitative, PointsShopTypeQuantitativeNew},
			},
			BusinessKey: []string{},
			MenuKeys:    []string{"bots"},
		},
		// 余币宝
		TypeLendEarn: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {UserTaskTaskTypeLendEarn},
				//限时任务
				UserTaskTypeVeteranLimited: {UserTaskTaskTypeLendEarn},
				// 积分商城
				UserTaskTypePointsExchange: {PointsShopTypeFinance},
			},
			BusinessKey: []string{},
			MenuKeys:    []string{"lend_earn"},
		},
		// c2c
		TypeC2c: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {UserTaskTaskTypeC2c},
				//限时任务
				UserTaskTypeVeteranLimited: {UserTaskTaskTypeC2c},
				// 积分商城
				UserTaskTypePointsExchange: {},
			},
			BusinessKey: []string{"api_c2c_order"},
			MenuKeys:    []string{},
		},
		//lanchpool
		TypeNewCoinMining: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {UserTaskTaskTypeLanchPool},
				//限时任务
				UserTaskTypeVeteranLimited: {UserTaskTaskTypeLanchPool},
				// 积分商城
				UserTaskTypePointsExchange: {},
			},
			BusinessKey: []string{},
			MenuKeys:    []string{"launchpool"},
		},
		//入金
		TypeDeposits: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {UserTaskTaskTypeDeposits, UserTaskTaskTypeNetDeposits},
				//限时任务
				UserTaskTypeVeteranLimited: {UserTaskTaskTypeDeposits, UserTaskTaskTypeNetDeposits},
				// 积分商城
				UserTaskTypePointsExchange: {},
			},
			BusinessKey: []string{"api_buy_crypto_order"},
			MenuKeys:    []string{},
		},
		//现货
		TypeSpots: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {UserTaskTaskTypeSpots},
				//限时任务
				UserTaskTypeVeteranLimited: {UserTaskTaskTypeSpots},
				// 积分商城
				UserTaskTypePointsExchange: {PointsShopTypeSpotCommissionRebate},
			},
			BusinessKey: []string{"api_tradeIndex_order"},
			MenuKeys:    []string{"spot_trade"},
		},
		//alpha
		TypeAlpha: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {UserTaskTaskTypeAlpha},
				//限时任务
				UserTaskTypeVeteranLimited: {UserTaskTaskTypeAlpha},
				// 积分商城
				UserTaskTypePointsExchange: {},
			},
			BusinessKey: []string{},
			MenuKeys:    []string{"alpha"},
		},
		//逐仓杠杆
		TypeLever: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {UserTaskTaskTypeLever},
				//限时任务
				UserTaskTypeVeteranLimited: {UserTaskTaskTypeLever},
				// 积分商城
				UserTaskTypePointsExchange: {},
			},
			BusinessKey: []string{},
			MenuKeys:    []string{"margin_trade"},
		},
		//邀请
		TypeInvite: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {UserTaskTaskTypeInvite},
				//限时任务
				UserTaskTypeVeteranLimited: {UserTaskTaskTypeInvite},
				// 积分商城
				UserTaskTypePointsExchange: {},
			},
			BusinessKey: []string{},
			MenuKeys:    []string{"invit_plan"},
		},
		// 跟单
		TypeCopyTrading: {
			Menus: map[int][]int{
				//日常任务
				UserTaskTypeVeteranDaily: {},
				//限时任务
				UserTaskTypeVeteranLimited: {},
				// 积分商城
				UserTaskTypePointsExchange: {PointsShopTypeLossProtectionCopier},
			},
			BusinessKey: []string{},
			MenuKeys:    []string{"copy_trading"},
		},
	}
}
