package welfare_config

import (
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"database/sql"
	"errors"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                    = gormcsql.InitField
	_ WelfareConfigModel = (*customWelfareConfigModel)(nil)
)

type (
	// WelfareConfigModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfareConfigModel.
	WelfareConfigModel interface {
		welfareConfigModel
		customWelfareConfigLogicModel
		GetWelfareConfigByName(ctx context.Context, name string) (*WelfareConfig, error)
	}

	customWelfareConfigLogicModel interface {
		WithSession(tx *gorm.DB) WelfareConfigModel
	}

	customWelfareConfigModel struct {
		*defaultWelfareConfigModel
	}
)

func (c customWelfareConfigModel) WithSession(tx *gorm.DB) WelfareConfigModel {
	newModel := *c.defaultWelfareConfigModel
	c.defaultWelfareConfigModel = &newModel
	c.conn = tx
	return c
}

// NewWelfareConfigModel returns a model for the database table.
func NewWelfareConfigModel(conn *gorm.DB) WelfareConfigModel {
	return &customWelfareConfigModel{
		defaultWelfareConfigModel: newWelfareConfigModel(conn),
	}
}

func (m *defaultWelfareConfigModel) GetWelfareConfigByName(ctx context.Context, name string) (*WelfareConfig, error) {
	var resp WelfareConfig
	err := m.conn.WithContext(ctx).Model(&WelfareConfig{}).Where("`name` = @name", sql.Named("name", name)).Take(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return &resp, nil
}
