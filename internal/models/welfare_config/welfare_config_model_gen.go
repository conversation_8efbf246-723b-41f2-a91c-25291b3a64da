// Code generated by goctl. DO NOT EDIT.

package welfare_config

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfareConfigModel interface {
		Insert(ctx context.Context, data *WelfareConfig) error

		FindOne(ctx context.Context, id int64) (*WelfareConfig, error)
		Update(ctx context.Context, data *WelfareConfig) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfareConfigModel struct {
		conn  *gorm.DB
		table string
	}

	WelfareConfig struct {
		Id        int64     `gorm:"column:id"`
		Name      string    `gorm:"column:name"`   // 配置名称
		Config    string    `gorm:"column:config"` // 配置
		CreatedAt time.Time `gorm:"column:created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at"`
	}
)

var QWelfareConfig WelfareConfig

func init() {
	gormcsql.InitField(&QWelfareConfig)
}

func (WelfareConfig) TableName() string {
	return "`welfare_config`"
}

func newWelfareConfigModel(conn *gorm.DB) *defaultWelfareConfigModel {
	return &defaultWelfareConfigModel{
		conn:  conn,
		table: "`welfare_config`",
	}
}

func (m *defaultWelfareConfigModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfareConfig{}, id).Error

	return err
}

func (m *defaultWelfareConfigModel) FindOne(ctx context.Context, id int64) (*WelfareConfig, error) {
	var resp WelfareConfig
	err := m.conn.WithContext(ctx).Model(&WelfareConfig{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfareConfigModel) Insert(ctx context.Context, data *WelfareConfig) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfareConfigModel) Update(ctx context.Context, data *WelfareConfig) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
