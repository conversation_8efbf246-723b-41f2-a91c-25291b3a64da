// Code generated by goctl. DO NOT EDIT.

package welfare_check_in_logs

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfareCheckInLogsModel interface {
		Insert(ctx context.Context, data *WelfareCheckInLogs) error

		FindOne(ctx context.Context, id int64) (*WelfareCheckInLogs, error)
		Update(ctx context.Context, data *WelfareCheckInLogs) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfareCheckInLogsModel struct {
		conn  *gorm.DB
		table string
	}

	WelfareCheckInLogs struct {
		Id             int64     `gorm:"column:id"`
		CheckInTaskId  int64     `gorm:"column:check_in_task_id"`  // 签到任务ID
		Uid            int64     `gorm:"column:uid"`               // uid
		Day            int64     `gorm:"column:day"`               // Ymd格式
		IsCycleLastDay int64     `gorm:"column:is_cycle_last_day"` // 是否是周期的最后一天 1:是
		PrizeId        int64     `gorm:"column:prize_id"`          // 奖品id
		PrizeType      int64     `gorm:"column:prize_type"`        // 奖品类型 1积分 2卡劵
		PrizeTypeNum   int64     `gorm:"column:prize_type_num"`    // 奖品数量
		Status         int64     `gorm:"column:status"`            // 状态 1:成功; 2:失败 3:发奖中
		CreatedAt      time.Time `gorm:"column:created_at"`
		UpdatedAt      time.Time `gorm:"column:updated_at"`
	}
)

var QWelfareCheckInLogs WelfareCheckInLogs

func init() {
	gormcsql.InitField(&QWelfareCheckInLogs)
}

func (WelfareCheckInLogs) TableName() string {
	return "`welfare_check_in_logs`"
}

func newWelfareCheckInLogsModel(conn *gorm.DB) *defaultWelfareCheckInLogsModel {
	return &defaultWelfareCheckInLogsModel{
		conn:  conn,
		table: "`welfare_check_in_logs`",
	}
}

func (m *defaultWelfareCheckInLogsModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfareCheckInLogs{}, id).Error

	return err
}

func (m *defaultWelfareCheckInLogsModel) FindOne(ctx context.Context, id int64) (*WelfareCheckInLogs, error) {
	var resp WelfareCheckInLogs
	err := m.conn.WithContext(ctx).Model(&WelfareCheckInLogs{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfareCheckInLogsModel) Insert(ctx context.Context, data *WelfareCheckInLogs) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfareCheckInLogsModel) Update(ctx context.Context, data *WelfareCheckInLogs) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
