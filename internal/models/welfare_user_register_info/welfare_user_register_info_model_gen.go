// Code generated by goctl. DO NOT EDIT.

package welfare_user_register_info

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfareUserRegisterInfoModel interface {
		Insert(ctx context.Context, data *WelfareUserRegisterInfo) error

		FindOne(ctx context.Context, id int64) (*WelfareUserRegisterInfo, error)
		Update(ctx context.Context, data *WelfareUserRegisterInfo) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfareUserRegisterInfoModel struct {
		conn  *gorm.DB
		table string
	}

	WelfareUserRegisterInfo struct {
		Id                    int64     `gorm:"column:id"`                      // 自增ID
		Uid                   int64     `gorm:"column:uid"`                     // 用户uid
		RegisterCountryId     int64     `gorm:"column:register_country_id"`     // 注册国家
		ResidenceCountryId    int64     `gorm:"column:residence_country_id"`    // 注册IP国家
		CreatedAt             time.Time `gorm:"column:created_at"`              // 创建时间
		UpdatedAt             time.Time `gorm:"column:updated_at"`              // 更新时间
		Region                string    `gorm:"column:region"`                  // 地区
		IsVisitNewbie         int64     `gorm:"column:is_visit_newbie"`         // 是否访问过新客福利中心（访问新客接口）1是0否
		IsPopUp               int64     `gorm:"column:is_pop_up"`               // 是否弹过弹窗 1是0否
		NewbieEndTime         int64     `gorm:"column:newbie_end_time"`         // 新客期结束时间戳
		AdvancedEndTime       int64     `gorm:"column:advanced_end_time"`       // 进阶任务结算时间戳 用于n+3天发奖
		FinishAdvancedReceive int64     `gorm:"column:finish_advanced_receive"` // 是否完成进阶任务发奖 1:是 2:否
		IsBlack               int64     `gorm:"column:is_black"`                // 是否是黑敏感 1是 2否
	}
)

var QWelfareUserRegisterInfo WelfareUserRegisterInfo

func init() {
	gormcsql.InitField(&QWelfareUserRegisterInfo)
}

func (WelfareUserRegisterInfo) TableName() string {
	return "`welfare_user_register_info`"
}

func newWelfareUserRegisterInfoModel(conn *gorm.DB) *defaultWelfareUserRegisterInfoModel {
	return &defaultWelfareUserRegisterInfoModel{
		conn:  conn,
		table: "`welfare_user_register_info`",
	}
}

func (m *defaultWelfareUserRegisterInfoModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfareUserRegisterInfo{}, id).Error

	return err
}

func (m *defaultWelfareUserRegisterInfoModel) FindOne(ctx context.Context, id int64) (*WelfareUserRegisterInfo, error) {
	var resp WelfareUserRegisterInfo
	err := m.conn.WithContext(ctx).Model(&WelfareUserRegisterInfo{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfareUserRegisterInfoModel) Insert(ctx context.Context, data *WelfareUserRegisterInfo) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfareUserRegisterInfoModel) Update(ctx context.Context, data *WelfareUserRegisterInfo) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
