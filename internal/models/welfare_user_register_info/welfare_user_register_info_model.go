package welfare_user_register_info

import (
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"database/sql"
	"errors"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                              = gormcsql.InitField
	_ WelfareUserRegisterInfoModel = (*customWelfareUserRegisterInfoModel)(nil)
)

type (
	// WelfareUserRegisterInfoModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfareUserRegisterInfoModel.
	WelfareUserRegisterInfoModel interface {
		welfareUserRegisterInfoModel
		customWelfareUserRegisterInfoLogicModel
		GetUserRegisterInfo(ctx context.Context, uid int64) (*WelfareUserRegisterInfo, error)
	}

	customWelfareUserRegisterInfoLogicModel interface {
		WithSession(tx *gorm.DB) WelfareUserRegisterInfoModel
	}

	customWelfareUserRegisterInfoModel struct {
		*defaultWelfareUserRegisterInfoModel
	}
)

func (c customWelfareUserRegisterInfoModel) WithSession(tx *gorm.DB) WelfareUserRegisterInfoModel {
	newModel := *c.defaultWelfareUserRegisterInfoModel
	c.defaultWelfareUserRegisterInfoModel = &newModel
	c.conn = tx
	return c
}

// NewWelfareUserRegisterInfoModel returns a model for the database table.
func NewWelfareUserRegisterInfoModel(conn *gorm.DB) WelfareUserRegisterInfoModel {
	return &customWelfareUserRegisterInfoModel{
		defaultWelfareUserRegisterInfoModel: newWelfareUserRegisterInfoModel(conn),
	}
}

func (m *defaultWelfareUserRegisterInfoModel) GetUserRegisterInfo(ctx context.Context, uid int64) (*WelfareUserRegisterInfo, error) {
	var resp WelfareUserRegisterInfo
	err := m.conn.WithContext(ctx).Model(&WelfareUserRegisterInfo{}).Where("`uid` = @uid", sql.Named("uid", uid)).Take(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return &resp, nil

}
