package welfare_user_tasks

import (
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"errors"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                       = gormcsql.InitField
	_ WelfareUserTasksModel = (*customWelfareUserTasksModel)(nil)
)

type (
	// WelfareUserTasksModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfareUserTasksModel.
	WelfareUserTasksModel interface {
		welfareUserTasksModel
		customWelfareUserTasksLogicModel
		GetUserTask(ctx context.Context, uid, baseType, taskType int64, tid int64) (*WelfareUserTasks, error)
	}

	customWelfareUserTasksLogicModel interface {
		WithSession(tx *gorm.DB) WelfareUserTasksModel
	}

	customWelfareUserTasksModel struct {
		*defaultWelfareUserTasksModel
	}
)

func (c customWelfareUserTasksModel) WithSession(tx *gorm.DB) WelfareUserTasksModel {
	newModel := *c.defaultWelfareUserTasksModel
	c.defaultWelfareUserTasksModel = &newModel
	c.conn = tx
	return c
}

// NewWelfareUserTasksModel returns a model for the database table.
func NewWelfareUserTasksModel(conn *gorm.DB) WelfareUserTasksModel {
	return &customWelfareUserTasksModel{
		defaultWelfareUserTasksModel: newWelfareUserTasksModel(conn),
	}
}

func (m *defaultWelfareUserTasksModel) GetUserTask(ctx context.Context, uid, baseType, taskType int64, tid int64) (*WelfareUserTasks, error) {
	var resp WelfareUserTasks
	query := m.conn.WithContext(ctx).Model(WelfareUserTasks{})
	// 根据条件动态构建查询
	if uid > 0 {
		query = query.Where("`uid` = ?", uid)
	}
	if baseType > 0 {
		query = query.Where("`type` = ?", baseType)
	}
	if taskType > 0 {
		query = query.Where("`task_type` = ?", taskType)
	}
	if tid > 0 {
		query = query.Where("`task_id` = ?", tid)
	}
	err := query.First(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return &resp, nil

}
