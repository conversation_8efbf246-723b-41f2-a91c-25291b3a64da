// Code generated by goctl. DO NOT EDIT.

package welfare_user_tasks

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfareUserTasksModel interface {
		Insert(ctx context.Context, data *WelfareUserTasks) error

		FindOne(ctx context.Context, id int64) (*WelfareUserTasks, error)
		Update(ctx context.Context, data *WelfareUserTasks) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfareUserTasksModel struct {
		conn  *gorm.DB
		table string
	}

	WelfareUserTasks struct {
		Id             int64           `gorm:"column:id"`
		Uid            int64           `gorm:"column:uid"`           // uid
		Type           int64           `gorm:"column:type"`          // 类型 1新手任务 2福利任务 3邀请任务 4新人任务
		TaskId         int64           `gorm:"column:task_id"`       // 任务id
		TaskType       int64           `gorm:"column:task_type"`     // 任务类型：1kyc2认证 2现货 3合约 4邀请 5量化 6余币宝 7startup 8首次入金
		TaskProgress   decimal.Decimal `gorm:"column:task_progress"` // 任务进度：U
		Status         int64           `gorm:"column:status"`        // 状态 0未完成 1进行中 2已完成 3结算中 4已结算 5过期
		Points         int64           `gorm:"column:points"`        // 积分
		Memo           string          `gorm:"column:memo"`          // 备注
		CreatedAt      time.Time       `gorm:"column:created_at"`
		UpdatedAt      time.Time       `gorm:"column:updated_at"`
		Stage          int64           `gorm:"column:stage"`            // 阶段默认0
		FinishTaskTime int64           `gorm:"column:finish_task_time"` // 完成任务时间戳 秒
		TaskCenterId   int64           `gorm:"column:task_center_id"`   // 任务中心任务id
		RecordShow     int64           `gorm:"column:record_show"`      // 字段是否显示 1:显示 2:不显示
	}
)

var QWelfareUserTasks WelfareUserTasks

func init() {
	gormcsql.InitField(&QWelfareUserTasks)
}

func (WelfareUserTasks) TableName() string {
	return "`welfare_user_tasks`"
}

func newWelfareUserTasksModel(conn *gorm.DB) *defaultWelfareUserTasksModel {
	return &defaultWelfareUserTasksModel{
		conn:  conn,
		table: "`welfare_user_tasks`",
	}
}

func (m *defaultWelfareUserTasksModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfareUserTasks{}, id).Error

	return err
}

func (m *defaultWelfareUserTasksModel) FindOne(ctx context.Context, id int64) (*WelfareUserTasks, error) {
	var resp WelfareUserTasks
	err := m.conn.WithContext(ctx).Model(&WelfareUserTasks{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfareUserTasksModel) Insert(ctx context.Context, data *WelfareUserTasks) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfareUserTasksModel) Update(ctx context.Context, data *WelfareUserTasks) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
