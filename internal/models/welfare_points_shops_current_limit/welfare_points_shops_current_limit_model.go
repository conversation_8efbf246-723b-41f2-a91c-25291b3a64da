package welfare_points_shops_current_limit

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                                     = gormcsql.InitField
	_ WelfarePointsShopsCurrentLimitModel = (*customWelfarePointsShopsCurrentLimitModel)(nil)
)

type (
	// WelfarePointsShopsCurrentLimitModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfarePointsShopsCurrentLimitModel.
	WelfarePointsShopsCurrentLimitModel interface {
		welfarePointsShopsCurrentLimitModel
		customWelfarePointsShopsCurrentLimitLogicModel
	}

	customWelfarePointsShopsCurrentLimitLogicModel interface {
		WithSession(tx *gorm.DB) WelfarePointsShopsCurrentLimitModel
	}

	customWelfarePointsShopsCurrentLimitModel struct {
		*defaultWelfarePointsShopsCurrentLimitModel
	}
)

func (c customWelfarePointsShopsCurrentLimitModel) WithSession(tx *gorm.DB) WelfarePointsShopsCurrentLimitModel {
	newModel := *c.defaultWelfarePointsShopsCurrentLimitModel
	c.defaultWelfarePointsShopsCurrentLimitModel = &newModel
	c.conn = tx
	return c
}

// NewWelfarePointsShopsCurrentLimitModel returns a model for the database table.
func NewWelfarePointsShopsCurrentLimitModel(conn *gorm.DB) WelfarePointsShopsCurrentLimitModel {
	return &customWelfarePointsShopsCurrentLimitModel{
		defaultWelfarePointsShopsCurrentLimitModel: newWelfarePointsShopsCurrentLimitModel(conn),
	}
}
