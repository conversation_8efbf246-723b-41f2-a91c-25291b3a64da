// Code generated by goctl. DO NOT EDIT.

package welfare_points_shops_current_limit

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfarePointsShopsCurrentLimitModel interface {
		Insert(ctx context.Context, data *WelfarePointsShopsCurrentLimit) error

		FindOne(ctx context.Context, id int64) (*WelfarePointsShopsCurrentLimit, error)
		Update(ctx context.Context, data *WelfarePointsShopsCurrentLimit) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfarePointsShopsCurrentLimitModel struct {
		conn  *gorm.DB
		table string
	}

	WelfarePointsShopsCurrentLimit struct {
		Id        int64     `gorm:"column:id"`
		PrizeId   int64     `gorm:"column:prize_id"` // 奖品id
		Type      int64     `gorm:"column:type"`     // 类型 1点卡 2vip1 3合约体验金 4startup券 5量化体验金 6vip+1
		TypeNum   int64     `gorm:"column:type_num"` // type数量
		Num       int64     `gorm:"column:num"`      // 奖品数量/份额
		CreatedAt time.Time `gorm:"column:created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at"`
	}
)

var QWelfarePointsShopsCurrentLimit WelfarePointsShopsCurrentLimit

func init() {
	gormcsql.InitField(&QWelfarePointsShopsCurrentLimit)
}

func (WelfarePointsShopsCurrentLimit) TableName() string {
	return "`welfare_points_shops_current_limit`"
}

func newWelfarePointsShopsCurrentLimitModel(conn *gorm.DB) *defaultWelfarePointsShopsCurrentLimitModel {
	return &defaultWelfarePointsShopsCurrentLimitModel{
		conn:  conn,
		table: "`welfare_points_shops_current_limit`",
	}
}

func (m *defaultWelfarePointsShopsCurrentLimitModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfarePointsShopsCurrentLimit{}, id).Error

	return err
}

func (m *defaultWelfarePointsShopsCurrentLimitModel) FindOne(ctx context.Context, id int64) (*WelfarePointsShopsCurrentLimit, error) {
	var resp WelfarePointsShopsCurrentLimit
	err := m.conn.WithContext(ctx).Model(&WelfarePointsShopsCurrentLimit{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfarePointsShopsCurrentLimitModel) Insert(ctx context.Context, data *WelfarePointsShopsCurrentLimit) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfarePointsShopsCurrentLimitModel) Update(ctx context.Context, data *WelfarePointsShopsCurrentLimit) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
