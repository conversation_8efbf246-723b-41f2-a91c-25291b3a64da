// Code generated by goctl. DO NOT EDIT.

package welfare_user_points

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfareUserPointsNewModel interface {
		Insert(ctx context.Context, data *WelfareUserPointsNew) error

		FindOne(ctx context.Context, id int64) (*WelfareUserPointsNew, error)
		FindOneByUid(ctx context.Context, uid int64) (*WelfareUserPointsNew, error)
		Update(ctx context.Context, data *WelfareUserPointsNew) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfareUserPointsNewModel struct {
		conn  *gorm.DB
		table string
	}

	WelfareUserPointsNew struct {
		Id             int64     `gorm:"column:id"`
		Uid            int64     `gorm:"column:uid"`              // uid
		CleanPointTime int64     `gorm:"column:clean_point_time"` // 积分上次清0时间戳 秒
		Points         int64     `gorm:"column:points"`
		CreatedAt      time.Time `gorm:"column:created_at"`
		UpdatedAt      time.Time `gorm:"column:updated_at"`
	}
)

var QWelfareUserPointsNew WelfareUserPointsNew

func init() {
	gormcsql.InitField(&QWelfareUserPointsNew)
}

func (WelfareUserPointsNew) TableName() string {
	return "`welfare_user_points_new`"
}

func newWelfareUserPointsNewModel(conn *gorm.DB) *defaultWelfareUserPointsNewModel {
	return &defaultWelfareUserPointsNewModel{
		conn:  conn,
		table: "`welfare_user_points_new`",
	}
}

func (m *defaultWelfareUserPointsNewModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfareUserPointsNew{}, id).Error

	return err
}

func (m *defaultWelfareUserPointsNewModel) FindOne(ctx context.Context, id int64) (*WelfareUserPointsNew, error) {
	var resp WelfareUserPointsNew
	err := m.conn.WithContext(ctx).Model(&WelfareUserPointsNew{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfareUserPointsNewModel) FindOneByUid(ctx context.Context, uid int64) (*WelfareUserPointsNew, error) {
	var resp WelfareUserPointsNew
	err := m.conn.WithContext(ctx).Model(&WelfareUserPointsNew{}).Where("`uid` = ?", uid).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultWelfareUserPointsNewModel) Insert(ctx context.Context, data *WelfareUserPointsNew) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfareUserPointsNewModel) Update(ctx context.Context, data *WelfareUserPointsNew) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
