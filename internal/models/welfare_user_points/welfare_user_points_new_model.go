package welfare_user_points

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                           = gormcsql.InitField
	_ WelfareUserPointsNewModel = (*customWelfareUserPointsNewModel)(nil)
)

type (
	// WelfareUserPointsNewModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfareUserPointsNewModel.
	WelfareUserPointsNewModel interface {
		welfareUserPointsNewModel
		customWelfareUserPointsNewLogicModel
	}

	customWelfareUserPointsNewLogicModel interface {
		WithSession(tx *gorm.DB) WelfareUserPointsNewModel
	}

	customWelfareUserPointsNewModel struct {
		*defaultWelfareUserPointsNewModel
	}
)

func (c customWelfareUserPointsNewModel) WithSession(tx *gorm.DB) WelfareUserPointsNewModel {
	newModel := *c.defaultWelfareUserPointsNewModel
	c.defaultWelfareUserPointsNewModel = &newModel
	c.conn = tx
	return c
}

// NewWelfareUserPointsNewModel returns a model for the database table.
func NewWelfareUserPointsNewModel(conn *gorm.DB) WelfareUserPointsNewModel {
	return &customWelfareUserPointsNewModel{
		defaultWelfareUserPointsNewModel: newWelfareUserPointsNewModel(conn),
	}
}
