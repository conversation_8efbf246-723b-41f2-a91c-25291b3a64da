// Code generated by goctl. DO NOT EDIT.

package welfare_points_shop

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfarePointsShopModel interface {
		Insert(ctx context.Context, data *WelfarePointsShop) error

		FindOne(ctx context.Context, id int64) (*WelfarePointsShop, error)
		Update(ctx context.Context, data *WelfarePointsShop) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfarePointsShopModel struct {
		conn  *gorm.DB
		table string
	}

	WelfarePointsShop struct {
		Id             int64     `gorm:"column:id"`
		PrizeId        int64     `gorm:"column:prize_id"`        // 商品ID
		PrizeName      string    `gorm:"column:prize_name"`      // 商品（卡券）名称多语言
		PrizeType      int64     `gorm:"column:prize_type"`      // 类型 1卡券
		PrizeSubType   int64     `gorm:"column:prize_sub_type"`  // 类型 1点卡 2vip1 3合约体验金 4startup券 5量化体验金 6vip+1 7理财体验金 8合约体验券 9高息理财
		PrizeValue     string    `gorm:"column:prize_value"`     // 商品价值
		PrizeDesc      string    `gorm:"column:prize_desc"`      // 商品描述多语言
		PrizeUrl       string    `gorm:"column:prize_url"`       // 商品描述地址
		PrizeMaxNum    int64     `gorm:"column:prize_max_num"`   // 奖品库存总额
		ExchangePoints int64     `gorm:"column:exchange_points"` // 积分
		ExchangeCycle  int64     `gorm:"column:exchange_cycle"`  // 兑换周期
		ExchangeNum    int64     `gorm:"column:exchange_num"`    // 兑换次数
		Source         string    `gorm:"column:source"`          // 卡劵下发使用的source
		Status         int64     `gorm:"column:status"`          // 状态：1=上架, 2=下架
		Sort           int64     `gorm:"column:sort"`            // 排序 使用desc
		PreEnv         int64     `gorm:"column:pre_env"`         // 是否是预发环境 1:是
		CreatedAt      time.Time `gorm:"column:created_at"`
		UpdatedAt      time.Time `gorm:"column:updated_at"`
	}
)

var QWelfarePointsShop WelfarePointsShop

func init() {
	gormcsql.InitField(&QWelfarePointsShop)
}

func (WelfarePointsShop) TableName() string {
	return "`welfare_points_shop`"
}

func newWelfarePointsShopModel(conn *gorm.DB) *defaultWelfarePointsShopModel {
	return &defaultWelfarePointsShopModel{
		conn:  conn,
		table: "`welfare_points_shop`",
	}
}

func (m *defaultWelfarePointsShopModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfarePointsShop{}, id).Error

	return err
}

func (m *defaultWelfarePointsShopModel) FindOne(ctx context.Context, id int64) (*WelfarePointsShop, error) {
	var resp WelfarePointsShop
	err := m.conn.WithContext(ctx).Model(&WelfarePointsShop{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfarePointsShopModel) Insert(ctx context.Context, data *WelfarePointsShop) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfarePointsShopModel) Update(ctx context.Context, data *WelfarePointsShop) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
