package welfare_points_shop

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                        = gormcsql.InitField
	_ WelfarePointsShopModel = (*customWelfarePointsShopModel)(nil)
)

type (
	// WelfarePointsShopModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfarePointsShopModel.
	WelfarePointsShopModel interface {
		welfarePointsShopModel
		customWelfarePointsShopLogicModel
	}

	customWelfarePointsShopLogicModel interface {
		WithSession(tx *gorm.DB) WelfarePointsShopModel
	}

	customWelfarePointsShopModel struct {
		*defaultWelfarePointsShopModel
	}
)

func (c customWelfarePointsShopModel) WithSession(tx *gorm.DB) WelfarePointsShopModel {
	newModel := *c.defaultWelfarePointsShopModel
	c.defaultWelfarePointsShopModel = &newModel
	c.conn = tx
	return c
}

// NewWelfarePointsShopModel returns a model for the database table.
func NewWelfarePointsShopModel(conn *gorm.DB) WelfarePointsShopModel {
	return &customWelfarePointsShopModel{
		defaultWelfarePointsShopModel: newWelfarePointsShopModel(conn),
	}
}
