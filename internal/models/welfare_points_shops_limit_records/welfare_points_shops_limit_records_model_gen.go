// Code generated by goctl. DO NOT EDIT.

package welfare_points_shops_limit_records

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfarePointsShopsLimitRecordsModel interface {
		Insert(ctx context.Context, data *WelfarePointsShopsLimitRecords) error

		FindOne(ctx context.Context, id int64) (*WelfarePointsShopsLimitRecords, error)
		Update(ctx context.Context, data *WelfarePointsShopsLimitRecords) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfarePointsShopsLimitRecordsModel struct {
		conn  *gorm.DB
		table string
	}

	WelfarePointsShopsLimitRecords struct {
		Id        int64     `gorm:"column:id"`
		PrizeId   int64     `gorm:"column:prize_id"` // 奖品id
		Type      int64     `gorm:"column:type"`     // 类型 1点卡 2vip1 3合约体验金 4startup券 5量化体验金 6vip+1
		TypeNum   int64     `gorm:"column:type_num"` // type数量
		Num       int64     `gorm:"column:num"`      // 奖品数量/份额
		CreatedAt time.Time `gorm:"column:created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at"`
	}
)

var QWelfarePointsShopsLimitRecords WelfarePointsShopsLimitRecords

func init() {
	gormcsql.InitField(&QWelfarePointsShopsLimitRecords)
}

func (WelfarePointsShopsLimitRecords) TableName() string {
	return "`welfare_points_shops_limit_records`"
}

func newWelfarePointsShopsLimitRecordsModel(conn *gorm.DB) *defaultWelfarePointsShopsLimitRecordsModel {
	return &defaultWelfarePointsShopsLimitRecordsModel{
		conn:  conn,
		table: "`welfare_points_shops_limit_records`",
	}
}

func (m *defaultWelfarePointsShopsLimitRecordsModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfarePointsShopsLimitRecords{}, id).Error

	return err
}

func (m *defaultWelfarePointsShopsLimitRecordsModel) FindOne(ctx context.Context, id int64) (*WelfarePointsShopsLimitRecords, error) {
	var resp WelfarePointsShopsLimitRecords
	err := m.conn.WithContext(ctx).Model(&WelfarePointsShopsLimitRecords{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfarePointsShopsLimitRecordsModel) Insert(ctx context.Context, data *WelfarePointsShopsLimitRecords) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfarePointsShopsLimitRecordsModel) Update(ctx context.Context, data *WelfarePointsShopsLimitRecords) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
