package welfare_points_shops_limit_records

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                                     = gormcsql.InitField
	_ WelfarePointsShopsLimitRecordsModel = (*customWelfarePointsShopsLimitRecordsModel)(nil)
)

type (
	// WelfarePointsShopsLimitRecordsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfarePointsShopsLimitRecordsModel.
	WelfarePointsShopsLimitRecordsModel interface {
		welfarePointsShopsLimitRecordsModel
		customWelfarePointsShopsLimitRecordsLogicModel
	}

	customWelfarePointsShopsLimitRecordsLogicModel interface {
		WithSession(tx *gorm.DB) WelfarePointsShopsLimitRecordsModel
	}

	customWelfarePointsShopsLimitRecordsModel struct {
		*defaultWelfarePointsShopsLimitRecordsModel
	}
)

func (c customWelfarePointsShopsLimitRecordsModel) WithSession(tx *gorm.DB) WelfarePointsShopsLimitRecordsModel {
	newModel := *c.defaultWelfarePointsShopsLimitRecordsModel
	c.defaultWelfarePointsShopsLimitRecordsModel = &newModel
	c.conn = tx
	return c
}

// NewWelfarePointsShopsLimitRecordsModel returns a model for the database table.
func NewWelfarePointsShopsLimitRecordsModel(conn *gorm.DB) WelfarePointsShopsLimitRecordsModel {
	return &customWelfarePointsShopsLimitRecordsModel{
		defaultWelfarePointsShopsLimitRecordsModel: newWelfarePointsShopsLimitRecordsModel(conn),
	}
}
