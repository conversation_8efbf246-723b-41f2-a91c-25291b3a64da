package welfare_points_records

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                           = gormcsql.InitField
	_ WelfarePointsRecordsModel = (*customWelfarePointsRecordsModel)(nil)
)

type (
	// WelfarePointsRecordsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfarePointsRecordsModel.
	WelfarePointsRecordsModel interface {
		welfarePointsRecordsModel
		customWelfarePointsRecordsLogicModel
	}

	customWelfarePointsRecordsLogicModel interface {
		WithSession(tx *gorm.DB) WelfarePointsRecordsModel
	}

	customWelfarePointsRecordsModel struct {
		*defaultWelfarePointsRecordsModel
	}
)

func (c customWelfarePointsRecordsModel) WithSession(tx *gorm.DB) WelfarePointsRecordsModel {
	newModel := *c.defaultWelfarePointsRecordsModel
	c.defaultWelfarePointsRecordsModel = &newModel
	c.conn = tx
	return c
}

// NewWelfarePointsRecordsModel returns a model for the database table.
func NewWelfarePointsRecordsModel(conn *gorm.DB) WelfarePointsRecordsModel {
	return &customWelfarePointsRecordsModel{
		defaultWelfarePointsRecordsModel: newWelfarePointsRecordsModel(conn),
	}
}
