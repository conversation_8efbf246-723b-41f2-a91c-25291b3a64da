// Code generated by goctl. DO NOT EDIT.

package welfare_points_records

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfarePointsRecordsModel interface {
		Insert(ctx context.Context, data *WelfarePointsRecords) error

		FindOne(ctx context.Context, id int64) (*WelfarePointsRecords, error)
		Update(ctx context.Context, data *WelfarePointsRecords) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfarePointsRecordsModel struct {
		conn  *gorm.DB
		table string
	}

	WelfarePointsRecords struct {
		Id            int64     `gorm:"column:id"`
		Uid           int64     `gorm:"column:uid"`            // uid
		Action        string    `gorm:"column:action"`         // incr增加 decr减少
		Points        int64     `gorm:"column:points"`         // 积分
		Type          int64     `gorm:"column:type"`           // 类型 1新手任务 2福利任务 3邀请任务
		TaskId        int64     `gorm:"column:task_id"`        // 任务id
		TaskType      int64     `gorm:"column:task_type"`      // 任务类型：1kyc2认证 2现货 3合约 4邀请 5量化 6余币宝 7startup
		CorrelationId int64     `gorm:"column:correlation_id"` // 任务/兑换记录id
		Memo          string    `gorm:"column:memo"`           // 备注
		CreatedAt     time.Time `gorm:"column:created_at"`
		UpdatedAt     time.Time `gorm:"column:updated_at"`
	}
)

var QWelfarePointsRecords WelfarePointsRecords

func init() {
	gormcsql.InitField(&QWelfarePointsRecords)
}

func (WelfarePointsRecords) TableName() string {
	return "`welfare_points_records`"
}

func newWelfarePointsRecordsModel(conn *gorm.DB) *defaultWelfarePointsRecordsModel {
	return &defaultWelfarePointsRecordsModel{
		conn:  conn,
		table: "`welfare_points_records`",
	}
}

func (m *defaultWelfarePointsRecordsModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfarePointsRecords{}, id).Error

	return err
}

func (m *defaultWelfarePointsRecordsModel) FindOne(ctx context.Context, id int64) (*WelfarePointsRecords, error) {
	var resp WelfarePointsRecords
	err := m.conn.WithContext(ctx).Model(&WelfarePointsRecords{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfarePointsRecordsModel) Insert(ctx context.Context, data *WelfarePointsRecords) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfarePointsRecordsModel) Update(ctx context.Context, data *WelfarePointsRecords) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
