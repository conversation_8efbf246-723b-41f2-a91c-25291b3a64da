package welfare_task_cfgs

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                      = gormcsql.InitField
	_ WelfareTaskCfgsModel = (*customWelfareTaskCfgsModel)(nil)
)

type (
	// WelfareTaskCfgsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfareTaskCfgsModel.
	WelfareTaskCfgsModel interface {
		welfareTaskCfgsModel
		customWelfareTaskCfgsLogicModel
	}

	customWelfareTaskCfgsLogicModel interface {
		WithSession(tx *gorm.DB) WelfareTaskCfgsModel
	}

	customWelfareTaskCfgsModel struct {
		*defaultWelfareTaskCfgsModel
	}
)

func (c customWelfareTaskCfgsModel) WithSession(tx *gorm.DB) WelfareTaskCfgsModel {
	newModel := *c.defaultWelfareTaskCfgsModel
	c.defaultWelfareTaskCfgsModel = &newModel
	c.conn = tx
	return c
}

// NewWelfareTaskCfgsModel returns a model for the database table.
func NewWelfareTaskCfgsModel(conn *gorm.DB) WelfareTaskCfgsModel {
	return &customWelfareTaskCfgsModel{
		defaultWelfareTaskCfgsModel: newWelfareTaskCfgsModel(conn),
	}
}
