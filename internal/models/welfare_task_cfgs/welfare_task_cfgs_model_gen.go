// Code generated by goctl. DO NOT EDIT.

package welfare_task_cfgs

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfareTaskCfgsModel interface {
		Insert(ctx context.Context, data *WelfareTaskCfgs) error

		FindOne(ctx context.Context, id int64) (*WelfareTaskCfgs, error)
		Update(ctx context.Context, data *WelfareTaskCfgs) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfareTaskCfgsModel struct {
		conn  *gorm.DB
		table string
	}

	WelfareTaskCfgs struct {
		Id               int64          `gorm:"column:id"`                 // 数据ID
		TaskRegionKey    string         `gorm:"column:task_region_key"`    // 任务所在地区id welfare_region_cfgs.task_region_key
		TaskRegion       string         `gorm:"column:task_region"`        // 任务所在地区标识
		TaskIdentityId   int64          `gorm:"column:task_identity_id"`   // 任务所在身份id 1.普通用户，2.VIP
		TaskIdentityName string         `gorm:"column:task_identity_name"` // 任务所在身份
		TaskId           int64          `gorm:"column:task_id"`            // 任务ID
		TaskName         string         `gorm:"column:task_name"`          // 任务名称
		Type             int64          `gorm:"column:type"`               // 福利中心任务类型，10 新客注册任务、11 入门任务
		TaskType         int64          `gorm:"column:task_type"`          // 任务系统任务类型转换为int 1 kyc2任务、2现货
		TaskCenterType   int64          `gorm:"column:task_center_type"`   // 1常规任务、2循环任务、3多次任务、4用户可领取多次任务
		EffectiveTime    string         `gorm:"column:effective_time"`     // 生效时间
		ExtraTaskInfo    sql.NullString `gorm:"column:extra_task_info"`    // 任务额外信息
		Reward           sql.NullString `gorm:"column:reward"`             // 基础奖励list
		ButtonType       string         `gorm:"column:button_type"`        // 操作按钮类型 1.register 2.download 3.deposit 4.trade 5.verify 6.complete
		Status           int64          `gorm:"column:status"`             // 状态：1=待审核, 2=已生效, 3=已失效
		ApprovedSnapshot sql.NullString `gorm:"column:approved_snapshot"`  // 审核通过的线上快照
		OnlineStatus     int64          `gorm:"column:online_status"`      // 上线状态：1=上线, 2=下线
		CreatorId        int64          `gorm:"column:creator_id"`         // 创建人ID
		CreatorName      string         `gorm:"column:creator_name"`       // 创建人姓名
		ApprovalId       int64          `gorm:"column:approval_id"`        // 审核人ID
		ApprovalName     string         `gorm:"column:approval_name"`      // 审核人姓名
		Sort             int64          `gorm:"column:sort"`               // 排序
		PreEnv           int64          `gorm:"column:pre_env"`            // 是否是预发环境 1:是
		Version          int64          `gorm:"column:version"`            // 版本
		CreatedAt        time.Time      `gorm:"column:created_at"`         // 创建时间
		UpdatedAt        time.Time      `gorm:"column:updated_at"`         // 更新时间
	}
)

var QWelfareTaskCfgs WelfareTaskCfgs

func init() {
	gormcsql.InitField(&QWelfareTaskCfgs)
}

func (WelfareTaskCfgs) TableName() string {
	return "`welfare_task_cfgs`"
}

func newWelfareTaskCfgsModel(conn *gorm.DB) *defaultWelfareTaskCfgsModel {
	return &defaultWelfareTaskCfgsModel{
		conn:  conn,
		table: "`welfare_task_cfgs`",
	}
}

func (m *defaultWelfareTaskCfgsModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfareTaskCfgs{}, id).Error

	return err
}

func (m *defaultWelfareTaskCfgsModel) FindOne(ctx context.Context, id int64) (*WelfareTaskCfgs, error) {
	var resp WelfareTaskCfgs
	err := m.conn.WithContext(ctx).Model(&WelfareTaskCfgs{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfareTaskCfgsModel) Insert(ctx context.Context, data *WelfareTaskCfgs) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfareTaskCfgsModel) Update(ctx context.Context, data *WelfareTaskCfgs) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
