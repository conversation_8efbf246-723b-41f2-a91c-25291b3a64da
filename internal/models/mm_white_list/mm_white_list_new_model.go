package mm_white_list

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                     = gormcsql.InitField
	_ MmWhiteListNewModel = (*customMmWhiteListNewModel)(nil)
)

type (
	// MmWhiteListNewModel is an interface to be customized, add more methods here,
	// and implement the added methods in customMmWhiteListNewModel.
	MmWhiteListNewModel interface {
		mmWhiteListNewModel
		customMmWhiteListNewLogicModel
	}

	customMmWhiteListNewLogicModel interface {
		WithSession(tx *gorm.DB) MmWhiteListNewModel
	}

	customMmWhiteListNewModel struct {
		*defaultMmWhiteListNewModel
	}
)

func (c customMmWhiteListNewModel) WithSession(tx *gorm.DB) MmWhiteListNewModel {
	newModel := *c.defaultMmWhiteListNewModel
	c.defaultMmWhiteListNewModel = &newModel
	c.conn = tx
	return c
}

// NewMmWhiteListNewModel returns a model for the database table.
func NewMmWhiteListNewModel(conn *gorm.DB) MmWhiteListNewModel {
	return &customMmWhiteListNewModel{
		defaultMmWhiteListNewModel: newMmWhiteListNewModel(conn),
	}
}
