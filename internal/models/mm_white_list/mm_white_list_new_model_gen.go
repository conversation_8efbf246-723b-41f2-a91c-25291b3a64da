// Code generated by goctl. DO NOT EDIT.

package mm_white_list

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	mmWhiteListNewModel interface {
		Insert(ctx context.Context, data *MmWhiteListNew) error

		FindOne(ctx context.Context, id int64) (*MmWhiteListNew, error)
		Update(ctx context.Context, data *MmWhiteListNew) error

		Delete(ctx context.Context, id int64) error
	}

	defaultMmWhiteListNewModel struct {
		conn  *gorm.DB
		table string
	}

	MmWhiteListNew struct {
		Id        int64     `gorm:"column:id"`
		Uid       int64     `gorm:"column:uid"`
		Type      string    `gorm:"column:type"`
		Tier      int64     `gorm:"column:tier"`
		CreatedAt time.Time `gorm:"column:created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at"`
		IsIncr    int64     `gorm:"column:is_incr"`
	}
)

var QMmWhiteListNew MmWhiteListNew

func init() {
	gormcsql.InitField(&QMmWhiteListNew)
}

func (MmWhiteListNew) TableName() string {
	return "`mm_white_list_new`"
}

func newMmWhiteListNewModel(conn *gorm.DB) *defaultMmWhiteListNewModel {
	return &defaultMmWhiteListNewModel{
		conn:  conn,
		table: "`mm_white_list_new`",
	}
}

func (m *defaultMmWhiteListNewModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&MmWhiteListNew{}, id).Error

	return err
}

func (m *defaultMmWhiteListNewModel) FindOne(ctx context.Context, id int64) (*MmWhiteListNew, error) {
	var resp MmWhiteListNew
	err := m.conn.WithContext(ctx).Model(&MmWhiteListNew{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultMmWhiteListNewModel) Insert(ctx context.Context, data *MmWhiteListNew) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultMmWhiteListNewModel) Update(ctx context.Context, data *MmWhiteListNew) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
