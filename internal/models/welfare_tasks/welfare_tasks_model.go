package welfare_tasks

import (
	"bitbucket.org/gatebackend/gorm-zero/gormc"
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"context"
	"errors"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                   = gormcsql.InitField
	_ WelfareTasksModel = (*customWelfareTasksModel)(nil)
)

type (
	// WelfareTasksModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfareTasksModel.
	WelfareTasksModel interface {
		welfareTasksModel
		customWelfareTasksLogicModel
		FindListByConditions(ctx context.Context, baseType, taskType, status int, taskId, sourceId int64, taskIds []int64) ([]*WelfareTasks, error)
	}

	customWelfareTasksLogicModel interface {
		WithSession(tx *gorm.DB) WelfareTasksModel
	}

	customWelfareTasksModel struct {
		*defaultWelfareTasksModel
	}
)

func (c customWelfareTasksModel) WithSession(tx *gorm.DB) WelfareTasksModel {
	newModel := *c.defaultWelfareTasksModel
	c.defaultWelfareTasksModel = &newModel
	c.conn = tx
	return c
}

// NewWelfareTasksModel returns a model for the database table.
func NewWelfareTasksModel(conn *gorm.DB) WelfareTasksModel {
	return &customWelfareTasksModel{
		defaultWelfareTasksModel: newWelfareTasksModel(conn),
	}
}

// tIds 本地任务ID列表
func (c customWelfareTasksModel) FindListByConditions(ctx context.Context, baseType, taskType, status int, taskId, sourceId int64, tIds []int64) ([]*WelfareTasks, error) {
	resp := []*WelfareTasks{}
	query := c.conn.WithContext(ctx).Model(WelfareTasks{})
	// 根据条件动态构建查询
	if baseType > 0 {
		query = query.Where("`base_type` = ?", baseType)
	}
	if taskType > 0 {
		query = query.Where("`task_type` = ?", taskType)
	}
	if status > 0 {
		query = query.Where("`status` = ?", status)
	}
	if taskId > 0 {
		query = query.Where("`task_sys_id` = ?", taskId)
	}
	if sourceId > 0 {
		query = query.Where("`source_id` = ?", sourceId)
	}
	if len(tIds) > 0 {
		query = query.Where("`id` in ?", tIds)
	}
	err := query.Order("weight_sort ASC,id ASC").Find(&resp).Error
	if err != nil && !errors.Is(err, gormc.ErrNotFound) {
		return nil, err
	}
	return resp, nil
}
