// Code generated by goctl. DO NOT EDIT.

package welfare_tasks

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfareTasksModel interface {
		Insert(ctx context.Context, data *WelfareTasks) error

		FindOne(ctx context.Context, id int64) (*WelfareTasks, error)
		Update(ctx context.Context, data *WelfareTasks) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfareTasksModel struct {
		conn  *gorm.DB
		table string
	}

	WelfareTasks struct {
		Id              int64          `gorm:"column:id"`                // 数据ID
		BaseType        int64          `gorm:"column:base_type"`         // 1挑战任务 2福利任务 3邀请任务 4新人任务 5签到任务 6tg签到任务  7限时任务
		TaskSysId       int64          `gorm:"column:task_sys_id"`       // 任务系统ID
		WelfareTaskDesc string         `gorm:"column:welfare_task_desc"` // 任务描述
		TaskType        int64          `gorm:"column:task_type"`         // 任务类型：1kyc2认证 2现货 3合约 4邀请 5量化 6余币宝 7startup 8首次入金
		Status          int64          `gorm:"column:status"`            // 上下架状态 1:上架 2:下架
		PreEnv          int64          `gorm:"column:pre_env"`           // 是否是预发环境 1:是
		TemplateId      int64          `gorm:"column:template_id"`       // 模版ID 由运营人员输入
		ComplianceCode  string         `gorm:"column:compliance_code"`   // 合规code 用英文逗号格开
		PreTaskSysId    int64          `gorm:"column:pre_task_sys_id"`   // 前置任务系统ID 需要完成这个前置任务再领取当前任务
		SourceId        int64          `gorm:"column:source_id"`         // Source ID 来源是任务中台，在此另存一份
		WeightSort      int64          `gorm:"column:weight_sort"`       // 权重 用于排序，默认倒序
		CreatedAt       time.Time      `gorm:"column:created_at"`        // 创建时间
		UpdatedAt       time.Time      `gorm:"column:updated_at"`        // 更新时间
		DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;index"`  // 删除时间 不为null则是被删除数据
	}
)

var QWelfareTasks WelfareTasks

func init() {
	gormcsql.InitField(&QWelfareTasks)
}

func (WelfareTasks) TableName() string {
	return "`welfare_tasks`"
}

func newWelfareTasksModel(conn *gorm.DB) *defaultWelfareTasksModel {
	return &defaultWelfareTasksModel{
		conn:  conn,
		table: "`welfare_tasks`",
	}
}

func (m *defaultWelfareTasksModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfareTasks{}, id).Error

	return err
}

func (m *defaultWelfareTasksModel) FindOne(ctx context.Context, id int64) (*WelfareTasks, error) {
	var resp WelfareTasks
	err := m.conn.WithContext(ctx).Model(&WelfareTasks{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfareTasksModel) Insert(ctx context.Context, data *WelfareTasks) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfareTasksModel) Update(ctx context.Context, data *WelfareTasks) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
