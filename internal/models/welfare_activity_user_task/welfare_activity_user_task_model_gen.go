// Code generated by goctl. DO NOT EDIT.

package welfare_activity_user_task

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfareActivityUserTaskModel interface {
		Insert(ctx context.Context, data *WelfareActivityUserTask) error

		FindOne(ctx context.Context, id int64) (*WelfareActivityUserTask, error)
		Update(ctx context.Context, data *WelfareActivityUserTask) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfareActivityUserTaskModel struct {
		conn  *gorm.DB
		table string
	}

	WelfareActivityUserTask struct {
		Id        int64     `gorm:"column:id"`
		Uid       int64     `gorm:"column:uid"`     // 用户 uid
		TaskId    int64     `gorm:"column:task_id"` // 任务表主键
		SysId     int64     `gorm:"column:sys_id"`  // 任务中心任务 id
		Aid       int64     `gorm:"column:aid"`     // 活动标识 定向活动-1
		Status    int64     `gorm:"column:status"`  // 状态 0未完成 1进行中 2已完成 3结算中 4已结算 5过期
		Points    int64     `gorm:"column:points"`  // 积分,任务中心拉取
		CreatedAt time.Time `gorm:"column:created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at"`
		Source    string    `gorm:"column:source"`
		Ack       int64     `gorm:"column:ack"` // ack状态
	}
)

var QWelfareActivityUserTask WelfareActivityUserTask

func init() {
	gormcsql.InitField(&QWelfareActivityUserTask)
}

func (WelfareActivityUserTask) TableName() string {
	return "`welfare_activity_user_task`"
}

func newWelfareActivityUserTaskModel(conn *gorm.DB) *defaultWelfareActivityUserTaskModel {
	return &defaultWelfareActivityUserTaskModel{
		conn:  conn,
		table: "`welfare_activity_user_task`",
	}
}

func (m *defaultWelfareActivityUserTaskModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfareActivityUserTask{}, id).Error

	return err
}

func (m *defaultWelfareActivityUserTaskModel) FindOne(ctx context.Context, id int64) (*WelfareActivityUserTask, error) {
	var resp WelfareActivityUserTask
	err := m.conn.WithContext(ctx).Model(&WelfareActivityUserTask{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfareActivityUserTaskModel) Insert(ctx context.Context, data *WelfareActivityUserTask) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfareActivityUserTaskModel) Update(ctx context.Context, data *WelfareActivityUserTask) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
