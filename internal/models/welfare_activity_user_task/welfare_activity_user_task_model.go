package welfare_activity_user_task

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                              = gormcsql.InitField
	_ WelfareActivityUserTaskModel = (*customWelfareActivityUserTaskModel)(nil)
)

type (
	// WelfareActivityUserTaskModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfareActivityUserTaskModel.
	WelfareActivityUserTaskModel interface {
		welfareActivityUserTaskModel
		customWelfareActivityUserTaskLogicModel
	}

	customWelfareActivityUserTaskLogicModel interface {
		WithSession(tx *gorm.DB) WelfareActivityUserTaskModel
	}

	customWelfareActivityUserTaskModel struct {
		*defaultWelfareActivityUserTaskModel
	}
)

func (c customWelfareActivityUserTaskModel) WithSession(tx *gorm.DB) WelfareActivityUserTaskModel {
	newModel := *c.defaultWelfareActivityUserTaskModel
	c.defaultWelfareActivityUserTaskModel = &newModel
	c.conn = tx
	return c
}

// NewWelfareActivityUserTaskModel returns a model for the database table.
func NewWelfareActivityUserTaskModel(conn *gorm.DB) WelfareActivityUserTaskModel {
	return &customWelfareActivityUserTaskModel{
		defaultWelfareActivityUserTaskModel: newWelfareActivityUserTaskModel(conn),
	}
}
