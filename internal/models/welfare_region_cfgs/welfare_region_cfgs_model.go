package welfare_region_cfgs

import (
	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var (
	_                        = gormcsql.InitField
	_ WelfareRegionCfgsModel = (*customWelfareRegionCfgsModel)(nil)
)

type (
	// WelfareRegionCfgsModel is an interface to be customized, add more methods here,
	// and implement the added methods in customWelfareRegionCfgsModel.
	WelfareRegionCfgsModel interface {
		welfareRegionCfgsModel
		customWelfareRegionCfgsLogicModel
	}

	customWelfareRegionCfgsLogicModel interface {
		WithSession(tx *gorm.DB) WelfareRegionCfgsModel
	}

	customWelfareRegionCfgsModel struct {
		*defaultWelfareRegionCfgsModel
	}
)

func (c customWelfareRegionCfgsModel) WithSession(tx *gorm.DB) WelfareRegionCfgsModel {
	newModel := *c.defaultWelfareRegionCfgsModel
	c.defaultWelfareRegionCfgsModel = &newModel
	c.conn = tx
	return c
}

// NewWelfareRegionCfgsModel returns a model for the database table.
func NewWelfareRegionCfgsModel(conn *gorm.DB) WelfareRegionCfgsModel {
	return &customWelfareRegionCfgsModel{
		defaultWelfareRegionCfgsModel: newWelfareRegionCfgsModel(conn),
	}
}
