// Code generated by goctl. DO NOT EDIT.

package welfare_region_cfgs

import (
	"context"
	"database/sql"
	"time"

	gormcsql "bitbucket.org/gatebackend/gorm-zero/gormc/sql"
	"gorm.io/gorm"
)

// avoid unused err.
var _ = time.Second

type (
	welfareRegionCfgsModel interface {
		Insert(ctx context.Context, data *WelfareRegionCfgs) error

		FindOne(ctx context.Context, id int64) (*WelfareRegionCfgs, error)
		FindOneByTaskRegionKey(ctx context.Context, taskRegionKey string) (*WelfareRegionCfgs, error)
		Update(ctx context.Context, data *WelfareRegionCfgs) error

		Delete(ctx context.Context, id int64) error
	}

	defaultWelfareRegionCfgsModel struct {
		conn  *gorm.DB
		table string
	}

	WelfareRegionCfgs struct {
		Id            int64     `gorm:"column:id"`              // 配置ID
		TaskRegionKey string    `gorm:"column:task_region_key"` // 任务所在地区key
		TaskRegion    string    `gorm:"column:task_region"`     // 任务所在地区
		DayNum        int64     `gorm:"column:day_num"`         // 周期配置 天数
		CustomerType  int64     `gorm:"column:customer_type"`   // 客户类型 0新老客 1新客 2老客
		CreatedAt     time.Time `gorm:"column:created_at"`      // 创建时间
		UpdatedAt     time.Time `gorm:"column:updated_at"`      // 更新时间
	}
)

var QWelfareRegionCfgs WelfareRegionCfgs

func init() {
	gormcsql.InitField(&QWelfareRegionCfgs)
}

func (WelfareRegionCfgs) TableName() string {
	return "`welfare_region_cfgs`"
}

func newWelfareRegionCfgsModel(conn *gorm.DB) *defaultWelfareRegionCfgsModel {
	return &defaultWelfareRegionCfgsModel{
		conn:  conn,
		table: "`welfare_region_cfgs`",
	}
}

func (m *defaultWelfareRegionCfgsModel) Delete(ctx context.Context, id int64) error {
	db := m.conn
	err := db.WithContext(ctx).Delete(&WelfareRegionCfgs{}, id).Error

	return err
}

func (m *defaultWelfareRegionCfgsModel) FindOne(ctx context.Context, id int64) (*WelfareRegionCfgs, error) {
	var resp WelfareRegionCfgs
	err := m.conn.WithContext(ctx).Model(&WelfareRegionCfgs{}).Where("`id` = @id", sql.Named("id", id)).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err

}

func (m *defaultWelfareRegionCfgsModel) FindOneByTaskRegionKey(ctx context.Context, taskRegionKey string) (*WelfareRegionCfgs, error) {
	var resp WelfareRegionCfgs
	err := m.conn.WithContext(ctx).Model(&WelfareRegionCfgs{}).Where("`task_region_key` = ?", taskRegionKey).Take(&resp).Error
	if err == ErrNotFound {
		return nil, err
	}
	return &resp, err
}

func (m *defaultWelfareRegionCfgsModel) Insert(ctx context.Context, data *WelfareRegionCfgs) error {
	db := m.conn
	err := db.WithContext(ctx).Save(&data).Error
	return err
}

func (m *defaultWelfareRegionCfgsModel) Update(ctx context.Context, data *WelfareRegionCfgs) error {
	db := m.conn
	err := db.WithContext(ctx).Updates(data).Error
	return err
}
