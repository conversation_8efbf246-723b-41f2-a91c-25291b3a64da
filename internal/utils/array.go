package utils

import (
	"fmt"
	"strings"
)

// 判断字符串是否在数组中.
func InArray(needle string, haystack []string) bool {
	for _, item := range haystack {
		if item == needle {
			return true
		}
	}
	return false
}

func Intersection[T comparable](a, b []T) []T {
	set := make(map[T]struct{})
	result := []T{}
	seen := make(map[T]struct{})

	for _, val := range a {
		set[val] = struct{}{}
	}

	for _, val := range b {
		if _, exists := set[val]; exists {
			if _, added := seen[val]; !added {
				result = append(result, val)
				seen[val] = struct{}{}
			}
		}
	}

	return result
}

// 将int64列表转化成字符串
func IntListToStr(nums []int64) string {
	if len(nums) == 0 {
		return ""
	}

	strs := make([]string, len(nums))
	for i, num := range nums {
		strs[i] = fmt.Sprintf("%d", num)
	}

	return strings.Join(strs, ",")
}
