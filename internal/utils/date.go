package utils

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"strconv"
	"time"
)

const (
	DateYmdFormat  = "20060102"
	DateFormat     = "2006-01-02"
	TimeFormat     = "15:04:05"
	DateTimeFormat = "2006-01-02 15:04:05"
)

// TimeUtil 封装了 time 包的一些常用功能
type TimeUtil struct{}

// Now 返回当前时间
func (tu *TimeUtil) Now() time.Time {
	return time.Now()
}

// GetNowTime 获取当前时间戳
func (tu *TimeUtil) GetNowTime(t time.Time) int64 {
	return t.Unix()
}

func (tu *TimeUtil) GetNowMilliTime(t time.Time) int64 {
	return t.UnixMilli()
}

func (tu *TimeUtil) FormatTime(t time.Time) string {
	return t.Format(DateTimeFormat)
}

func (tu *TimeUtil) FormatYmd(t time.Time) int64 {
	return MustInt64(t.Format(DateYmdFormat), 0)
}

func TransformDateStringToTime(timeStr string) (time.Time, error) {
	// 解析时间字符串为 time.Time 类型
	t, err := time.Parse(DateTimeFormat, timeStr)
	if err != nil {
		return time.Time{}, err
	}
	return t, nil
}

// 获取格式为 YYYYMMDD 的时间 int类型 20240725
func GetYmdDay(timestampVal int64) int {
	result := 0
	// 获取当前时间
	now := time.Now()
	//如果传入时间就使用传入的时间戳
	if timestampVal > 0 {
		// 将时间戳转换为 time.Time 类型
		now = time.Unix(int64(timestampVal), 0)
	}
	// 将时间格式化为 YYYYMMDD 格式的字符串
	formatted := now.Format("20060102")
	// 将格式化后的字符串转换为 int 类型
	result, err := strconv.Atoi(formatted)
	if err != nil {
		logx.Info("GetYmdDay strconv.Atoi(formatted) is err:", formatted, err)
	}
	return result
}

// 获取当前时间是周几
func GetWeek(timestampVal int) int {
	result := 0
	// 获取当前时间
	now := time.Now()
	//如果传入时间就使用传入的时间戳
	if timestampVal > 0 {
		// 将时间戳转换为 time.Time 类型
		now = time.Unix(int64(timestampVal), 0)
	}
	result = int(now.Weekday())
	if result == 0 {
		result = 7
	}
	return result
}

// GetDateByWeekday 根据周几获取本周对应的日期
func GetDateByWeekday(weekday int) int {
	// 获取当前时间
	now := time.Now()
	// 计算本周一的日期
	monday := now.AddDate(0, 0, -int(now.Weekday()-time.Monday))
	// 计算指定周几对应的日期
	targetDate := monday.AddDate(0, 0, int(time.Weekday(weekday)-time.Monday))

	// 将时间格式化为 YYYYMMDD 格式的字符串
	formatted := targetDate.Format("20060102")
	// 将格式化后的字符串转换为 int 类型
	result, err := strconv.Atoi(formatted)
	if err != nil {
		logx.Info("GetYmdDay strconv.Atoi(formatted) is err:", formatted, err)
	}
	return result
}

// CalculateExpireTime 计算过期时间并返回格式化后的字符串
func CalculateExpireTime(addTime int) string {
	expireTime := time.Now().Add(time.Duration(addTime) * time.Second)
	return expireTime.Format("20060102")
}

func GetCurrentDayLastSec() int64 {
	now := time.Now()
	// 构建当天 23:59:59
	endOfDay := time.Date(
		now.Year(),
		now.Month(),
		now.Day(),
		23, 59, 59, 0,
		now.Location(),
	)

	return endOfDay.Unix()
}
