package utils

import (
	"context"

	"gorm.io/gorm"
)

// DBUtil 数据库操作工具类
type DBUtil struct {
	DB *gorm.DB
}

// NewDBUtil 创建数据库工具类实例
func NewDBUtil(DB *gorm.DB) *DBUtil {
	return &DBUtil{DB: DB}
}

// Create 创建记录
func (d *DBUtil) Create(ctx context.Context, model interface{}) error {
	return d.DB.WithContext(ctx).Create(model).Error
}

// Update 更新记录
func (d *DBUtil) Update(ctx context.Context, model interface{}, conditions interface{}) error {
	return d.DB.WithContext(ctx).Model(model).Where(conditions).Updates(model).Error
}

// Delete 删除记录
func (d *DBUtil) Delete(ctx context.Context, model interface{}, conditions interface{}) error {
	return d.DB.WithContext(ctx).Where(conditions).Delete(model).Error
}

// FindOne 查询单条记录
func (d *DBUtil) FindOne(ctx context.Context, model interface{}, conditions interface{}) error {
	return d.DB.WithContext(ctx).Where(conditions).First(model).Error
}

// FindList 查询多条记录
func (d *DBUtil) FindList(ctx context.Context, model interface{}, conditions interface{}) error {
	return d.DB.WithContext(ctx).Where(conditions).Find(model).Error
}

// FindPage 分页查询
func (d *DBUtil) FindPage(ctx context.Context, model interface{}, conditions interface{}, page, pageSize int) error {
	offset := (page - 1) * pageSize
	return d.DB.WithContext(ctx).Where(conditions).Offset(offset).Limit(pageSize).Find(model).Error
}

// Count 统计记录数
func (d *DBUtil) Count(ctx context.Context, model interface{}, conditions interface{}) (int64, error) {
	var count int64
	err := d.DB.WithContext(ctx).Model(model).Where(conditions).Count(&count).Error
	return count, err
}

// Transaction 事务操作
func (d *DBUtil) Transaction(ctx context.Context, fc func(tx *gorm.DB) error) error {
	return d.DB.WithContext(ctx).Transaction(fc)
}
