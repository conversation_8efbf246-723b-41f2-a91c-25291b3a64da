package utils

import (
	"context"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logc"
)

// ContextConfig Context配置
type ContextConfig struct {
	Timeout time.Duration
	Name    string // 用于日志标识
}

// WithTimeoutAndLog 创建带超时和日志的context
func WithTimeoutAndLog(parent context.Context, config ContextConfig) (context.Context, context.CancelFunc) {
	ctx, cancel := context.WithTimeout(parent, config.Timeout)
	
	// 记录context创建日志
	if config.Name != "" {
		logc.Infof(ctx, "Created context [%s] with timeout: %v", config.Name, config.Timeout)
	}
	
	// 包装cancel函数，添加日志
	wrappedCancel := func() {
		if config.Name != "" {
			logc.Infof(ctx, "Canceling context [%s]", config.Name)
		}
		cancel()
	}
	
	return ctx, wrappedCancel
}

// WithDBTimeout 创建数据库操作专用的context
func WithDBTimeout(parent context.Context) (context.Context, context.CancelFunc) {
	return WithTimeoutAndLog(parent, ContextConfig{
		Timeout: 30 * time.Second,
		Name:    "database_operation",
	})
}

// WithRedisTimeout 创建Redis操作专用的context
func WithRedisTimeout(parent context.Context) (context.Context, context.CancelFunc) {
	return WithTimeoutAndLog(parent, ContextConfig{
		Timeout: 10 * time.Second,
		Name:    "redis_operation",
	})
}

// WithHTTPTimeout 创建HTTP请求专用的context
func WithHTTPTimeout(parent context.Context) (context.Context, context.CancelFunc) {
	return WithTimeoutAndLog(parent, ContextConfig{
		Timeout: 30 * time.Second,
		Name:    "http_request",
	})
}

// WithTaskTimeout 创建任务处理专用的context
func WithTaskTimeout(parent context.Context, taskName string, timeout time.Duration) (context.Context, context.CancelFunc) {
	return WithTimeoutAndLog(parent, ContextConfig{
		Timeout: timeout,
		Name:    "task_" + taskName,
	})
}

// IsContextCanceled 检查context是否被取消
func IsContextCanceled(ctx context.Context) bool {
	select {
	case <-ctx.Done():
		return true
	default:
		return false
	}
}

// LogContextError 记录context相关错误
func LogContextError(ctx context.Context, operation string, err error) {
	if err == nil {
		return
	}
	
	if err == context.Canceled {
		logc.Warnf(ctx, "Operation [%s] was canceled: %v", operation, err)
	} else if err == context.DeadlineExceeded {
		logc.Errorf(ctx, "Operation [%s] timeout: %v", operation, err)
	} else {
		logc.Errorf(ctx, "Operation [%s] failed: %v", operation, err)
	}
}

// SafeExecuteWithTimeout 安全执行带超时的操作
func SafeExecuteWithTimeout(ctx context.Context, timeout time.Duration, operation string, fn func(context.Context) error) error {
	opCtx, cancel := WithTimeoutAndLog(ctx, ContextConfig{
		Timeout: timeout,
		Name:    operation,
	})
	defer cancel()
	
	err := fn(opCtx)
	LogContextError(opCtx, operation, err)
	return err
}
