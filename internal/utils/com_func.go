package utils

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/svc"
	"math/big"
	"net/http"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-ip-go/ip2location"
)

// Contains 泛型函数，用于判断元素是否在列表中
func ContainsArray[T comparable](list []T, element T) bool {
	for _, item := range list {
		if item == element {
			return true
		}
	}
	return false
}

// RemoveDuplicate 去掉重复项
func RemoveDuplicate(list []int64) []int64 {
	itemMap := make(map[int64]struct{}, len(list))
	for _, item := range list {
		itemMap[item] = struct{}{}
	}
	// 为了避免切片append
	result := make([]int64, len(itemMap))
	index := 0
	for item := range itemMap {
		result[index] = item
		index++
	}
	return result
}

// 获取用户国家ID
func GetUserCountry(r *http.Request) int {
	userInfo := requestools.GetUserInfo(r)
	countryId := 0
	if userInfo != nil && userInfo.UID > 0 {
		countryCode, err := strconv.Atoi(userInfo.Country)
		if err != nil {
			logx.Info("GetUserCountry strconv.Atoi(userInfo.Country) is err:%s,%v", userInfo.Country, err)
		}
		countryId = countryCode
	} else {
		ip := requestools.GetClientIP(r)
		detail, err := ip2location.GetDetailByIP(ip)
		if err != nil {
			logx.Infof("GetUserCountry ip2location.GetDetailByIP  is err, ip is:%s,err is:%v", ip, err)
		}
		if detail != nil {
			countryId = detail.CountryCode
		}
	}
	return countryId
}

/**
 *科学计数法转普通数字
 * @param $num         科学计数法字符串  如 2.1E-5
 * @param int $double 小数点保留位数 默认5位
 * @return string
 */
func Sctonum(num string, precision int) string {
	if precision <= 0 {
		precision = 5
	}

	// 检查是否包含科学计数法标记
	if !strings.Contains(strings.ToLower(num), "e") {
		return num
	}

	// 分割数字和指数部分
	parts := strings.Split(strings.ToLower(num), "e")
	if len(parts) != 2 {
		return num
	}

	// 解析底数和指数
	base, _ := new(big.Float).SetString(parts[0])
	exp, err := strconv.Atoi(parts[1])
	if err != nil {
		return num
	}

	// 计算10的指数次方
	ten := new(big.Float).SetInt64(10)
	power := new(big.Float).SetInt64(1)
	if exp > 0 {
		for i := 0; i < exp; i++ {
			power.Mul(power, ten)
		}
	} else {
		for i := 0; i < -exp; i++ {
			power.Quo(power, ten)
		}
	}

	// 计算结果
	result := new(big.Float).Mul(base, power)

	// 设置精度并转换为字符串
	result.SetPrec(uint(precision * 4)) // 设置足够的精度
	str := result.Text('f', precision)

	// 去除末尾的0和小数点
	str = strings.TrimRight(str, "0")
	str = strings.TrimRight(str, ".")

	return str
}

// DesensitizeString 对字符串进行脱敏处理
// str: 需要脱敏的字符串
// charLength: 脱敏字符的长度，默认为6
// isMiddle: 是否在中间脱敏，true为中间脱敏，false为两端脱敏
/*func DesensitizeString(str string, charLength int, isMiddle bool) string {
	if str == "" {
		return ""
	}

	if charLength <= 0 {
		charLength = 6
	}

	// 生成脱敏字符
	character := strings.Repeat("*", charLength)
	strLength := utf8.RuneCountInString(str)

	if isMiddle {
		// 中间脱敏模式
		if strLength > charLength {
			start := (strLength - charLength) / 2
			length := charLength
			// 获取中间部分
			middle := getSubstring(str, start, length)
			return character + middle + character
		} else {
			// 根据字符串长度处理不同情况
			var start, length int
			switch strLength {
			case 1:
				start, length = 0, 1
			case 2:
				start, length = 0, 1
			case 3:
				start, length = 1, 1
			case 4:
				start, length = 1, 2
			case 5:
				start, length = 1, 3
			case 6:
				start, length = 1, 4
			default:
				start, length = 0, 0
			}
			middle := getSubstring(str, start, length)
			return character + middle + character
		}
	} else {
		// 两端脱敏模式
		if strLength > charLength {
			if charLength*2 >= strLength {
				charLength = charLength / 2
			}
			beforeStart := 0
			beforeLength := charLength
			afterStart := strLength - charLength
			afterLength := charLength

			before := getSubstring(str, beforeStart, beforeLength)
			after := getSubstring(str, afterStart, afterLength)
			return before + character + after
		} else {
			// 根据字符串长度处理不同情况
			var beforeStart, beforeLength, afterStart, afterLength int
			switch strLength {
			case 1:
				beforeStart, beforeLength, afterStart, afterLength = 0, 1, 0, 1
			case 2:
				beforeStart, beforeLength, afterStart, afterLength = 0, 1, 1, 1
			case 3:
				beforeStart, beforeLength, afterStart, afterLength = 0, 1, 2, 1
			case 4:
				beforeStart, beforeLength, afterStart, afterLength = 0, 1, 3, 1
			case 5:
				beforeStart, beforeLength, afterStart, afterLength = 0, 2, 3, 2
			case 6:
				beforeStart, beforeLength, afterStart, afterLength = 0, 2, 4, 2
			default:
				beforeStart, beforeLength, afterStart, afterLength = 0, 3, strLength-3, 3
			}

			before := getSubstring(str, beforeStart, beforeLength)
			after := getSubstring(str, afterStart, afterLength)
			return before + character + after
		}
	}
}*/

// getSubstring 安全地获取子字符串，支持UTF-8编码
func getSubstring(str string, start, length int) string {
	runes := []rune(str)
	if start >= len(runes) {
		return ""
	}
	end := start + length
	if end > len(runes) {
		end = len(runes)
	}
	return string(runes[start:end])
}

// redis key 延时双删   delay:延时时间数值 最终会在函数中转成毫秒 maxRetry:删除失败的重试次数
func DelayDelRedisKey(svcCtx *svc.ServiceContext, redisKey string, delay time.Duration, maxRetry int) {
	_, err := svcCtx.Redis.DelCtx(context.Background(), redisKey)
	if err != nil {
		logx.Errorf("DelayDelRedisKey failed, redisKey is:%s, err:%v", redisKey, err)
	}
	go func() {
		time.Sleep(delay * time.Millisecond)
		for i := 0; i < maxRetry; i++ {
			_, err := svcCtx.Redis.DelCtx(context.Background(), redisKey)
			if err == nil {
				logx.Infof("DelayDelRedisKey delete RegisterInfo from Redis success, redisKey is:%s, try:%d", redisKey, i+1)
				return
			}
			logx.Errorf("DelayDelRedisKey failed, redisKey is:%s, try:%d, err:%v", redisKey, i+1, err)
			time.Sleep(100 * time.Millisecond)
		}
	}()
}

// 打印接口最终接口函数
func LogRouterData(ctx context.Context, routerName string, uid int, req interface{}, resp interface{}, err error) {
	reqByte, _ := json.Marshal(req)
	respByte, _ := json.Marshal(resp)
	logc.Infof(ctx, "%s is finish. uid: %d, Req: %s, resp: %s , failed: %v", routerName, uid, string(reqByte), string(respByte), err)
}

// 字符串转int
func ParseInt(str string) int {
	if str == "" {
		return 0
	}
	num, err := strconv.Atoi(str)
	if err != nil {
		// 记录错误或执行其他处理
		logx.Infof("ParseInt Atoi is err:%s , %v", str, err)
		return 0
	}
	return num
}

// ParseMapString 解析 JSON 字符串为 map[string]string 结构
func ParseMapString(jsonStr string) map[string]string {
	// 空字符串直接返回空 map
	if jsonStr == "" {
		return map[string]string{}
	}
	// 声明目标类型，减少类型断言
	var data map[string]string
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		logx.Infof("ParseMapString Unmarshal is err:%s , %v", jsonStr, err)
		return map[string]string{}
	}
	return data
}

// ParseMapIntList 解析 JSON 字符串为 map[string][]int64 结构
func ParseMapIntList(jsonStr string) map[string][]int64 {
	// 空字符串直接返回空 map
	if jsonStr == "" {
		return map[string][]int64{}
	}
	// 声明目标类型，减少类型断言
	var data map[string][]int64
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		logx.Infof("ParseMapIntList Unmarshal is err:%s , %v", jsonStr, err)
		return map[string][]int64{}
	}
	return data
}

// ParseList 解析 JSON 数组为 []int64 类型
func ParseList(jsonStr string) []int64 {
	// 处理空字符串
	if jsonStr == "" {
		return []int64{}
	}
	// 直接解析为 []int64
	var data []int64
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		logx.Infof("ParseList Unmarshal is err:%s , %v", jsonStr, err)
		return []int64{}
	}
	return data
}

func DelCommonRedisKey(svc *svc.ServiceContext, uid int64, Type int, lang string) bool {
	switch Type {
	case consts.UserTaskTypeNewbieGuide:
		beginnerKeys := []string{
			fmt.Sprintf(consts.WelfareBeginnerUserTasksKey, uid, lang, 1),
			fmt.Sprintf(consts.WelfareBeginnerUserTasksKey, uid, lang, 0),
		}
		for _, keyItem := range beginnerKeys {
			_, _ = svc.Redis.Del(keyItem)
			DelayDelRedisKey(svc, keyItem, 0, 3)
		}
	case consts.UserTaskTypeNewbieAdvanced:
		DelayDelRedisKey(svc, fmt.Sprintf(consts.WelfareAdvancedUserTasksKey, uid), 0, 3)
	case consts.UserTaskTypeVeteranDaily:
		DelayDelRedisKey(svc, fmt.Sprintf(consts.WelfareDailyUserTasksKey, uid, lang), 0, 3)
	case consts.UserTaskTypeVeteranLimited:
		DelayDelRedisKey(svc, fmt.Sprintf(consts.WelfareLimitUserTasksKey, uid, lang), 0, 3)
	}

	return true
}

var PrizeTypeList = map[int]string{
	consts.PointsShopTypePoint:                   "点卡",
	consts.PointsShopTypeVip1:                    "vip1",
	consts.PointsShopTypeFutures:                 "合约体验金",
	consts.PointsShopTypeStartup:                 "startup体验券",
	consts.PointsShopTypeQuantitative:            "交易机器人体验金",
	consts.PointsShopTypeVipAdd1:                 "vip+1",
	consts.PointsShopTypeFinance:                 "理财体验金",
	consts.PointsShopTypeContractBonusNew:        "合约体验券",
	consts.PointsShopTypeFinanceHighInterest:     "高息理财",
	consts.PointsShopTypeCommissionRebate10:      "10%手续费返现券",
	consts.PointsShopTypeCommissionRebate20:      "20%手续费返现券",
	consts.PointsShopTypeTaskSysPoints:           "积分",
	consts.PointsShopTypeTaskSysDiy:              "业务端自定义",
	consts.PointsShopTypeTaskSysCoupon:           "卡券",
	consts.PointsShopTypeTaskSysPrizePool:        "抽奖池",
	consts.PointsShopTypeQuantitativeNew:         "新版交易机器人体验金",
	consts.PointsShopTypeSpotCommissionRebate:    "现货手续费返现券",
	consts.PointsShopTypeFuturesCommissionRebate: "合约手续费返现券",
	consts.PointsShopTypeLossProtectionCopier:    "跟单包赔券",
	consts.PointsShopTypeVipCard:                 "VIP体验卡",
}

// 根据奖励类型获取描述
func GetTypeText(typeNum int, typeVal int) string {
	typeText, exists := PrizeTypeList[typeVal]
	if !exists {
		typeText = "-"
	}

	switch typeVal {
	case consts.PointsShopTypePoint:
		typeText = strconv.Itoa(typeNum) + typeText
	case consts.PointsShopTypeVip1:
		// todo vip暂不处理
	case consts.PointsShopTypeFutures:
		typeText = strconv.Itoa(typeNum) + "USDT" + typeText
	case consts.PointsShopTypeStartup:
		typeText = strconv.Itoa(typeNum) + "份额" + typeText
	case consts.PointsShopTypeQuantitative:
		typeText = strconv.Itoa(typeNum) + "USDT" + typeText
	case consts.PointsShopTypeVipAdd1:
		// todo vip+1暂不处理
	case consts.PointsShopTypeFinance:
		typeText = strconv.Itoa(typeNum) + typeText
	case consts.PointsShopTypeContractBonusNew:
		typeText = strconv.Itoa(typeNum) + "USDT" + typeText
	case consts.PointsShopTypeFinanceHighInterest:
		typeText = strconv.Itoa(typeNum) + "%" + typeText
	case consts.PointsShopTypeCommissionRebate10:
		// 无操作
	case consts.PointsShopTypeCommissionRebate20:
		// 无操作
	case consts.PointsShopTypeQuantitativeNew:
		typeText = strconv.Itoa(typeNum) + "USDT" + typeText
	case consts.PointsShopTypeSpotCommissionRebate:
		typeText = strconv.Itoa(typeNum) + "USDT" + typeText
	}
	return typeText
}

// DeduplicateMap 使用 map 去重，时间复杂度 O(n)，空间复杂度 O(n)
// 保持原始顺序，适用于需要保留顺序的场景
func DeduplicateMap(slice []string) []string {
	seen := make(map[string]struct{})
	result := make([]string, 0, len(slice))

	for _, item := range slice {
		if _, exists := seen[item]; !exists {
			seen[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

// InterfaceToInt 将 interface{} 转换为 int，支持的类型：int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64, string
func InterfaceToInt(value interface{}) int {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int8:
		return int(v)
	case int16:
		return int(v)
	case int32:
		return int(v)
	case int64:
		return int(v)
	case uint:
		return int(v)
	case uint8:
		return int(v)
	case uint16:
		return int(v)
	case uint32:
		return int(v)
	case uint64:
		return int(v)
	case float32:
		return int(v)
	case float64:
		return int(v)
	case string:
		i, err := strconv.Atoi(v)
		if err != nil {
			return 0
		}
		return i
	default:
		return 0
	}
}
