package utils

import (
	"context"
	"crypto/tls"
	"net/http"
	"time"
)

// HTTPClientConfig HTTP客户端配置
type HTTPClientConfig struct {
	Timeout             time.Duration
	InsecureSkipVerify  bool
	MaxIdleConns        int
	MaxIdleConnsPerHost int
	IdleConnTimeout     time.Duration
}

// DefaultHTTPClientConfig 默认HTTP客户端配置
func DefaultHTTPClientConfig() *HTTPClientConfig {
	return &HTTPClientConfig{
		Timeout:             30 * time.Second,
		InsecureSkipVerify:  false,
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
	}
}

// NewHTTPClient 创建带超时和连接池配置的HTTP客户端
func NewHTTPClient(config *HTTPClientConfig) *http.Client {
	if config == nil {
		config = DefaultHTTPClientConfig()
	}

	transport := &http.Transport{
		MaxIdleConns:        config.MaxIdleConns,
		MaxIdleConnsPerHost: config.MaxIdleConnsPerHost,
		IdleConnTimeout:     config.IdleConnTimeout,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: config.InsecureSkipVerify,
		},
	}

	return &http.Client{
		Timeout:   config.Timeout,
		Transport: transport,
	}
}

// NewHTTPClientWithContext 创建带context的HTTP客户端
func NewHTTPClientWithContext(ctx context.Context, config *HTTPClientConfig) *http.Client {
	client := NewHTTPClient(config)
	
	// 如果context有deadline，使用更短的超时时间
	if deadline, ok := ctx.Deadline(); ok {
		timeoutFromContext := time.Until(deadline)
		if timeoutFromContext < config.Timeout {
			client.Timeout = timeoutFromContext
		}
	}
	
	return client
}

// NewDevHTTPClient 创建开发环境HTTP客户端（跳过证书验证）
func NewDevHTTPClient(timeout time.Duration) *http.Client {
	config := DefaultHTTPClientConfig()
	config.Timeout = timeout
	config.InsecureSkipVerify = true
	return NewHTTPClient(config)
}
