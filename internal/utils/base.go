package utils

import (
	"bitbucket.org/gateio/gateio-lib-base-go/environment"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"github.com/joho/godotenv"
	"math"
	"math/big"
	"strings"
)

// key常量
const key = "gateio0411"

// UID加密
func GetEncryptedUid(uid int64) string {
	// 转16进制字符串
	hexStr := fmt.Sprintf("%x", uid)

	slen := len(hexStr)
	klen := len(key)

	cipher := []byte{}

	for i := 0; i < slen; i += klen {
		// 取子串，长度不够取剩余全部
		end := i + klen
		if end > slen {
			end = slen
		}
		subStr := hexStr[i:end]

		// 异或操作（逐字节）
		for j := 0; j < len(subStr); j++ {
			cipher = append(cipher, subStr[j]^key[j])
		}
	}
	// base64 编码，并替换URL安全字符
	base64Str := base64.StdEncoding.EncodeToString(cipher)
	base64Str = strings.ReplaceAll(base64Str, "=", "")
	base64Str = strings.ReplaceAll(base64Str, "+", "-")
	base64Str = strings.ReplaceAll(base64Str, "/", "_")

	return base64Str
}

// UID解密
func GetDecryptedUid(cipherText string) (int64, error) {
	// 还原base64字符
	// 注意base64编码长度需要被4整除，补'=‘
	padding := len(cipherText) % 4
	if padding > 0 {
		cipherText += strings.Repeat("=", 4-padding)
	}
	cipherText = strings.ReplaceAll(cipherText, "-", "+")
	cipherText = strings.ReplaceAll(cipherText, "_", "/")

	// base64解码
	cipherBytes, err := base64.StdEncoding.DecodeString(cipherText)
	if err != nil {
		return 0, err
	}

	slen := len(cipherBytes)
	klen := len(key)

	plainBytes := []byte{}

	for i := 0; i < slen; i += klen {
		end := i + klen
		if end > slen {
			end = slen
		}
		subBytes := cipherBytes[i:end]
		for j := 0; j < len(subBytes); j++ {
			plainBytes = append(plainBytes, key[j]^subBytes[j])
		}
	}

	// 16进制转10进制
	var uid int64
	_, err = fmt.Sscanf(string(plainBytes), "%x", &uid)
	if err != nil {
		return 0, err
	}

	return uid, nil
}

// desensitizeString 脱敏字符串函数
// str 字符串
// charLength 替换*的长度，默认6
// isMiddle 是否居中脱敏，true居中，false两端保留脱敏中间星号
func DesensitizeString(str string, charLength int, isMiddle bool) string {
	if str == "" {
		return ""
	}

	// 将字符串转rune切片，保证支持多字节字符串
	runes := []rune(str)
	strLen := len(runes)
	starStr := strings.Repeat("*", charLength)

	if isMiddle {
		var start, length int

		if strLen > charLength {
			start = int(math.Floor(float64(strLen-charLength) / 2.0))
			length = charLength
		} else {
			switch strLen {
			case 1:
				start = 0
				length = 1
			case 2:
				start = 0
				length = 1
			case 3:
				start = 1
				length = 1
			case 4:
				start = 1
				length = 2
			case 5:
				start = 1
				length = 3
			case 6:
				start = 1
				length = 4
			default:
				start = 0
				length = 0
			}
		}

		// 取脱敏替换部分原始字符串段（用星号替代）
		var before string
		if start > 0 {
			before = string(runes[:start])
		}
		var after string
		if start+length < strLen {
			after = string(runes[start+length:])
		}

		return before + starStr + after
	} else {
		var beforeStart, beforeLength, afterStart, afterLength int

		if strLen > charLength {
			if charLength*2 >= strLen {
				charLength = charLength / 2
			}
			beforeStart = 0
			beforeLength = charLength
			afterStart = strLen - charLength
			afterLength = charLength
		} else {
			switch strLen {
			case 1:
				beforeStart = 0
				beforeLength = 1
				afterStart = 0
				afterLength = 1
			case 2:
				beforeStart = 0
				beforeLength = 1
				afterStart = 1
				afterLength = 1
			case 3:
				beforeStart = 0
				beforeLength = 1
				afterStart = 2
				afterLength = 1
			case 4:
				beforeStart = 0
				beforeLength = 1
				afterStart = 3
				afterLength = 1
			case 5:
				beforeStart = 0
				beforeLength = 2
				afterStart = 3
				afterLength = 2
			case 6:
				beforeStart = 0
				beforeLength = 2
				afterStart = 4
				afterLength = 2
			default:
				beforeStart = 0
				beforeLength = 3
				afterStart = strLen - 3
				afterLength = 3
			}
		}

		beforeStr := ""
		if beforeLength > 0 {
			beforeStr = string(runes[beforeStart : beforeStart+beforeLength])
		}
		afterStr := ""
		if afterLength > 0 {
			afterStr = string(runes[afterStart : afterStart+afterLength])
		}

		return beforeStr + starStr + afterStr
	}
}

// SecureRandomInt 生成安全的随机数.
func SecureRandomInt(max int64) (int64, error) {
	n, err := rand.Int(rand.Reader, big.NewInt(max))
	if err != nil {
		return 0, err
	}
	return n.Int64(), nil
}

// CopyMap 复制一个map[string]interface{}类型的map.
func CopyMap(original map[string]interface{}) map[string]interface{} {
	// 创建一个新的map
	copied := make(map[string]interface{}, len(original))
	// 逐个复制原map中的键值对
	for key, value := range original {
		copied[key] = value
	}
	return copied
}

func InStringSlice(slice []string, val string) bool {
	for _, item := range slice {
		if item == val {
			return true
		}
	}
	return false
}

// CheckDev 是否为测试环境
func CheckDev() bool {
	if environment.IsProd() || environment.IsPre() {
		return false
	}
	return true
}

// UnifyTopic 获取对应环境的topic名称
func UnifyTopic(topic string) string {
	if environment.IsPre() {
		topic = fmt.Sprintf("%s-Pre", topic)
	}
	return topic
}

// InitLocalConfig 本地开发初始化配置.
func InitLocalConfig() {
	_ = godotenv.Load()
}
