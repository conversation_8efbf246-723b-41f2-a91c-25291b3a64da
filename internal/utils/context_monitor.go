package utils

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logc"
)

// ContextMonitor Context监控器
type ContextMonitor struct {
	mu       sync.RWMutex
	contexts map[string]*ContextInfo
}

// ContextInfo Context信息
type ContextInfo struct {
	ID          string
	Operation   string
	StartTime   time.Time
	Timeout     time.Duration
	Caller      string
	Status      string
	LastUpdate  time.Time
}

var (
	globalMonitor *ContextMonitor
	once          sync.Once
)

// GetContextMonitor 获取全局Context监控器
func GetContextMonitor() *ContextMonitor {
	once.Do(func() {
		globalMonitor = &ContextMonitor{
			contexts: make(map[string]*ContextInfo),
		}
		// 启动清理协程
		go globalMonitor.cleanup()
	})
	return globalMonitor
}

// RegisterContext 注册Context
func (cm *ContextMonitor) RegisterContext(ctx context.Context, operation string, timeout time.Duration) string {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	id := fmt.Sprintf("%p", ctx)
	caller := getCaller(2)
	
	info := &ContextInfo{
		ID:         id,
		Operation:  operation,
		StartTime:  time.Now(),
		Timeout:    timeout,
		Caller:     caller,
		Status:     "active",
		LastUpdate: time.Now(),
	}
	
	cm.contexts[id] = info
	
	logc.Infof(ctx, "Context registered: %s [%s] timeout=%v caller=%s", id, operation, timeout, caller)
	return id
}

// UpdateContextStatus 更新Context状态
func (cm *ContextMonitor) UpdateContextStatus(ctx context.Context, id, status string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if info, exists := cm.contexts[id]; exists {
		info.Status = status
		info.LastUpdate = time.Now()
		logc.Infof(ctx, "Context status updated: %s [%s] -> %s", id, info.Operation, status)
	}
}

// UnregisterContext 注销Context
func (cm *ContextMonitor) UnregisterContext(ctx context.Context, id string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if info, exists := cm.contexts[id]; exists {
		duration := time.Since(info.StartTime)
		logc.Infof(ctx, "Context unregistered: %s [%s] duration=%v", id, info.Operation, duration)
		delete(cm.contexts, id)
	}
}

// GetActiveContexts 获取活跃的Context列表
func (cm *ContextMonitor) GetActiveContexts() []*ContextInfo {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	var active []*ContextInfo
	for _, info := range cm.contexts {
		if info.Status == "active" {
			active = append(active, info)
		}
	}
	return active
}

// GetTimeoutContexts 获取超时的Context列表
func (cm *ContextMonitor) GetTimeoutContexts() []*ContextInfo {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	var timeouts []*ContextInfo
	now := time.Now()
	
	for _, info := range cm.contexts {
		if info.Status == "active" && now.Sub(info.StartTime) > info.Timeout {
			timeouts = append(timeouts, info)
		}
	}
	return timeouts
}

// LogContextStats 记录Context统计信息
func (cm *ContextMonitor) LogContextStats(ctx context.Context) {
	active := cm.GetActiveContexts()
	timeouts := cm.GetTimeoutContexts()
	
	logc.Infof(ctx, "Context Stats - Active: %d, Timeouts: %d", len(active), len(timeouts))
	
	if len(timeouts) > 0 {
		logc.Warnf(ctx, "Timeout contexts detected:")
		for _, info := range timeouts {
			elapsed := time.Since(info.StartTime)
			logc.Warnf(ctx, "  - %s [%s] elapsed=%v timeout=%v caller=%s", 
				info.ID, info.Operation, elapsed, info.Timeout, info.Caller)
		}
	}
}

// cleanup 清理过期的Context信息
func (cm *ContextMonitor) cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		cm.mu.Lock()
		now := time.Now()
		var toDelete []string
		
		for id, info := range cm.contexts {
			// 清理超过1小时的记录
			if now.Sub(info.LastUpdate) > time.Hour {
				toDelete = append(toDelete, id)
			}
		}
		
		for _, id := range toDelete {
			delete(cm.contexts, id)
		}
		cm.mu.Unlock()
		
		if len(toDelete) > 0 {
			fmt.Printf("Cleaned up %d expired context records\n", len(toDelete))
		}
	}
}

// getCaller 获取调用者信息
func getCaller(skip int) string {
	_, file, line, ok := runtime.Caller(skip)
	if !ok {
		return "unknown"
	}
	return fmt.Sprintf("%s:%d", file, line)
}

// MonitoredContext 带监控的Context包装器
type MonitoredContext struct {
	context.Context
	id      string
	monitor *ContextMonitor
}

// NewMonitoredContext 创建带监控的Context
func NewMonitoredContext(parent context.Context, operation string, timeout time.Duration) (*MonitoredContext, context.CancelFunc) {
	ctx, cancel := context.WithTimeout(parent, timeout)
	monitor := GetContextMonitor()
	id := monitor.RegisterContext(ctx, operation, timeout)
	
	monitoredCtx := &MonitoredContext{
		Context: ctx,
		id:      id,
		monitor: monitor,
	}
	
	// 包装cancel函数
	wrappedCancel := func() {
		monitor.UpdateContextStatus(ctx, id, "canceled")
		cancel()
		monitor.UnregisterContext(ctx, id)
	}
	
	return monitoredCtx, wrappedCancel
}

// Done 重写Done方法，添加监控
func (mc *MonitoredContext) Done() <-chan struct{} {
	done := mc.Context.Done()
	
	// 启动监控协程
	go func() {
		<-done
		if mc.Context.Err() == context.DeadlineExceeded {
			mc.monitor.UpdateContextStatus(mc.Context, mc.id, "timeout")
		} else if mc.Context.Err() == context.Canceled {
			mc.monitor.UpdateContextStatus(mc.Context, mc.id, "canceled")
		}
	}()
	
	return done
}
