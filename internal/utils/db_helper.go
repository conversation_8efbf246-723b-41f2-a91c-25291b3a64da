package utils

import (
	"context"
	"errors"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"gorm.io/gorm"
)

// DBOperationConfig 数据库操作配置
type DBOperationConfig struct {
	Timeout     time.Duration
	Operation   string
	RetryCount  int
	RetryDelay  time.Duration
}

// DefaultDBConfig 默认数据库操作配置
func DefaultDBConfig(operation string) *DBOperationConfig {
	return &DBOperationConfig{
		Timeout:    30 * time.Second,
		Operation:  operation,
		RetryCount: 3,
		RetryDelay: 100 * time.Millisecond,
	}
}

// SafeDBOperation 安全执行数据库操作，带超时和重试
func SafeDBOperation(ctx context.Context, db *gorm.DB, config *DBOperationConfig, fn func(*gorm.DB) error) error {
	if config == nil {
		config = DefaultDBConfig("unknown")
	}

	// 创建带超时的context
	opCtx, cancel := context.WithTimeout(ctx, config.Timeout)
	defer cancel()

	var lastErr error
	for i := 0; i <= config.RetryCount; i++ {
		// 检查context是否已取消
		if IsContextCanceled(opCtx) {
			logc.Warnf(ctx, "DB operation [%s] canceled before attempt %d", config.Operation, i+1)
			return context.Canceled
		}

		// 执行数据库操作
		err := fn(db.WithContext(opCtx))
		if err == nil {
			if i > 0 {
				logc.Infof(ctx, "DB operation [%s] succeeded on retry %d", config.Operation, i)
			}
			return nil
		}

		lastErr = err
		
		// 检查是否是context相关错误
		if errors.Is(err, context.Canceled) || errors.Is(err, context.DeadlineExceeded) {
			logc.Warnf(ctx, "DB operation [%s] failed due to context: %v", config.Operation, err)
			return err
		}

		// 如果不是最后一次重试，等待后重试
		if i < config.RetryCount {
			logc.Warnf(ctx, "DB operation [%s] failed on attempt %d, retrying: %v", config.Operation, i+1, err)
			select {
			case <-time.After(config.RetryDelay):
				// 继续重试
			case <-opCtx.Done():
				return opCtx.Err()
			}
		}
	}

	logc.Errorf(ctx, "DB operation [%s] failed after %d attempts: %v", config.Operation, config.RetryCount+1, lastErr)
	return lastErr
}

// SafeDBQuery 安全执行数据库查询
func SafeDBQuery(ctx context.Context, db *gorm.DB, operation string, fn func(*gorm.DB) error) error {
	config := DefaultDBConfig(operation)
	config.Timeout = 15 * time.Second // 查询操作使用较短超时
	return SafeDBOperation(ctx, db, config, fn)
}

// SafeDBWrite 安全执行数据库写操作
func SafeDBWrite(ctx context.Context, db *gorm.DB, operation string, fn func(*gorm.DB) error) error {
	config := DefaultDBConfig(operation)
	config.Timeout = 30 * time.Second // 写操作使用较长超时
	return SafeDBOperation(ctx, db, config, fn)
}

// SafeDBTransaction 安全执行数据库事务
func SafeDBTransaction(ctx context.Context, db *gorm.DB, operation string, fn func(*gorm.DB) error) error {
	config := DefaultDBConfig(operation)
	config.Timeout = 60 * time.Second // 事务使用更长超时
	config.RetryCount = 1 // 事务通常不重试
	
	return SafeDBOperation(ctx, db, config, func(db *gorm.DB) error {
		return db.Transaction(fn)
	})
}

// CheckDBHealth 检查数据库连接健康状态
func CheckDBHealth(ctx context.Context, db *gorm.DB) error {
	return SafeDBQuery(ctx, db, "health_check", func(db *gorm.DB) error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.PingContext(ctx)
	})
}

// LogDBError 记录数据库错误
func LogDBError(ctx context.Context, operation string, err error) {
	if err == nil {
		return
	}
	
	if errors.Is(err, context.Canceled) {
		logc.Warnf(ctx, "DB operation [%s] was canceled", operation)
	} else if errors.Is(err, context.DeadlineExceeded) {
		logc.Errorf(ctx, "DB operation [%s] timeout", operation)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		logc.Infof(ctx, "DB operation [%s] record not found", operation)
	} else {
		logc.Errorf(ctx, "DB operation [%s] failed: %v", operation, err)
	}
}
