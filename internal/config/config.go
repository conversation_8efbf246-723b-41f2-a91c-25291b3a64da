package config

import (
	"bitbucket.org/gatebackend/go-zero/core/stores/redis"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"fmt"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/conf"
	"bitbucket.org/gatebackend/go-zero/core/service"

	"bitbucket.org/gatebackend/go-zero/rest"
	"bitbucket.org/gatebackend/gorm-zero/gormc/config/mysql"

	"bitbucket.org/gateio/gateio-lib-base-go/gconfig"
)

type Config struct {
	// Nacos 配置, 由 lib-base-go/gconfig 来管理, 项目启动时会从环境变量中加载
	// nacos 的连接信息, 之后从 nacos server 中拉取配置并加载到 Config 对象上.
	Nacos gconfig.GateNacosConfig `json:",optional,omitempty"`

	// 项目的基础配置: 项目名, log, dev server 等
	Basic service.ServiceConf `json:",optional,omitempty"`

	// OuterServiceConf 用来声明外部服务(3460 端口)的特有配置, 比如外部服务需要
	// 使用的 Mysql, Redis 等配置
	OuterServiceConf OuterServiceConf `json:",optional,omitempty"`
	// InnerServiceConf 和 OutServiceConf 同理, 通过这两个配置将内部和外部服务
	// 区分开来
	InnerServiceConf       InnerServiceConf `json:",optional,omitempty"`
	MysqlDefaultWelfare    mysql.Conf       `json:",optional,omitempty"`
	MysqlGrowthDataWelfare mysql.Conf       `json:",optional,omitempty"`
	MysqlFeeSettings       mysql.Conf       `json:",optional,omitempty"`
	// welfare数据库配置
	//MysqlWelfareDB mysql.Conf `json:",optional,omitempty"`

	// Redis配置
	Redis redis.RedisConf `json:",optional,omitempty"`
	// Kafka相关配置
	KafkaConf *KafkaConf `json:",optional,omitempty"`
	// 用户中心kafkap配置
	UserCenterKafkaConf *KafkaConf `json:",optional,omitempty"`
	//增加风控调用连接
	RiskServiceUrl   string `json:",optional,omitempty"`
	RiskGatewayAppId string `json:",optional,omitempty"`
	// 使用的 Mysql, Redis 等配置
	// Redis配置
	// XXXjob配置
	XXLJob xxljob.Conf `json:",optional,omitempty"`
}

func (c *Config) UnmarshalJSON(src []byte) error {
	return conf.LoadFromJsonBytes(src, c)
}

type OuterServiceConf struct {
	rest.RestConf

	// 在下面添加外部服务需要使用的配置, 并删除该注释
}

type InnerServiceConf struct {
	rest.RestConf

	// 在下面添加外部服务需要使用的配置, 并删除该注释
}

type KafkaConf struct {
	Brokers       []string      `json:",omitempty"`
	FetchMaxBytes int           `json:",omitempty"`
	FetchMinBytes int           `json:",omitempty"`
	FetchMaxWait  time.Duration `json:",omitempty"`
}

const gconfigKey = "default"

func LoadConfigFromNacos(config gconfig.GateNacosConfig) (*Config, error) {
	err := gconfig.AddNacosConf(config)
	if err != nil {
		return nil, fmt.Errorf("load config from nacos: %w", err)
	}

	c, err := GetConfig()
	if err != nil {
		return nil, err
	}
	return c, c.Basic.SetUp()
}

func GetConfig() (*Config, error) {
	var config Config
	err := gconfig.Scan(gconfigKey, &config)
	if err != nil {
		return nil, err
	}
	return standardize(&config), err
}

func standardize(config *Config) *Config {
	if len(config.OuterServiceConf.Name) == 0 {
		config.OuterServiceConf.Name = config.Basic.Name + "-outer" // for log, trace, metrics...
	}
	if len(config.InnerServiceConf.Name) == 0 {
		config.OuterServiceConf.Name = config.Basic.Name + "-inner"
	}
	if config.OuterServiceConf.Host == "" {
		config.OuterServiceConf.Host = "0.0.0.0"
	}
	if config.InnerServiceConf.Host == "" {
		config.InnerServiceConf.Host = "0.0.0.0"
	}
	if config.OuterServiceConf.Port == 0 {
		config.OuterServiceConf.Port = 3460
	}
	if config.InnerServiceConf.Port == 0 {
		config.InnerServiceConf.Port = 9580
	}
	if len(config.InnerServiceConf.Telemetry.Endpoint) > 0 {
		fmt.Println("[WARNING]telemetry 应该通过 Basic.Telemetry 来配置, 而不是 InnerServiceConf.Telemetry")
	}
	if len(config.OuterServiceConf.Telemetry.Endpoint) > 0 {
		fmt.Println("[WARNING]telemetry 应该通过 Basic.Telemetry 来配置, 而不是 OuterServiceConf.Telemetry")
	}
	config.OuterServiceConf.Telemetry.Disabled = true
	config.InnerServiceConf.Telemetry.Disabled = true
	return config
}
