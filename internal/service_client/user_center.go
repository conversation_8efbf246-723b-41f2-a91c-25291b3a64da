package service_client

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/usercenter"
	"context"
	"github.com/pkg/errors"
)

type UserCenterCall struct {
	logx.Logger
	ctx context.Context
}

var userCenterCall *UserCenterCall

func NewUserCenterCall(ctx context.Context) *UserCenterCall {
	return &UserCenterCall{Logger: logx.WithContext(ctx), ctx: ctx}
}

func (uc *UserCenterCall) GetUserInfo(uid int64) (*usercenter.UserInfoResponse, error) {
	req := usercenter.GetUserInfoRequest{
		UserID: uid,
	}
	userInfo, err := usercenter.NewClient().GetUserInfo(uc.ctx, &req)
	if err != nil {
		uc.Logger.Errorf("UserCenterCall GetUserInfo failed, err: %v", err)
		return nil, err
	}

	return userInfo, nil
}

// 获取用户列表
func (uc *UserCenterCall) GetUserList(uid []int64) ([]usercenter.UserDetail, error) {
	req := usercenter.GetUserListRequest{
		UID: uid,
	}
	resp, err := usercenter.NewClient().GetUserList(uc.ctx, &req)
	if err != nil {
		uc.Logger.Errorf("UserCenterCall GetUserList failed, err: %v", err)
		return nil, err
	}
	if resp == nil {
		uc.Logger.Errorf("UserCenterCall GetUserList failed, err: %s", "resp is nil")
		return nil, errors.New("resp is nil")
	}
	return resp.List, nil
}

// 获取kyc详情
func (uc *UserCenterCall) GetUserKYCInfo(uid int64) (*usercenter.UserKYCInfoResponse, error) {
	req := usercenter.GetUserKYCInfoRequest{
		UserID: uid,
	}
	resp, err := usercenter.NewClient().GetUserKYCInfo(uc.ctx, req)
	if err != nil {
		uc.Logger.Errorf("UserCenterCall GetUserKYCInfo failed, err: %v", err)
		return nil, err
	}
	if resp == nil {
		uc.Logger.Errorf("UserCenterCall GetUserKYCInfo failed, err: %s", "resp is nil")
		return nil, errors.New("resp is nil")
	}
	return resp, nil
}

// 修改VIP等级
func (uc *UserCenterCall) UpdateUserVIP(uid, tier, typeVal, validDays, expireTime int, symbol, name string) error {
	req := &usercenter.UpdateUserVIPRequest{
		UID:        uid,
		Tier:       tier,
		Type:       typeVal,
		ValidDays:  validDays,
		ExpireTime: expireTime,
		Symbol:     symbol,
		Name:       name,
	}
	err := usercenter.NewClient().UpdateUserVIP(uc.ctx, req)
	if err != nil {
		uc.Logger.Errorf("UserCenterCall UpdateUserVIP failed, err: %v", err)
		return err
	}
	return nil
}
