package service_client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"

	"bitbucket.org/gatebackend/go-zero/core/logc"
)

type CheckRiskReq struct {
	AppId     string                 `json:"appId"`
	EventCode string                 `json:"eventCode"`
	Flag      string                 `json:"flag"`
	Data      map[string]interface{} `json:"data"`
}

type CheckRiskData struct {
	DevopsEnvName string `json:"devops_env_name"`
	DevopsRole    string `json:"devops_role"`
	UserId        string `json:"user_id"`
	Ip            string `json:"ip"`
	ConstId       string `json:"const_id"`
}

type CheckRiskResponse struct {
	UUID   string `json:"uuid"`
	Status string `json:"status"`
	Result struct {
		HandleFlag      interface{} `json:"handleFlag"`
		RiskLevel       string      `json:"riskLevel"`
		RiskType        string      `json:"riskType"`
		HitPolicyCode   interface{} `json:"hitPolicyCode"`
		HitPolicyName   interface{} `json:"hitPolicyName"`
		HitRules        []HitRule   `json:"hitRules"`
		SuggestPolicies interface{} `json:"suggestPolicies"`
		Suggestion      interface{} `json:"suggestion"`
		Flag            string      `json:"flag"`
		ExtraInfo       struct {
			ObserveMode        []bool `json:"_observe_mode"`
			CostTime           int    `json:"_cost_time"`
			RuleIdCodeNamePair string `json:"ruleIdCodeNamePair"`
		} `json:"extraInfo"`
		NameListJson  interface{} `json:"nameListJson"`
		PolicyScore   interface{} `json:"policyScore"`
		NameListField interface{} `json:"nameListField"`
	} `json:"result"`
}

type HitRule struct {
	ID        int64       `json:"id"`
	LeftValue interface{} `json:"leftValue"`
}

func InnerCheckRisk(svcCtx *svc.ServiceContext, ctx context.Context, param *CheckRiskReq) (*CheckRiskResponse, error) {
	paramBytes, _ := json.Marshal(param)
	// 对于东京AWS生产环境，需要与OPS沟通，使用对应的private link端点地址。注意是http还是https
	url := fmt.Sprintf("%s/risk/inner/checkRisk", svcCtx.RiskServiceUrl)

	// 创建新的 HTTP 请求，使用context
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(paramBytes))
	if err != nil {
		logc.Errorf(ctx, "InnerCheckRisk NewRequestWithContext failed: %v", err)
		return nil, err
	}

	// 设置Header。注意不要写错了
	req.Host = "risk-gateway.gatexsecurity.com"
	req.Header.Set("Content-Type", "application/json")

	// 创建 HTTP 客户端，使用工具函数
	var client *http.Client
	if utils.CheckDev() {
		// 开发环境跳过证书验证
		client = utils.NewDevHTTPClient(30 * time.Second)
	} else {
		// 生产环境使用默认配置
		config := utils.DefaultHTTPClientConfig()
		config.Timeout = 30 * time.Second
		client = utils.NewHTTPClient(config)
	}
	resp, err := client.Do(req)
	if err != nil {
		logc.Infof(ctx, "InnerCheckRisk sending request is err,err is:%v", err)
		return nil, err
	}
	defer resp.Body.Close()
	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logc.Warnf(ctx, "InnerCheckRisk ioutil.ReadAll is err,err is:%v", err)
		return nil, err
	}
	// 打印响应
	logc.Infof(ctx, "InnerCheckRisk bady is:%s", string(body))
	// 打印响应状态码
	fmt.Println("Response status:", resp.Status)
	dataResp := &CheckRiskResponse{}
	err = json.Unmarshal(body, dataResp)
	if err != nil {
		logc.Errorf(ctx, "InnerCheckRisk json.Unmarshal is err,err is:%v", err)
		return nil, err
	}
	return dataResp, nil
}
