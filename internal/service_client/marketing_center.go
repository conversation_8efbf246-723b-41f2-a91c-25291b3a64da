package service_client

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/marketing"
	"context"
	"errors"
)

type MarketingCenterCall struct {
	logx.Logger
	ctx context.Context
}

var marketingCenterCall *MarketingCenterCall

func NewMarketingCenterCall(ctx context.Context) *MarketingCenterCall {
	return &MarketingCenterCall{Logger: logx.WithContext(ctx), ctx: ctx}
}

// 验证用户是否在人群中
func (mc *MarketingCenterCall) CheckUserInCrowd(uid, crowdID int64) (*marketing.CheckUserInCrowdResponse, error) {
	req := &marketing.CheckUserInCrowdRequest{
		UserID:  uid,
		CrowdID: crowdID,
	}
	resp, err := marketing.NewClient().CheckUserInCrowd(mc.ctx, req)
	if err != nil {
		mc.<PERSON><PERSON>.<PERSON><PERSON>("MarketingCenterCall CheckUserInCrowd failed, err: %v", err)
		return nil, err
	}
	if resp == nil {
		mc.<PERSON><PERSON>.<PERSON>("MarketingCenterCall CheckUserInCrowd failed, err: %s", "resp is nil")
		return nil, errors.New("resp is nil")
	}
	return resp, nil
}
