package service_client

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/coupon"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/utils"
)

type CouponCenterCall struct {
	logx.Logger
	ctx context.Context
}

func NewCouponCenterCall(ctx context.Context) *CouponCenterCall {
	return &CouponCenterCall{Logger: logx.WithContext(ctx), ctx: ctx}
}

// 发放卡劵奖励
func (cc *CouponCenterCall) SendCouponById(uid, couponId int64, requestID, amount, source string) (resp *coupon.SendCouponByIdResponse, err error) {
	date := utils.TimeUtil{}
	milliTime := date.GetNowMilliTime(date.Now())
	req := coupon.SendCouponByIdRequest{
		UserID:    uid,
		CouponID:  couponId,
		Source:    source,
		OnlyID:    fmt.Sprintf("%d_%d", uid, milliTime),
		RequestID: fmt.Sprintf("%d_%s", uid, requestID),
		Amount:    amount,
	}
	resp = &coupon.SendCouponByIdResponse{}
	reqBytes, _ := json.Marshal(req)
	cc.Logger.Infof("SendCouponById req is: %v", string(reqBytes))
	resp, err = coupon.NewClient().SendCouponById(cc.ctx, &req)
	respBytes, _ := json.Marshal(resp)
	cc.Logger.Infof("SendCouponById resp is: %v", string(respBytes))

	return resp, err
}

// 查询卡劵发放进度
func (cc *CouponCenterCall) CheckSendCouponProgress(sourceVal, requestID string) (resp *coupon.CheckSendCouponProgressResponse, err error) {
	//获取卡劵发奖进度
	req := coupon.CheckSendCouponProgressRequest{
		Source:    sourceVal,
		RequestID: requestID,
	}
	resp = &coupon.CheckSendCouponProgressResponse{}
	reqBytes, _ := json.Marshal(req)
	cc.Logger.Infof("CheckSendCouponProgress req is: %v", string(reqBytes))
	resp, err = coupon.NewClient().CheckSendCouponProgress(cc.ctx, &req)
	respBytes, _ := json.Marshal(resp)
	cc.Logger.Infof("CheckSendCouponProgress resp is: %v", string(respBytes))
	return resp, err
}
