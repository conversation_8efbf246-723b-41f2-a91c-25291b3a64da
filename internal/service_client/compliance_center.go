package service_client

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-ip-go/ip2location"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/compliance"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"os"
)

type ComplianceCenterCall struct {
	logx.Logger
	ctx context.Context
	svc *svc.ServiceContext
}

func NewComplianceCenterCall(ctx context.Context) *ComplianceCenterCall {
	return &ComplianceCenterCall{Logger: logx.WithContext(ctx), ctx: ctx}
}

// 合规接口
func (ccc *ComplianceCenterCall) CheckLimit(country int64, r *http.Request, svc *svc.ServiceContext) (*compliance.CheckLimitResp, error) {
	var (
		subWebsiteType     = 0
		ipAddress          = ""
		ip                 = ""
		ipCountryId        = country
		language           = "en"
		vipLevel           = "-1"
		uid                = -1
		kycLevel           = -1
		residenceCountryId = "0"
		regTime            = 0
		userType           = 0
	)

	if country == 0 && r == nil {
		return nil, errors.New("invalid request")
	}

	if country == 0 {
		userInfo := requestools.GetUserInfo(r)
		if userInfo == nil {
			return nil, errors.New("auth info error")
		}

		country = int64(utils.GetUserCountry(r))
		subWebsiteType = requestools.DetectSite(r)

		ip = requestools.GetClientIP(r)

		ipInfo, _ := ip2location.GetDetailByIP(ip)
		ipAddress = ipInfo.IPAddress
		ipCountryId = int64(ipInfo.CountryCode)

		language = requestools.GetUserLanguage(r)
		vipLevel = userInfo.Tier
		uid = userInfo.UID
		kycLevel = userInfo.Verified
		residenceCountryId = userInfo.ResidenceCountryID
		regTime = int(userInfo.RegTimest)
		userType = userInfo.Type

	}

	redisKey := fmt.Sprintf(consts.ComplianceCheckLimitKey, uid, country)
	cached, err := svc.Redis.Get(redisKey)
	if err == nil && cached != "" {
		var response compliance.CheckLimitResp
		if err := json.Unmarshal([]byte(cached), &response); err == nil {
			return &response, nil
		} else {
			logx.Warnf("compliance json unmarshal failed: %v", err)
		}
	}

	params := &compliance.CheckLimitReq{
		KycCountryID:       country,
		Lang:               language,
		VipLevel:           utils.MustInt64(vipLevel),
		UserID:             int64(uid),
		KycLevel:           int64(kycLevel),
		IPCountryID:        ipCountryId,
		ResidenceCountryID: utils.MustInt64(residenceCountryId),
		SubWebsiteID:       int64(subWebsiteType),
		RegTime:            int64(regTime),
		UserType:           int64(userType),
		IPAddress:          ipAddress,
		IP:                 ip,
		RequestURI:         "",
		Debug:              0,
		ServerName:         os.Getenv("APP_NAME"),
	}
	//jsonParamsStr, _ := json.Marshal(params)
	//ccc.Logger.Infof("Compliance CheckLimit req is: %v", string(jsonParamsStr))
	client := compliance.NewClient()
	checkLimitResp, err := client.CheckLimit(ccc.ctx, params)
	if err != nil {
		return nil, err
	}
	jsonStr, _ := json.Marshal(checkLimitResp)
	//ccc.Logger.Infof("Compliance CheckLimit resp is: %v", string(jsonStr))
	err = svc.Redis.Setex(redisKey, string(jsonStr), 60*60)
	if err != nil {
		return nil, err
	}

	return checkLimitResp, nil
}
