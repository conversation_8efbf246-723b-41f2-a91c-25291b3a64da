package service_client

import (
	"context"
	"errors"

	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
)

type TaskCenterCall struct {
	logx.Logger
	ctx context.Context
}

var taskCenterCall *TaskCenterCall

func NewTaskCenterCall(ctx context.Context) *TaskCenterCall {
	return &TaskCenterCall{Logger: logx.WithContext(ctx), ctx: ctx}
}

// 批量获取任务详细信息
func (tc *TaskCenterCall) BatchTaskAll(uid, businessType int64, businessID, taskIDs string) (*task.BatchTaskAllResponse, error) {
	req := &task.BatchTaskAllRequest{
		UserID:       uid,
		TaskIDs:      taskIDs,
		BusinessType: businessType,
		BusinessID:   businessID,
	}
	resp, err := task.NewClient().BatchTaskAll(tc.ctx, req)
	if err != nil {
		tc.Logger.Errorf("TaskCenterCall BatchTaskAll failed, err: %v", err)
		return nil, err
	}
	if resp == nil {
		tc.Logger.Errorf("TaskCenterCall BatchTaskAll failed, err: %s", "resp is nil")
		return nil, errors.New("resp is nil")
	}
	return resp, nil
}

// 批量获取任务详细信息
func (tc *TaskCenterCall) RecordSchedule(uid, taskId, businessType int64, businessId string) (*task.RecordScheduleResponse, error) {
	req := &task.RecordScheduleRequest{
		UserID:       uid,
		TaskID:       taskId,
		BusinessType: businessType,
		BusinessID:   businessId,
	}
	resp, err := task.NewClient().RecordSchedule(tc.ctx, req)
	if err != nil {
		tc.Logger.Errorf("TaskCenterCall RecordSchedule failed, err: %v", err)
		return nil, err
	}
	if resp == nil {
		tc.Logger.Errorf("TaskCenterCall RecordSchedule failed, err: %s", "resp is nil")
		return nil, errors.New("resp is nil")
	}
	return resp, nil
}

// 批量获取任务详细Detail
func (tc *TaskCenterCall) BatchTaskDetail(taskIDs string) (*task.BatchTaskDetailResponse, error) {
	req := &task.BatchTaskDetailRequest{
		TaskIDs: taskIDs,
	}
	resp, err := task.NewClient().BatchTaskDetail(tc.ctx, req)
	if err != nil {
		tc.Logger.Errorf("TaskCenterCall BatchTaskDetail failed, err: %v", err)
		return nil, err
	}
	if resp == nil {
		tc.Logger.Errorf("TaskCenterCall BatchTaskDetail failed, err: %s", "resp is nil")
		return nil, errors.New("resp is nil")
	}
	return resp, nil
}

// TaskCenterClient 继承 ServiceCallClient
type TaskCenterClient struct {
	*servicecall.Client
}
