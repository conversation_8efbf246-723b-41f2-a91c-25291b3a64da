package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/engine/auth"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-ip-go/ip2location"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/usercenter"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
)

// 根据ip获取国家信息
func GetClientCountry(r *http.Request, ctx context.Context) (int, error) {
	ip := requestools.GetClientIP(r)
	logc.Info(ctx, "GetClientCountry requestools.GetClientIP get IP is: ", ip)
	detail, err := ip2location.GetDetailByIP(ip)
	if err != nil {
		logc.Infof(ctx, "user_service ip2location.GetDetailByIP  is err, ip is:%s,err is:%v", ip, err)
		return 0, err
	}
	if detail != nil {
		return detail.CountryCode, nil
	}
	logc.Infof(ctx, "user_service ip2location.GetDetailByIP  detail is nil, ip is:%s", ip)
	return 0, nil
}

/**
 * @brief 获取用户信息
 * @param uid 用户ID
 * @return 用户信息
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws \Psr\Container\NotFoundExceptionInterface
 */
func GetCenterUserInfoByUid(svcCtx *svc.ServiceContext, ctx context.Context, uid int64) (*usercenter.UserInfoResponse, error) {
	userInfo := &usercenter.UserInfoResponse{}
	userKey := fmt.Sprintf(consts.NewbieTaskUserInfoKey, uid)
	cacheData, _ := svcCtx.Redis.Get(userKey)
	if cacheData != "" {
		_ = json.Unmarshal([]byte(cacheData), userInfo)
	}
	if userInfo.UID <= 0 {
		var err error
		userCenter := service_client.NewUserCenterCall(ctx)
		userInfo, err = userCenter.GetUserInfo(uid)
		if err != nil {
			logc.Infof(ctx, "user_service GetCenterUserInfoByUid userCenter.GetUserInfo is err, uid is:%d,err is:%v", uid, err)
			return nil, err
		}
		if userInfo.UID <= 0 {
			logc.Infof(ctx, "user_service GetCenterUserInfoByUid userCenter.GetUserInfo is err, uid is:%d,err is:%s", uid, "len(userList) is zero")
			return nil, err
		}
		userCacheData, err := json.Marshal(userInfo)
		if err != nil {
			logc.Infof(ctx, "user_service GetCenterUserInfoByUid json.Marshal is err, uid is:%d,err is:%v", uid, err)
			return userInfo, err
		}
		_ = svcCtx.Redis.Setex(userKey, string(userCacheData), 86400)
	}
	if userInfo.UID <= 0 {
		logc.Infof(ctx, "user_service GetCenterUserInfoByUid userInfo is err, uid is:%d,err is:%v", 0, errors.New("userInfo not found"))
		return nil, errors.New("userInfo not found")
	}
	return userInfo, nil
}

// 验证用户是否在黑名单国家
func IsBlackCountry(svcCtx *svc.ServiceContext, ctx context.Context, registerCountryID, residenceCountryID int64) (int, error) {
	//获取国家黑名单信息
	blackCountryIdStr, err := GetWelfareConfig(svcCtx, ctx, consts.NewbieTaskBlackCountryList)
	if err != nil {
		return 0, err
	}
	blackCountryIds := utils.ParseList(blackCountryIdStr)
	if blackCountryIds == nil || len(blackCountryIds) <= 0 {
		return consts.IsBlackCountryNo, nil
	}
	if utils.ContainsArray(blackCountryIds, registerCountryID) || utils.ContainsArray(blackCountryIds, residenceCountryID) {
		return consts.IsBlackCountryYes, nil
	}
	return consts.IsBlackCountryNo, nil
}

func GetUserKycInfo(svcCtx *svc.ServiceContext, ctx context.Context, uid int64) (*usercenter.UserKYCInfoResponse, error) {
	cacheKey := fmt.Sprintf("user_kyc_info_%d", uid)

	var userKYCInfoResponse *usercenter.UserKYCInfoResponse
	cacheData, _ := svcCtx.Redis.Get(cacheKey)
	if cacheData != "" {
		_ = json.Unmarshal([]byte(cacheData), &userKYCInfoResponse)
		return userKYCInfoResponse, nil
	}

	userCenter := service_client.NewUserCenterCall(ctx)
	resp, err := userCenter.GetUserKYCInfo(uid)
	if err != nil {
		logc.Infof(ctx, "user_service userCenter.GetUserKYCInfo is err, uid is:%d,err is:%v", uid, err)
		return nil, err
	}
	if resp != nil {
		cacheDataByte, err := json.Marshal(resp)
		//当json序列化成功情况下再插入缓存
		if err == nil {
			_ = svcCtx.Redis.Setex(cacheKey, string(cacheDataByte), 600)
		}
	}
	return resp, nil
}

// 获取用户是否已经下载APP和登录过
func HasDownloadApp(svcCtx *svc.ServiceContext, ctx context.Context, uid string) (int, error) {
	cacheKey := fmt.Sprintf(consts.UserCenterLastReportInfoKey, uid)
	lastInfo := usercenter.GetAppLastReportInfo{}
	cacheData, _ := svcCtx.Redis.Get(cacheKey)
	if cacheData != "" {
		_ = json.Unmarshal([]byte(cacheData), &lastInfo)
	} else {
		req := &usercenter.GetAppLastReportInfosRequest{
			Uids: uid,
		}
		resp, err := usercenter.NewClient().GetAppLastReportInfos(ctx, req)
		if err != nil {
			logc.Infof(ctx, "user_service HasDownloadApp userCenter.GetAppLastReportInfos is err, uid is:%s,err is:%v", uid, err)
			return 0, err
		}
		if resp != nil {
			lastInfo = resp.Data[uid]
			cacheDataByte, err := json.Marshal(lastInfo)
			//当json序列化成功情况下再插入缓存
			if err == nil {
				//缓存30秒
				_ = svcCtx.Redis.Setex(cacheKey, string(cacheDataByte), 30)
			}
		}
	}
	if lastInfo.Uid > 0 {
		return consts.CommonYes, nil
	} else {
		return consts.CommonNo, nil
	}
}

// 根据name获取config配置
func GetWelfareConfig(svcCtx *svc.ServiceContext, ctx context.Context, name string) (string, error) {
	cacheKey := fmt.Sprintf(consts.WelfareConfigKey, name)
	cacheData, err := svcCtx.Redis.Get(cacheKey)
	if err != nil {
		logc.Errorf(ctx, "task_service GetWelfareConfig svcCtx.Redis.Get is err, key is:%s,err is:%v", cacheKey, err)
		return "", err
	}
	if cacheData == "" {
		welConfig, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetWelfareConfigByName(ctx, name)
		if err != nil {
			logc.Errorf(ctx, "task_service GetWelfareConfigByName WelfareConfigModel is err, params is:%s,err is:%v", name, err)
			return "", err
		}
		if welConfig != nil && welConfig.Config != "" {
			cacheData = welConfig.Config
			_ = svcCtx.Redis.Setex(cacheKey, cacheData, 600)
		}
	}
	return cacheData, nil
}

// 根据name设置config配置
func SetWelfareConfig(svcCtx *svc.ServiceContext, ctx context.Context, name, value string) (bool, error) {

	configId, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).SetWelfareConfig(ctx, name, value)
	if err != nil || configId <= 0 {
		logc.Errorf(ctx, "SetWelfareConfig is err:%v", err)
		return false, err
	}

	cacheKey := fmt.Sprintf(consts.WelfareConfigKey, name)
	utils.DelayDelRedisKey(svcCtx, cacheKey, 200, 3)

	return true, nil
}

func CheckUserIdentity(svc *svc.ServiceContext, ctx context.Context, userInfo *auth.UserInfo) (bool, int) {
	if userInfo == nil || userInfo.UID <= 0 {
		return false, consts.ErrUserLogin
	}

	uid := userInfo.UID

	mmUidList, err := dao.NewFeeSettingMysqlDBUtil(svc.FeeSettingDB).GetMmUidAry(svc, ctx)
	if err != nil {
		logx.Infof("GetUserIdentity GetMmUidAry is failed: %v, uid:%d", err, uid)
		return false, consts.ErrDbError
	}

	if utils.ContainsArray(mmUidList, int64(uid)) || userInfo.Verified == consts.KycLvCompany {
		return false, consts.ErrIdentityCompany
	}

	if userInfo.IsSub == 1 {
		return false, consts.ErrIdentitySubAccount
	}
	if userInfo.AgencyType == 3 {
		return false, consts.ErrIdentityAgentPartner
	}

	return true, consts.Success
}

func GetSystemSwitchByKey(svc *svc.ServiceContext, ctx context.Context, key string) (bool, error) {
	pointsSendSwitch, err := GetWelfareConfig(svc, ctx, key)
	if err != nil {
		return false, err
	}

	commonNo, _ := utils.TryString(consts.CommonNo)
	if pointsSendSwitch == commonNo {
		return false, nil
	}

	return true, nil
}
