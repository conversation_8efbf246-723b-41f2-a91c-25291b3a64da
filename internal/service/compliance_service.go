package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_points_shop"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
)

type ComplianceCenterCall struct {
	Logger logx.Logger
	ctx    context.Context
	svc    *svc.ServiceContext
}

func NewComplianceCenterCall(ctx context.Context, svc *svc.ServiceContext) *ComplianceCenterCall {
	return &ComplianceCenterCall{Logger: logx.WithContext(ctx), ctx: ctx, svc: svc}
}

func (cc *ComplianceCenterCall) GetComplianceTaskByKey(country int, r *http.Request, taskTypeMenu int, taskCfgs []*welfare_task_cfgs.WelfareTaskCfgs) ([]*welfare_task_cfgs.WelfareTaskCfgs, error) {
	if len(taskCfgs) <= 0 {
		return nil, nil
	}

	allTaskType, err := cc.GetAllComplianceType(country, r, taskTypeMenu)
	if err != nil {
		return nil, err
	}

	var welfareTaskList []*welfare_task_cfgs.WelfareTaskCfgs
	for _, vv := range taskCfgs {
		if !utils.ContainsArray(allTaskType, int(vv.TaskType)) {
			welfareTaskList = append(welfareTaskList, vv)
		} else {
			cc.Logger.Infof("任务被合规过滤,task_id:%d", vv.TaskId)
		}
	}

	return welfareTaskList, nil
}

func (cc *ComplianceCenterCall) GetAllComplianceType(country int, r *http.Request, menu int) ([]int, error) {
	//通过合规接口拿到所有的businessKey和menu
	complianceRep, err := service_client.NewComplianceCenterCall(cc.ctx).CheckLimit(int64(country), r, cc.svc)
	if err != nil {
		return nil, err
	}

	//获取所有被合规的taskType
	ComplianceConfig := consts.GetComplianceConfig()
	allTaskType := make([]int, 0)
	for _, v := range ComplianceConfig {
		mapBusiness := []string{}
		mapMenu := []string{}
		if len(v.BusinessKey) != 0 {
			mapBusiness = utils.Intersection(v.BusinessKey, complianceRep.Business)
		}
		if len(v.MenuKeys) != 0 {
			mapMenu = utils.Intersection(v.MenuKeys, complianceRep.Menu)
		}

		//所有被合规的任务类型
		if len(mapBusiness) > 0 || len(mapMenu) > 0 {
			allTaskType = append(allTaskType, v.Menus[menu]...)
		}
	}

	return allTaskType, nil
}

func (cc *ComplianceCenterCall) GetCompliancePointsShopByKey(country int, r *http.Request, pointsShopCfgs []*welfare_points_shop.WelfarePointsShop) ([]*welfare_points_shop.WelfarePointsShop, error) {
	if len(pointsShopCfgs) <= 0 {
		return nil, nil
	}

	allTaskType, err := cc.GetAllComplianceType(country, r, consts.UserTaskTypePointsExchange)
	if err != nil {
		return nil, err
	}

	var shopList []*welfare_points_shop.WelfarePointsShop
	for _, v := range pointsShopCfgs {
		if !utils.ContainsArray(allTaskType, int(v.PrizeSubType)) {
			shopList = append(shopList, v)
		} else {
			cc.Logger.Infof("奖品被合规过滤,shop_id:%d", v.Id)
		}
	}

	return shopList, nil
}

// 用户所在国家，是否是合规国家，如果国家理财和合约被合规，则属于合规国家
func (cc *ComplianceCenterCall) IsComplianceRegion(country int64, r *http.Request) (bool, error) {
	//通过合规接口拿到所有的businessKey和menu
	complianceRep, err := service_client.NewComplianceCenterCall(cc.ctx).CheckLimit(country, r, cc.svc)
	if err != nil {
		return false, err
	}

	isComplianceRegion := []string{"lend_earn", "futures"}
	intersection := utils.Intersection(isComplianceRegion, complianceRep.Menu)
	if len(intersection) == 0 {
		return false, nil
	}

	return true, nil
}
