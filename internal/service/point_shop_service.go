package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"strconv"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_points_shop"
	"gateio_service_welfare_go/internal/svc"
)

func SortPrize(svcCtx *svc.ServiceContext, ctx context.Context, shopSortMap map[string]int) (result bool, err error) {
	for id, sort := range shopSortMap {
		idInt64, _ := strconv.ParseInt(id, 10, 64)
		pointsShop := &welfare_points_shop.WelfarePointsShop{
			Id:   idInt64,
			Sort: int64(sort),
		}
		_, err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateWelfarePointsShop(ctx, pointsShop)
		if err != nil {
			logc.Errorf(ctx, "Failed to update sort for pointsShop ID %d: %v", id, err)
			return false, err
		}
	}
	return true, nil
}

func OfflinePrize(svcCtx *svc.ServiceContext, ctx context.Context, id int64) (result bool, err error) {
	pointsShop := &welfare_points_shop.WelfarePointsShop{
		Id:     id,
		Status: consts.StatusOffline,
	}
	_, err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateWelfarePointsShop(ctx, pointsShop)
	if err != nil {
		logc.Errorf(ctx, "Failed to offline pointsShop for task ID %d: %v", id, err)
		return false, err
	}
	return true, nil
}
