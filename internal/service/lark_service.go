package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bytes"
	"context"
	"encoding/json"
	"gateio_service_welfare_go/internal/utils"
	"io"
	"net/http"
)

const (
	BaseHook = "https://open.larksuite.com/open-apis/bot/v2/hook/"
)

// Config 模拟 PHP 的 config 函数
func Config(key string) string {
	// 这里简单返回一个示例值，实际中需要从配置文件读取
	// 假设配置文件读取逻辑如下，你可以根据实际情况修改
	switch key {
	case "lark.dev":
		return "dev-webhook-url"
	default:
		return ""
	}
}

func GetHookKeyMap() map[string]string {
	return map[string]string{
		"dev":                   "1cd8f8f5-6b6c-4933-8dc9-5d3bbaf58af9", //测试环境
		"online":                "b2ef9d77-12d6-4a0b-88bf-a110c0cc872b", //线上环境
		"online-reconciliation": "02b3dd04-5839-4e79-a79f-9c3afcd5b8a2", //线上环境-对账
	}
}

// SendMessage 发送消息.
func SendMessage(ctx context.Context, msg, business string) (err error) {
	if utils.CheckDev() {
		business = "dev"
	}
	dataMap := map[string]interface{}{
		"msg_type": "text",
		"content": map[string]string{
			"text": msg,
		},
	}
	// 将 map 转换为 JSON
	jsonData, err := json.Marshal(dataMap)
	if err != nil {
		logc.Errorf(ctx, "JSON 序列化失败:", err)
		return
	}
	sendMessageURL := "https://open.larksuite.com/open-apis/bot/v2/hook/" + GetHookKeyMap()[business]
	params := bytes.NewBuffer(jsonData)
	resp, err := http.DefaultClient.Post(sendMessageURL, "application/json", params)
	if err != nil {
		logc.Errorf(ctx, "lark发送消息接口请求错误:", err)
		return err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logc.Errorf(ctx, "读取响应体失败:", err)
		return
	}

	if resp.StatusCode != http.StatusOK {
		logc.Errorf(ctx, "lark发送消息接口请求错误:", resp.StatusCode, " 异常返回：", string(body))
		return err
	}

	var result struct {
		Msg  string      `json:"msg"`
		Code int         `json:"code"`
		Data interface{} `json:"data"`
	}
	err = json.Unmarshal(body, &result)
	if err != nil {
		logc.Errorf(ctx, "lark发送消息接口返回解析错误:", err)
		return err
	}
	if result.Code != 0 {
		logc.Errorf(ctx, "lark发送消息接口返回错误:", result)
		return err
	}
	return
}
