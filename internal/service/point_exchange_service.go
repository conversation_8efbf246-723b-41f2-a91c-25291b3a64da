package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"errors"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_points_shop"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"
)

/*
积分兑换：发劵的是有不同实现，分两种：vip1,coupon_id，
完成任务发奖：任务中配置不同奖品，不同实现方式
领取任务：根据任务类型区分实现方式，taskInit,任务列表不同类型的条件判断不一样 完成任务发奖有积分和卡劵
*/

// 包初始化时设置随机数种子
var (
	random = rand.New(rand.NewSource(time.Now().UnixNano()))
	mutex  sync.Mutex
)

// 获取唯一ID
func GetOnlyId() string {
	// 加锁确保并发安全
	mutex.Lock()
	defer mutex.Unlock()

	// 获取当前时间的毫秒级时间戳
	milliseconds := time.Now().UnixMilli()
	// 生成两个 1111 到 9999 之间的随机数
	randNum1 := random.Intn(9999-1111+1) + 1111
	randNum2 := random.Intn(9999-1111+1) + 1111

	// 使用 strings.Builder 进行字符串拼接
	var builder strings.Builder
	builder.WriteString(strconv.FormatInt(milliseconds, 10))
	builder.WriteByte('_')
	builder.WriteString(strconv.Itoa(randNum1))
	builder.WriteByte('_')
	builder.WriteString(strconv.Itoa(randNum2))

	return builder.String()
}

// 定义多态实现interface
type SendPrizeService interface {
	SendPrize(uid int64, welfarePointsShop *welfare_points_shop.WelfarePointsShop, source, requestId string) (int, error)
}

// 定义vip1兑换结构体
type VipImpl struct {
	Ctx    context.Context
	SvcCtx *svc.ServiceContext
	Name   string
}

// 暂时不独自发放vip奖励
func (v *VipImpl) SendPrize(uid int64, shopInfo *welfare_points_shop.WelfarePointsShop, source, requestId string) (int, error) {
	userCenter := service_client.NewUserCenterCall(v.Ctx)
	users, err := userCenter.GetUserList([]int64{uid})
	if err != nil {
		logc.Infof(v.Ctx, "VipImpl SendPrize GetUserInfo is err, uid is:%d,err is:%v", uid, err)
		return 0, err
	}
	if len(users) == 0 {
		logc.Infof(v.Ctx, "VipImpl SendPrize GetUserInfo user is nil, uid is:%d", uid)
		return 0, errors.New("Not found user")
	}
	if users[0].IsSub == 1 {
		return 0, errors.New("The sub account cannot manually adjust the vip level")
	}
	tierOld := users[0].Tier
	tier := int64(1)
	if shopInfo.PrizeSubType == consts.PointsShopTypeVip1 && tierOld >= tier {
		return 0, errors.New("The VIP level to be changed is lower than the current level")
	}
	if shopInfo.PrizeSubType == consts.PointsShopTypeVipAdd1 {
		tier = tierOld + 1
	}

	symbol := "activity"
	name := "welfare"
	err = userCenter.UpdateUserVIP(int(uid), int(tier), 2, int(utils.MustInt64(shopInfo.PrizeValue, 0)), 0, symbol, name)
	if err != nil {
		return 0, err
	}
	return 0, nil
}

// 定义需要CouponId发放的结构体
type CouponIdImpl struct {
	Ctx    context.Context
	SvcCtx *svc.ServiceContext
	Name   string
}

func (f *CouponIdImpl) SendPrize(uid int64, shopInfo *welfare_points_shop.WelfarePointsShop, source, requestId string) (int, error) {
	prizeNum := int(utils.MustInt64(shopInfo.PrizeValue, 0))
	logc.Infof(f.Ctx, "CouponIdImpl SendPrize param is:%s,%d,%s,%d,%s", source, shopInfo.PrizeId, requestId, prizeNum, f.Name)
	_, err := service_client.NewCouponCenterCall(f.Ctx).SendCouponById(uid, shopInfo.PrizeId, requestId, shopInfo.PrizeValue, source)
	if err != nil {
		logc.Info(f.Ctx, "CouponIdImpl SendCouponById is err:", err, source, shopInfo.PrizeId, requestId, prizeNum, f.Name)
		return 0, err
	}
	return 0, nil
}
