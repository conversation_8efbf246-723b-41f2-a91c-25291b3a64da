package prize_service

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"errors"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"time"
)

// CheckIn 签到
func CheckIn(svc *svc.ServiceContext, ctx context.Context, task *dao.CheckInConfig, uid, isCycleLastDay int) (int64, error) {
	recordId, err := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).InsertCheckIn(ctx, task, uid, isCycleLastDay)
	if err != nil {
		logc.Errorf(ctx, "Insert check-in record failed: %v", err)
		return 0, fmt.Errorf("insert check-in record failed: %w", err)
	}
	prizeId, source := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).GetPrizeInfo(task)

	// 处理奖品发放
	status := HandlePrizeDistribution(svc, ctx, task, uid, recordId, prizeId, source)

	// 调用dao层更新签到状态
	if _, err := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).UpdateCheckInStatus(ctx, recordId, status); err != nil {
		logc.Errorf(ctx, "Update check-in status failed: %v", err)
		return 0, err
	}
	// 签到失败
	if status == consts.CheckInStatusFailed {
		return 0, fmt.Errorf("CheckIn prize distribution failed")
	}

	return recordId, nil
}

// HandlePrizeDistribution 处理奖品发放
func HandlePrizeDistribution(svc *svc.ServiceContext, ctx context.Context, task *dao.CheckInConfig, uid int, recordId int64, prizeId int, source string) int {
	switch task.PrizeType {
	case consts.CheckInPoints:
		w := dao.NewWelfareMysqlDBUtil(svc.WelfareDB)
		today := utils.GetYmdDay(0)

		//开启事务
		tx := w.DB.Begin()
		defer tx.Rollback()
		//增加用户积分和积分流水
		boolPoint, err := service.AddUserPoints(ctx, tx, uid, int64(task.PrizeTypeNum), task, consts.UserTaskTypeCheckIn, today, consts.UserTaskTaskTypeCheckIn, int(recordId))
		if err != nil || !boolPoint {
			logc.Errorf(ctx, "Send AddUserPoints failed: %v", err)
			return consts.CheckInStatusFailed
		}

		tx.Commit()
		w.DelRedisUserPoints(svc, int64(uid))
		return consts.CheckInStatusSuccess
	case consts.CheckInCoupon:
		res, err := SendCheckInCoupon(svc, ctx, int64(uid), int64(prizeId), int64(task.PrizeTypeNum), source)
		if err != nil {
			logc.Errorf(ctx, "SendCheckInCoupon failed: %v", err)
		}
		return res
	default:
		return consts.CheckInStatusFailed
	}
}

func SendCheckInCoupon(svc *svc.ServiceContext, ctx context.Context, uid, couponId, prizeTypeNum int64, source string) (int, error) {
	prizeSource := consts.RecordPrizeSourceCheckinTasks
	// 根据卡券id获取卡券类型
	prizeType := consts.CheckInCouponType

	//开启事务
	tx := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).DB.Begin()
	defer tx.Rollback()
	recordId, errRecord := dao.NewWelfareMysqlTxDBUtil(tx).InsertExchangeRecord(ctx, uid, couponId, 0, consts.RecordStatusInit, prizeSource, int64(prizeType), prizeTypeNum, 0)

	if errRecord != nil || recordId <= 0 {
		return consts.CheckInStatusFailed, errors.New("InsertExchangeRecord error")
	}

	recordDetailId, errRecordDetail := dao.NewWelfareMysqlTxDBUtil(tx).InsertUserPrizeDetail(ctx, uid, recordId, nil, nil, time.Now(), time.Now())
	if errRecordDetail != nil || recordDetailId <= 0 {
		return consts.CheckInStatusFailed, errors.New("InsertUserPrizeDetail error")
	}

	//基础数据变更成功，提交事务
	tx.Commit()

	detail := map[string]string{
		"sendPrizeStatus": "INIT",
	}
	status := consts.RecordStatusFailed
	//请求卡券发放奖励
	sendCouponFormatResp := service.SendCouponPrice(ctx, uid, couponId, utils.MustString(recordId), utils.MustString(prizeTypeNum), source)
	if sendCouponFormatResp.Code == consts.Success {
		detail["sendPrizeStatus"] = "SUCCESS"
		status = consts.RecordStatusSuccess
	} else {
		detail["sendPrizeStatus"] = "FAILED"
		if sendCouponFormatResp.CouponCode == -1 {
			detail["sendCouponStatus"] = "FAILED"
		}
	}
	//当调用任务中心发奖成功之后会出现INIT的情况，调用任务中心发奖成功之后不做处理
	if detail["sendPrizeStatus"] != "INIT" {
		// 开启事务
		tx2 := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).DB.Begin()
		defer tx2.Rollback()

		prizeService := NewPrizeService(ctx, svc)
		updateExchangeBool := prizeService.UpdateExchangeStatusById(tx2, int(uid), int(recordId), int(recordDetailId), status, detail, int(couponId), prizeSource, 0, "")
		if !updateExchangeBool {
			return consts.CheckInStatusFailed, errors.New("UpdateExchangeStatusById error")
		}
		tx2.Commit()
	}

	// 发放卡券成功
	if sendCouponFormatResp.Code == consts.Success {
		return consts.CheckInStatusSuccess, nil
	} else {
		return consts.CheckInStatusProcess, nil
	}

}
