package prize_service

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
	"gateio_service_welfare_go/internal/utils"
	"gorm.io/gorm"
	"net/http"
	"strconv"
	"time"
)

type PrizeService struct {
	Logger logx.Logger
	ctx    context.Context
	svc    *svc.ServiceContext
}

func NewPrizeService(ctx context.Context, svc *svc.ServiceContext) *PrizeService {
	return &PrizeService{Logger: logx.WithContext(ctx), ctx: ctx, svc: svc}
}

func (np *PrizeService) IsKycRepeat(photoidEncryption string, taskCenterId int64) (bool, error) {
	if photoidEncryption == "" || taskCenterId <= 0 {
		return false, errors.New("invalid params")
	}

	kycPrizeInfo, err := dao.NewWelfareMysqlDBUtil(np.svc.WelfareDB).GetPrizeDetailByKycAndTaskId(np.ctx, photoidEncryption, taskCenterId)
	if err != nil {
		return false, err
	}

	if kycPrizeInfo.Id <= 0 {
		return false, nil
	}

	return true, nil
}

func (np *PrizeService) ReceivePrize(req *types.ReveivePrizeReq, r *http.Request) (resp *types.UniversalNoParamReq, error error) {
	userInfo := requestools.GetUserInfo(r)
	uid := 0
	if userInfo != nil {
		uid = userInfo.UID
	}

	defer func() {
		utils.LogRouterData(np.ctx, "ReceivePrize", uid, req, resp, error)
	}()

	if uid <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}

	//领取奖励频率限制，加锁
	lockKey := fmt.Sprintf(consts.WelfareReceivePrize, uid)
	setBool, err := np.svc.Redis.SetnxEx(lockKey, "1", 2)
	if err != nil {
		np.Logger.Errorf("receive prize SetnxEx is err,uid is:%d，failed is：%v", uid, err)
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}

	if !setBool {
		np.Logger.Infof("receive prize ManyAttempts ,uid is:%d", uid)
		return nil, consts.GetErrorMsg(r, consts.ManyAttempts)
	}
	boolCheck, checkCode := service.CheckUserIdentity(np.svc, np.ctx, userInfo)
	if !boolCheck {
		return nil, consts.GetErrorMsg(r, checkCode)
	}

	userTaskInfo, err := dao.NewWelfareMysqlDBUtil(np.svc.WelfareDB).GetUserTaskByUIdAndTaskId(np.ctx, int64(uid), req.TaskId)
	if err != nil {
		np.Logger.Errorf("receive prize get user_task info failed ,uid is:%d, failed:%v", uid, err)
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}
	//福利任务信息应该从用户领取的快照中获取
	userTaskCfg := &welfare_task_cfgs.WelfareTaskCfgs{}
	err = json.Unmarshal([]byte(userTaskInfo.Memo), userTaskCfg)
	//判断福利中心任务是否被成功解析
	if userTaskCfg.Id == 0 || userTaskCfg.TaskId == 0 {
		np.Logger.Errorf("receive prize userTaskCfg failed ,uid is:%d, failed:%v", uid, err)
		return nil, consts.GetErrorMsg(r, consts.ErrSystemError)
	}

	if userTaskInfo.Id <= 0 || userTaskInfo.Status != consts.StatusDone {
		np.Logger.Errorf("receive prize userTaskInfo status != done ,uid is:%d", uid)
		return nil, consts.GetErrorMsg(r, consts.ErrInvalidParam)
	}

	taskCenterInfo, err := service.GetTaskListByTaskIds(np.svc, np.ctx, uid, []int64{userTaskCfg.TaskId})
	if err != nil {
		np.Logger.Errorf("receive prize get task center info failed ,uid is:%d, failed:%v", uid, err)
		return nil, consts.GetErrorMsg(r, consts.ErrApiError)
	}

	if _, ok := taskCenterInfo[userTaskCfg.TaskId]; !ok || taskCenterInfo[userTaskCfg.TaskId] == nil {
		np.Logger.Errorf("receive prize task center info empty ,uid is:%d", uid)
		return nil, consts.GetErrorMsg(r, consts.ErrRecordNotExist)
	}

	//任务大类
	oneLevelType := userTaskCfg.Type
	//如果是新人任务，需要判断风控
	if utils.ContainsArray([]int64{consts.UserTaskTypeNewbieGuide, consts.UserTaskTypeNewbieAdvanced}, oneLevelType) {
		dataMap := make(map[string]interface{})
		dataMap["user_id"] = uid
		dataMap["ip"] = requestools.GetClientIP(r)
		dataMap["const_id"] = req.ConstID
		dataMap["is_async"] = 0
		isRisk, errRisk := service.GetUserRiskData(np.svc, np.ctx, uid, req.ConstID, consts.RiskEventCodeIndexPageCheck, dataMap, r)
		if errRisk != nil {
			np.Logger.Errorf("receive prize InnerCheckRisk is err,params is:%d, %s ，err is：%v", uid, req.ConstID, err)
			return nil, consts.GetErrorMsg(r, consts.ErrApiError)
		}
		//命中风控
		if isRisk {
			np.Logger.Infof("receive prize InnerCheckRisk is risk,params is:%d, %s", uid, req.ConstID)
			//命中风控直接返回
			return nil, consts.GetErrorMsg(r, consts.ErrRiskNoticeReject)
		}
	}

	errCode := 0
	lang := requestools.GetUserLanguage(r)
	//如果是新人任务，需要判断风控和KYC是否重复
	if utils.ContainsArray([]int64{consts.UserTaskTypeNewbieGuide, consts.UserTaskTypeNewbieAdvanced}, oneLevelType) {
		errCode, err = NewPrizeNewbieService(np.ctx, np.svc).ReceivePrize(userTaskCfg, userTaskInfo, taskCenterInfo[userTaskCfg.TaskId], lang)
	} else {
		errCode, err = NewPrizeDailyService(np.ctx, np.svc).ReceivePrize(userTaskCfg, userTaskInfo, taskCenterInfo[userTaskCfg.TaskId])
	}

	if err != nil {
		np.Logger.Infof("receivePrize innerHandle service uid:%d|taskId:%d|return:%v", uid, req.TaskId, err)
		return nil, consts.GetErrorMsg(r, errCode)
	}
	utils.DelCommonRedisKey(np.svc, int64(uid), int(oneLevelType), lang)

	return &types.UniversalNoParamReq{}, nil
}

func (np *PrizeService) UpdateExchangeStatusById(updateTx *gorm.DB, uid, recordId, prizeDetailId int, status string, detail map[string]string, prizeId int, prizeSource string, taskCenterId int, photoIDEncryption string) bool {
	if recordId == 0 || prizeDetailId == 0 {
		np.Logger.Errorf("updateExchangeStatusById param failed,uid:%d", uid)
		return false
	}

	updateBool, err := dao.NewWelfareMysqlTxDBUtil(updateTx).UpdateExchangeStatusById(np.ctx, uid, recordId, status)
	if err != nil || !updateBool {
		return false
	}

	updateDetailBool, err := dao.NewWelfareMysqlTxDBUtil(updateTx).UpdateExchangeDetailById(np.ctx, prizeDetailId, detail, time.Now())
	if err != nil || !updateDetailBool {
		return false
	}

	// 成功发奖
	if status == consts.RecordStatusSuccess && photoIDEncryption != "" {
		insertBool, err := dao.NewWelfareMysqlTxDBUtil(updateTx).InsertUserKycPrizeDetail(np.ctx, uid, prizeId, taskCenterId, photoIDEncryption, prizeSource)
		if err != nil || insertBool <= 0 {
			return false
		}
	}

	return true
}

func (np *PrizeService) SendPriceToTask(uid, userTaskId, taskCenterId int64) (int, error) {
	boolSwitch, boolErr := service.GetSystemSwitchByKey(np.svc, np.ctx, consts.WelfareConfigTaskSend)
	if boolErr != nil {
		return consts.ErrSystemError, boolErr
	}

	if boolSwitch == false {
		return consts.ErrSuspendedForMaintenance, errors.New("suspended for maintenance")
	}

	//更新任务状态 更新成发奖中
	_, err := dao.NewWelfareMysqlDBUtil(np.svc.WelfareDB).UpdateTaskStatus(np.ctx, np.svc, uid, userTaskId, consts.StatusInSettlement)
	if err != nil {
		np.Logger.Errorf("ReceivePrize UpdateTaskStatus is failed:, status:%d | uid:%d taskId:%d err:%s", consts.StatusInSettlement, uid, userTaskId, err.Error())
		return consts.ErrSystemError, errors.New("UpdateTaskStatus in settlement failed")
	}
	taskSendPrizeReq := &task.SendPrizeRequest{
		UserID:       uid,
		TaskID:       taskCenterId,
		BusinessType: consts.WelfareTaskBusinessType,
		BusinessID:   strconv.FormatInt(userTaskId, 10),
	}
	responseTask, err := task.NewClient().SendPrize(np.ctx, taskSendPrizeReq)
	if err != nil {
		//系统超时
		if responseTask.Code == 0 {
			taskSendPrizeReqByte, _ := json.Marshal(taskSendPrizeReq)
			np.Logger.Errorf("ReceivePrize task center SendPrize is failed:, uid:%d params is : %s | err:%v", uid, string(taskSendPrizeReqByte), err)
			return consts.ErrSystemError, errors.New("ReceivePrize task center SendPrize failed")
		} else {
			//任务系统未发奖，没有入库，需要用户重新发起领奖
			_, err = dao.NewWelfareMysqlDBUtil(np.svc.WelfareDB).UpdateTaskStatus(np.ctx, np.svc, uid, userTaskId, consts.StatusDone)
			if err != nil {
				np.Logger.Errorf("ReceivePrize UpdateTaskStatus is failed:, uid:%d | status:%d | taskId:%d err:%s", uid, consts.StatusDone, userTaskId, err.Error())
				return consts.ErrSystemError, errors.New("UpdateTaskStatus done failed")
			}
		}
	}

	return consts.Success, nil
}
