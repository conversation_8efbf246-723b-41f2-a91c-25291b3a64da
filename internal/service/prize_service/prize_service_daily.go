package prize_service

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"errors"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/service/kafka_producer"
	"gateio_service_welfare_go/internal/svc"
	"math"
	"strconv"
	"time"
)

type PrizeDailyService struct {
	Logger logx.Logger
	ctx    context.Context
	svc    *svc.ServiceContext
}

func NewPrizeDailyService(ctx context.Context, svc *svc.ServiceContext) *PrizeDailyService {
	return &PrizeDailyService{Logger: logx.WithContext(ctx), ctx: ctx, svc: svc}
}

func (np *PrizeDailyService) ReceivePrize(userTaskCfg *welfare_task_cfgs.WelfareTaskCfgs, userTaskInfo *welfare_user_tasks.WelfareUserTasks, taskCenterInfo *task.Task) (errCode int, error error) {

	rewards := service.GetTaskCenterReward(taskCenterInfo)
	points := int64(0)

	hasPointsPrize := false
	hasCouponPrize := false

	for _, v := range rewards {
		//目前只支持积分类型的阶梯任务，需要判断状态获取对应的积分奖励
		if v.RewardType == consts.PrizeTypePoints || v.RewardType == consts.PrizeTypeDiy {
			hasPointsPrize = true
			if v.Status == consts.TaskStatusDone {
				points = int64(math.Max(float64(points), float64(v.RewardNum)))
			}
		} else {
			hasCouponPrize = true
		}
	}

	if hasPointsPrize && hasCouponPrize {
		return consts.ErrSystemError, errors.New("ReceiveDailyPrize has two difference prize type")
	}

	if !hasPointsPrize && !hasCouponPrize {
		return consts.ErrSystemError, errors.New("ReceiveDailyPrize prize type is not points and is not coupon")
	}

	//任务大类
	oneLevelType := userTaskCfg.Type
	taskType := userTaskInfo.TaskType
	userTaskId := userTaskInfo.Id
	taskCenterId := userTaskInfo.TaskCenterId
	uid := userTaskInfo.Uid

	if hasPointsPrize {

		boolSwitch, boolErr := service.GetSystemSwitchByKey(np.svc, np.ctx, consts.WelfareConfigPointsSend)
		if boolErr != nil {
			return consts.ErrSystemError, boolErr
		}

		if boolSwitch == false {
			return consts.ErrSuspendedForMaintenance, errors.New("suspended for maintenance")
		}

		if points <= 0 {
			return consts.ErrSystemError, errors.New("ReceiveDailyPrize get points = 0")
		}

		//开启事务
		tx := dao.NewWelfareMysqlDBUtil(np.svc.WelfareDB).DB.Begin()
		defer tx.Rollback()
		//更新任务状态 更新成已发奖
		data := &welfare_user_tasks.WelfareUserTasks{
			Id:     userTaskId,
			Status: consts.StatusSettlement,
			Points: points,
		}

		boolUdateStatus, err := dao.NewWelfareMysqlTxDBUtil(tx).UpdateTaskData(np.ctx, np.svc, uid, data)
		if err != nil || !boolUdateStatus {
			np.Logger.Errorf("ReceiveDailyPrize UpdateTaskStatus is error:, status:%d | businessId:%d err:%v", consts.StatusSettlement, userTaskId, err)
			return consts.ErrSystemError, errors.New("ReceiveDailyPrize UpdateTaskStatus is failed")
		}

		//增加用户积分和积分流水
		boolPoint, err := service.AddUserPoints(np.ctx, tx, int(uid), points, rewards, int(oneLevelType), int(userTaskCfg.Id), int(taskType), int(userTaskId))

		if err != nil || !boolPoint {
			return consts.ErrSystemError, errors.New("ReceiveDailyPrize IncrUserPoints is failed")
		}

		tx.Commit()
		dao.NewWelfareMysqlDBUtil(np.svc.WelfareDB).DelRedisUserPoints(np.svc, uid)

		kafkaProducer, err := kafka_producer.NewKafkaProducer(np.svc.KafkaConf)
		if err != nil {
			return consts.ErrSystemError, errors.New("ReceiveDailyPrize kafkaProducer init failed")
		}
		defer kafkaProducer.Close()

		err = kafkaProducer.TaskReceivePrizeSend(np.ctx, uid, taskCenterInfo.TaskID, consts.WelfareTaskBusinessType, time.Now().Unix(), strconv.FormatInt(userTaskId, 10))

		if err != nil {
			np.Logger.Errorf("ReceivePointPrize Producer.TaskRecordReceiveProducer failed：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d, err:%v", uid, userTaskId, taskCenterInfo.TaskID, consts.WelfareTaskBusinessType, err)
		} else {
			np.Logger.Infof("ReceivePointPrize Producer.TaskRecordReceiveProducer succ：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d", uid, userTaskId, taskCenterInfo.TaskID, consts.WelfareTaskBusinessType)
		}
	} else {

		return NewPrizeService(np.ctx, np.svc).SendPriceToTask(uid, userTaskId, taskCenterId)
	}

	return consts.Success, nil

}
