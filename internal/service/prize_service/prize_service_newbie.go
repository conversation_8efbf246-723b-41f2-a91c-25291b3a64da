package prize_service

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"encoding/json"
	"errors"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/service/kafka_producer"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"strconv"
	"time"
)

type PrizeNewbieService struct {
	Logger logx.Logger
	ctx    context.Context
	svc    *svc.ServiceContext
}

func NewPrizeNewbieService(ctx context.Context, svc *svc.ServiceContext) *PrizeNewbieService {
	return &PrizeNewbieService{Logger: logx.WithContext(ctx), ctx: ctx, svc: svc}
}

func (np *PrizeNewbieService) IsKycRepeat(photoidEncryption string, taskCenterId int64) (bool, error) {
	if photoidEncryption == "" || taskCenterId <= 0 {
		return false, errors.New("invalid params")
	}

	kycPrizeInfo, err := dao.NewWelfareMysqlDBUtil(np.svc.WelfareDB).GetPrizeDetailByKycAndTaskId(np.ctx, photoidEncryption, taskCenterId)
	if err != nil {
		return false, err
	}

	if kycPrizeInfo.Id <= 0 {
		return false, nil
	}

	return true, nil
}

func (nns *PrizeNewbieService) ReceivePrize(userTaskCfg *welfare_task_cfgs.WelfareTaskCfgs, userTaskInfo *welfare_user_tasks.WelfareUserTasks, taskCenterInfo *task.Task, lang string) (errCode int, err error) {
	//奖励信息
	rewards := []*service.TaskCenterReward{}
	//任务大类
	oneLevelType := userTaskCfg.Type
	taskCenterId := userTaskInfo.TaskCenterId
	userTaskId := userTaskInfo.Id
	uid := userTaskInfo.Uid
	isKycRepeat := false
	photoIdEncryption := ""
	//注册任务不做kyc验证
	if userTaskCfg.Type != consts.UserTaskTypeNewbieRegister {
		rewards = service.GetTaskCenterReward(taskCenterInfo)
		userCenterInfo, errUserCenter := service.GetUserKycInfo(nns.svc, nns.ctx, uid)
		if errUserCenter != nil {
			return consts.ErrApiError, errors.New("get user kyc info error")
		}
		if userCenterInfo.PhotoidEncryption == "" {
			return consts.ErrMustKyc2, errors.New("get user PhotoidEncryption error")
		}
		photoIdEncryption = userCenterInfo.PhotoidEncryption
		isKycRepeatVal, errKycRepeat := nns.IsKycRepeat(userCenterInfo.PhotoidEncryption, taskCenterId)
		if errKycRepeat != nil {
			return consts.ErrSystemError, errors.New("get user kyc repeat info error")
		}
		isKycRepeat = isKycRepeatVal
	} else {
		reward := &service.TaskCenterReward{}
		//获取注册任务的奖励信息
		registerTaskInfo := dao.RegisterTaskInfo{}
		_ = json.Unmarshal([]byte(userTaskCfg.ExtraTaskInfo.String), &registerTaskInfo)
		if registerTaskInfo.CouponId == 0 || registerTaskInfo.Source == "" {
			nns.Logger.Errorf("ReceivePrize registerTaskInfo is err,params is:%s ，uid:%d err is：%v", userTaskCfg.ExtraTaskInfo.String, uid, err)
			return consts.ErrDbError, errors.New("task CouponId,Source is nil")
		}
		reward.RewardCouponId = registerTaskInfo.CouponId
		reward.RewardSource = registerTaskInfo.Source
		couponAmount, err := service.GetCouponAmount(nns.svc, nns.ctx, registerTaskInfo.CouponId, registerTaskInfo.Source)
		if err != nil {
			//只报错不返回
			nns.Logger.Errorf("ReceivePrize GetCouponAmount is failed ，uid: %d Params is：%d, %s, err is:%v", uid, registerTaskInfo.CouponId, registerTaskInfo.Source, err)
		}
		reward.RewardType = consts.PrizeTypeCoupon
		couponAmountVal, err := strconv.ParseInt(couponAmount, 10, 64)
		if err != nil {
			//只报错不返回
			nns.Logger.Errorf("ReceivePrize strconv.Atoi(couponAmount) is failed is:%v, uid:%d, params is:%s", err, uid, couponAmount)
		}
		//因为只报错不返回，如果注册任务的奖励配置错误，RewardNum的值可能为0
		reward.RewardNum = couponAmountVal
		rewards = append(rewards, reward)
	}

	if len(rewards) == 0 {
		nns.Logger.Errorf("ReceivePrize rewards is empty uid:%d", uid)
		return 0, err
	}

	//判断是否有限时天数
	welcomeTaskInfo := dao.WelcomeTaskInfo{}
	_ = json.Unmarshal([]byte(userTaskCfg.ExtraTaskInfo.String), &welcomeTaskInfo)
	extraRewardEndTime := int64(0)
	if welcomeTaskInfo.LimitDays > 0 {
		extraRewardEndTime = userTaskInfo.CreatedAt.AddDate(0, 0, int(welcomeTaskInfo.LimitDays)).Unix()
	}

	couponId := rewards[0].RewardCouponId
	couponSource := rewards[0].RewardSource
	prizeNum := rewards[0].RewardNum

	if extraRewardEndTime >= userTaskInfo.FinishTaskTime && rewards[0].ExtraRewardCouponId > 0 && rewards[0].ExtraRewardSource != "" {
		couponId = rewards[0].ExtraRewardCouponId
		couponSource = rewards[0].ExtraRewardSource
		prizeNum = rewards[0].ExtraRewardNum
	}

	prizeId := couponId
	prizeType, err := service.GetCouponType(nns.svc, nns.ctx, couponId, couponSource)
	if err != nil {
		nns.Logger.Errorf("ReceivePrize GetCouponType is failed is:%v", err)
		return 0, err
	}

	prizeSource := consts.GetTaskTypeMapRecordPrizeSource()[int(oneLevelType)]
	//kyc验证不通过进阶任务和入门任务都不发奖励
	if isKycRepeat {
		//开启事务
		tx := dao.NewWelfareMysqlDBUtil(nns.svc.WelfareDB).DB.Begin()
		defer tx.Rollback()
		recordId, errRecord := dao.NewWelfareMysqlTxDBUtil(tx).InsertExchangeRecord(nns.ctx, uid, prizeId, 0, consts.RecordStatusFailed, prizeSource, int64(prizeType), prizeNum, userTaskId)
		if errRecord != nil || recordId <= 0 {
			return consts.ErrSystemError, errors.New("kyc repeat InsertExchangeRecord error")
		}

		detail := map[string]string{
			"sendPrizeStatus": "FAILED",
			"kyc_repeat":      "1",
			"kyc_value":       photoIdEncryption,
		}

		recordDetailId, errRecordDetail := dao.NewWelfareMysqlTxDBUtil(tx).InsertUserPrizeDetail(nns.ctx, uid, recordId, rewards, detail, time.Now(), time.Now())
		if errRecordDetail != nil || recordDetailId <= 0 {
			return consts.ErrSystemError, errors.New("kyc repeat InsertUserPrizeDetail error")
		}

		boolUpdate, errBoolUpdate := dao.NewWelfareMysqlTxDBUtil(tx).UpdateTaskStatus(nns.ctx, nns.svc, uid, userTaskId, consts.StatusExpire)
		if errBoolUpdate != nil || !boolUpdate {
			return consts.ErrSystemError, errors.New("kyc repeat UpdateTaskStatus error")
		}
		tx.Commit()
		utils.DelCommonRedisKey(nns.svc, uid, int(oneLevelType), lang)
		return consts.ErrUserKycRepeat, errors.New("kyc repeat return")
	}

	//奖励发放的状态信息
	status := consts.RecordStatusFailed
	detail := map[string]string{
		"sendPrizeStatus": "INIT",
	}
	code := 0
	//额外奖励和注册任务调用卡劵发奖 有额外奖励再发额外奖励
	if (extraRewardEndTime >= userTaskInfo.FinishTaskTime && rewards[0].ExtraRewardCouponId > 0 && rewards[0].ExtraRewardSource != "") || userTaskCfg.Type == consts.UserTaskTypeNewbieRegister {

		//开启事务
		tx := dao.NewWelfareMysqlDBUtil(nns.svc.WelfareDB).DB.Begin()
		defer tx.Rollback()
		recordId, errRecord := dao.NewWelfareMysqlTxDBUtil(tx).InsertExchangeRecord(nns.ctx, uid, prizeId, 0, consts.RecordStatusInit, consts.RecordPrizeSourceNewbieTaskNew, int64(prizeType), prizeNum, userTaskId)
		if errRecord != nil || recordId <= 0 {
			return consts.ErrSystemError, errors.New("InsertExchangeRecord error")
		}

		recordDetailId, errRecordDetail := dao.NewWelfareMysqlTxDBUtil(tx).InsertUserPrizeDetail(nns.ctx, uid, recordId, rewards, nil, time.Now(), time.Now())
		if errRecordDetail != nil || recordDetailId <= 0 {
			return consts.ErrSystemError, errors.New("InsertUserPrizeDetail error")
		}

		//更新任务状态 更新成发奖中
		_, err = dao.NewWelfareMysqlTxDBUtil(tx).UpdateTaskStatus(nns.ctx, nns.svc, uid, userTaskId, consts.StatusInSettlement)
		if err != nil {
			nns.Logger.Errorf("ReceivePrize UpdateTaskStatus is error:, status:%d | taskId:%d err:%s", consts.StatusInSettlement, userTaskId, err.Error())
			return consts.ErrSystemError, errors.New("UpdateTaskStatus in settlement error")
		}
		//基础数据变更成功，提交事务
		tx.Commit()

		boolSwitch, boolErr := service.GetSystemSwitchByKey(nns.svc, nns.ctx, consts.WelfareConfigCouponSend)
		if boolErr != nil {
			return consts.ErrSystemError, boolErr
		}

		if boolSwitch == false {
			return consts.ErrSuspendedForMaintenance, errors.New("suspended for maintenance")
		}

		//请求卡券发放奖励
		sendCouponFormatResp := service.SendCouponPrice(nns.ctx, uid, couponId, strconv.FormatInt(recordId, 10), "", couponSource)
		if sendCouponFormatResp.Code == consts.Success {
			detail["sendPrizeStatus"] = "SUCCESS"
			status = consts.RecordStatusSuccess
		} else {
			detail["sendPrizeStatus"] = "FAILED"
			if sendCouponFormatResp.CouponCode == -1 {
				detail["sendCouponStatus"] = "FAILED"
			}
		}
		code = sendCouponFormatResp.Code

		//当调用任务中心发奖成功之后会出现INIT的情况，调用任务中心发奖成功之后不做处理
		if detail["sendPrizeStatus"] != "INIT" {
			err = UpdateRewardStatus(nns.svc, nns.ctx, int(uid), int(recordId), int(recordDetailId), status, detail, int(prizeId), prizeSource, userTaskId, int(taskCenterId), photoIdEncryption)
			if err != nil {
				return consts.ErrSystemError, errors.New("UpdateRewardStatus settlement failed")
			}
		}

		//卡券发放成功，需要上报奖励 注册任务不需要上报
		if code == consts.Success && userTaskCfg.Type != consts.UserTaskTypeNewbieRegister {
			kafkaProducer, errKafkaInit := kafka_producer.NewKafkaProducer(nns.svc.KafkaConf)
			if errKafkaInit != nil {
				return consts.ErrSystemError, errors.New("ReceiveDailyPrize kafkaProducer init failed")
			}
			defer kafkaProducer.Close()

			errKafka := kafkaProducer.TaskReceivePrizeSend(nns.ctx, uid, taskCenterInfo.TaskID, consts.WelfareTaskBusinessType, time.Now().Unix(), strconv.FormatInt(userTaskId, 10))

			if errKafka != nil {
				nns.Logger.Errorf("ReceiveNewBiePrize Producer.TaskRecordReceiveProducer failed：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d, err:%v", uid, userTaskId, taskCenterInfo.TaskID, consts.WelfareTaskBusinessType, err)
			} else {
				nns.Logger.Infof("ReceiveNewBiePrize Producer.TaskRecordReceiveProducer succ：, UserId : %d, BusinessId: %d, TaskId: %d, BusinessType: %d", uid, userTaskId, taskCenterInfo.TaskID, consts.WelfareTaskBusinessType)
			}
		}

	} else {
		return NewPrizeService(nns.ctx, nns.svc).SendPriceToTask(uid, userTaskId, taskCenterId)
	}
	return code, nil
}

// 更新发奖状态  如果进阶任务发奖不调用任务中心发奖，这里就需要更新任务记录的展示状态
func UpdateRewardStatus(svc *svc.ServiceContext, ctx context.Context, uid, recordId, recordDetailId int, status string, detail map[string]string, prizeId int, prizeSource string, userTaskId int64, taskCenterId int, photoIdEncryption string) error {
	updateTx := dao.NewWelfareMysqlDBUtil(svc.WelfareDB).DB.Begin()
	defer updateTx.Rollback()
	updateExchangeBool := NewPrizeService(ctx, svc).UpdateExchangeStatusById(updateTx, uid, recordId, recordDetailId, status, detail, prizeId, prizeSource, taskCenterId, photoIdEncryption)
	if !updateExchangeBool {
		return errors.New("UpdateExchangeStatus failed")
	}

	if status == consts.RecordStatusSuccess {
		boolUpdate, errboolUpdate := dao.NewWelfareMysqlTxDBUtil(updateTx).UpdateTaskStatus(ctx, svc, int64(uid), userTaskId, consts.StatusSettlement)
		if errboolUpdate != nil || !boolUpdate {
			return errors.New("UpdateTaskStatus settlement failed")
		}
	}
	updateTx.Commit()
	return nil
}
