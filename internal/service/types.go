package service

type TaskCenterReward struct {
	Mark                string `json:"mark"`                   // 任务中心的任务mark
	MinNum              int64  `json:"min_num"`                // 完成任务的最小值
	RewardType          int64  `json:"reward_type"`            // 奖励类型
	RewardNum           int64  `json:"reward_num"`             // 奖励数量
	ExtraRewardType     int64  `json:"extra_reward_type"`      // 额外奖励类型(选填)
	ExtraRewardNum      int64  `json:"extra_reward_num"`       // 额外奖励数量(选填)
	RewardCouponId      int64  `json:"reward_coupon_id"`       // 正常奖励卡劵ID
	RewardSource        string `json:"reward_source"`          // 正常奖励source
	ExtraRewardCouponId int64  `json:"extra_reward_coupon_id"` // 额外奖励卡劵ID
	ExtraRewardSource   string `json:"extra_reward_source"`    // 额外奖励卡劵ID
	Status              int64  `json:"status"`                 // 奖励状态
}

type TaskCenterRewardDetail struct {
	Mark                string `json:"mark"`                   // 任务中心的任务mark
	MinNum              int64  `json:"min_num"`                // 完成任务的最小值
	RewardType          int64  `json:"reward_type"`            // 奖励类型
	RewardNum           int64  `json:"reward_num"`             // 奖励数量
	ExtraRewardType     int64  `json:"extra_reward_type"`      // 额外奖励类型(选填)
	ExtraRewardNum      int64  `json:"extra_reward_num"`       // 额外奖励数量(选填)
	RewardCouponId      int64  `json:"reward_coupon_id"`       // 正常奖励卡劵ID
	RewardSource        string `json:"reward_source"`          // 正常奖励source
	ExtraRewardCouponId int64  `json:"extra_reward_coupon_id"` // 额外奖励卡劵ID
	ExtraRewardSource   string `json:"extra_reward_source"`    // 额外奖励卡劵ID
	Status              int64  `json:"status"`                 // 奖励状态
	RewardName          string `json:"reward_name"`            // 奖励名称
	ExtraRewardName     string `json:"extra_reward_name"`      // 额外奖励名称
}

type QueryCoupon struct {
	CouponId int64  `json:"coupon_id"`
	Source   string `json:"Source"`
}

type CouponInfo struct {
	Amount                      string            `json:"amount"`
	Currency                    string            `json:"currency"`
	CouponRewardTranslationList map[string]string `json:"coupon_reward_translation_list"`
}
