package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"database/sql"
	"encoding/json"
	"strconv"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_task_cfgs"
	"gateio_service_welfare_go/internal/svc"
)

func SortTask(svcCtx *svc.ServiceContext, ctx context.Context, taskSortMap map[string]int) (result bool, err error) {
	for id, sort := range taskSortMap {
		idInt64, _ := strconv.ParseInt(id, 10, 64)
		task := &welfare_task_cfgs.WelfareTaskCfgs{
			Id:   idInt64,
			Sort: int64(sort),
		}
		_, err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateWelfareTaskCfg(ctx, task)
		if err != nil {
			logc.Errorf(ctx, "Failed to update sort for task ID %d: %v", id, err)
			return false, err
		}
	}
	return true, nil
}

func OfflineTask(svcCtx *svc.ServiceContext, ctx context.Context, id int64) (result bool, err error) {
	task := &welfare_task_cfgs.WelfareTaskCfgs{
		Id:           id,
		OnlineStatus: consts.StatusOffline,
	}
	_, err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateWelfareTaskCfg(ctx, task)
	if err != nil {
		logc.Errorf(ctx, "Failed to offline task for task ID %d: %v", id, err)
		return false, err
	}
	return true, nil
}

func ApproveTask(svcCtx *svc.ServiceContext, ctx context.Context, id int64, version int, creatorIdInt64 int64, creatorName string) (result bool, err error) {
	taskCfg, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).QueryWelfareTaskCfgListById(ctx, id)
	if err != nil || taskCfg == nil {
		logc.Errorf(ctx, "Failed to find task。task ID %d: %v", id, err)
		return false, err
	}
	if taskCfg.Version != int64(version) {
		logc.Errorf(ctx, "版本落后，请刷新查看后重新审核。task ID %d", id)
		return false, err
	}

	approvalId := creatorIdInt64
	approvalName := creatorName

	task := &welfare_task_cfgs.WelfareTaskCfgs{
		Id:           id,
		Version:      int64(version),
		Status:       consts.ReviewStatusApproval,
		OnlineStatus: consts.StatusOnline,
		ApprovalId:   approvalId,
		ApprovalName: approvalName,
	}
	taskCfg.Status = consts.ReviewStatusApproval
	taskCfg.OnlineStatus = consts.StatusOnline
	taskCfg.ApprovalId = approvalId
	taskCfg.ApprovalName = approvalName
	taskCfg.ApprovedSnapshot = sql.NullString{String: "", Valid: true}

	byteVal, _ := json.Marshal(taskCfg)
	task.ApprovedSnapshot = sql.NullString{String: string(byteVal), Valid: true}

	_, err = dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateWelfareTaskCfgByVersion(ctx, task)
	if err != nil {
		logc.Errorf(ctx, "Failed to approve task for task ID %d: %v", id, err)
		return false, err
	}
	return true, nil
}
