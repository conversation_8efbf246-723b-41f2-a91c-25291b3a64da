package kafka_producer

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"context"
	"gateio_service_welfare_go/internal/config"
	"gateio_service_welfare_go/internal/utils"
	"github.com/twmb/franz-go/pkg/kgo"
)

// KafkaProducer 表示一个 Kafka 生产者
type KafkaProducer struct {
	client *kgo.Client
	logx.Logger
}

// NewKafkaProducer 创建一个新的 Kafka 生产者客户端
func NewKafkaProducer(ProducerConf *config.KafkaConf) (*KafkaProducer, error) {
	client, err := kgo.NewClient(
		kgo.SeedBrokers(ProducerConf.Brokers...),
		kgo.RequiredAcks(kgo.AllISRAcks()),
	)
	if err != nil {
		return nil, err
	}
	return &KafkaProducer{
		client: client,
		Logger: logx.WithContext(context.Background()),
	}, nil
}

// ProduceMessagesDemo 发送指定数量的消息到 Kafka
func (p *KafkaProducer) ProduceMessagesDemo(ctx context.Context, topic string, msg []string) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	topic = utils.UnifyTopic(topic)
	messageCount := len(msg)
	for i := 0; i < messageCount; i++ {
		record := &kgo.Record{
			Value: []byte(msg[i]),
			Topic: topic,
		}
		err := p.client.ProduceSync(ctx, record).FirstErr()
		if err != nil {
			return err
		}
		p.Logger.Infof("成功发送消息: %s\n", msg[i])
	}
	return nil
}

// Close 关闭生产者客户端
func (p *KafkaProducer) Close() {
	p.client.Close()
}

// BaseProduceMsg kafka生产者发消息基本方法
func (p *KafkaProducer) BaseProduceMsg(ctx context.Context, topic string, msg string, key string, retryNum int) (int, error) {
	topic = utils.UnifyTopic(topic)
	record := &kgo.Record{
		Value: []byte(msg),
		Topic: topic,
		Key:   []byte(key),
	}

	sendNum := 1
	if retryNum > 0 {
		sendNum = sendNum + retryNum
	}

	for i := 0; i < sendNum; i++ {
		err := p.client.ProduceSync(ctx, record).FirstErr()
		if err != nil {
			if i == sendNum-1 { // 到了重试次数还发送失败，则返回错误
				return -1, err
			}
			logc.Infof(ctx, "BaseProduceMsg failed, step: ProduceSync, Topic: %s, Msg: %s, Err:%v", topic, msg, err)
			continue
		} else {
			break
		}
	}
	return 0, nil
}
