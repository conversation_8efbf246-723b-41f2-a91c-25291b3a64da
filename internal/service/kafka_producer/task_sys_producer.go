package kafka_producer

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/risk"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts/con_kafka"
	"gateio_service_welfare_go/internal/utils"
)

// 给任务系统上报用户领取任务统一方法
func (p *KafkaProducer) TaskRecordUserReceiveTask(ctx context.Context, userId, taskSysId, businessType, businessId, receiveTime, startTime, endTime int64, isDone int) error {
	// topic主题
	topicName := con_kafka.GrowthTaskRecordTopic

	// 发送的消息内容
	msg := con_kafka.TaskRecordReceiveMsg{
		UserId:       userId,
		TaskId:       taskSysId,
		ReceiveTime:  receiveTime,
		BusinessType: businessType,
		BusinessId:   businessId,
		IsDone:       isDone,
		IsAck:        0,
		StartTime:    startTime,
		EndTime:      endTime,
	}

	// 压缩消息体
	msgByte, err := json.Marshal(msg)
	if err != nil {
		logc.Errorf(ctx, "TaskRecordUserReceiveTask failed, step: json.Marshal, Topic : %s, Msg: %s, err: %v", topicName, string(msgByte), err)
		return fmt.Errorf("kafka消息体序列化失败")
	}

	// 发送消息
	_, err = p.BaseProduceMsg(ctx, topicName, string(msgByte), "", 3)
	if err != nil {
		logc.Errorf(ctx, "TaskRecordUserReceiveTask failed, step: p.BaseProduceMsg, Topic : %s, Msg: %s, err: %v", topicName, string(msgByte), err)
		// 上报任务失败，加入到补偿队列
		msgMap := utils.StructToMap(msg)
		param := &risk.ResetKafkaMessageRequest{
			Topic:   topicName,
			Message: msgMap,
			Key:     "",
		}
		err = risk.NewClient().ResetKafkaMessage(ctx, param)
		if err != nil {
			logc.Errorf(ctx, "TaskRecordUserReceiveTask failed, step: ResetKafkaMessage, param : %v, err: %v", param, err)
			return err
		}
	}
	return nil
}

// 给任务中台发送奖励上报
func (p *KafkaProducer) TaskReceivePrizeSend(ctx context.Context, uid, taskSysId, businessType, receiveTime int64, businessId string) error {
	// topic主题
	topicName := con_kafka.GrowthTaskReceiveTopic

	// 发送的消息内容
	msg := con_kafka.TaskPrizeSendMsg{
		UserID:       int(uid),
		TaskID:       int(taskSysId),
		ReceiveTime:  receiveTime,
		BusinessType: businessType,
		BusinessID:   businessId,
	}

	// 压缩消息体
	msgByte, err := json.Marshal(msg)
	if err != nil {
		logc.Errorf(ctx, "TaskReceivePrizeSend failed, step: json.Marshal, Topic : %s, Msg: %s, err: %v", topicName, string(msgByte), err)
		return fmt.Errorf("kafka消息体序列化失败")
	}

	// 发送消息
	_, err = p.BaseProduceMsg(ctx, topicName, string(msgByte), "", 3)
	if err != nil {
		logc.Errorf(ctx, "TaskReceivePrizeSend failed, step: p.BaseProduceMsg, Topic : %s, Msg: %s, err: %v", topicName, string(msgByte), err)
		// 上报任务失败，加入到补偿队列
		msgMap := utils.StructToMap(msg)
		param := &risk.ResetKafkaMessageRequest{
			Topic:   topicName,
			Message: msgMap,
			Key:     "",
		}
		err = risk.NewClient().ResetKafkaMessage(ctx, param)
		if err != nil {
			logc.Errorf(ctx, "TaskReceivePrizeSend failed, step: ResetKafkaMessage, param : %v, err: %v", param, err)
			return err
		}
	}
	return nil
}
