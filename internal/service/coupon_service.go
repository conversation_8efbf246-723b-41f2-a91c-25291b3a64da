package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/coupon"
)

type SendCouponFormatResp struct {
	Code       int    `json:"code"`
	CouponCode int    `json:"coupon_code"`
	Message    string `json:"message"`
	IsReturn   int    `json:"is_return"`
}

// 获取卡劵面值信息
func GetCouponAmount(svcCtx *svc.ServiceContext, ctx context.Context, couponId int64, source string) (string, error) {
	redisVal, _ := svcCtx.Redis.Get(fmt.Sprintf(consts.CouponInfoAmountKey, couponId, source))
	if redisVal != "" {
		return redisVal, nil
	} else {
		// 获取卡劵详情
		req := coupon.CouponInfoRequest{
			CouponID: couponId,
			Source:   source,
		}
		logc.Infof(ctx, "GetCouponAmount CouponInfo req is: %v", req)
		resp, err := coupon.NewClient().CouponInfo(ctx, &req)
		if err != nil {
			logc.Errorf(ctx, "service GetCouponAmount CouponInfo failed, err: %v", err)
			return "", err
		}
		logc.Infof(ctx, "GetCouponAmount CouponInfo resp Amount is: %s, req is:%v", resp.Amount, req)
		// 缓存十分钟
		_ = svcCtx.Redis.Setex(fmt.Sprintf(consts.CouponInfoAmountKey, couponId, source), resp.Amount, 600)
		return resp.Amount, nil
	}
}

// 获取卡劵信息
func GetCouponInfo(svcCtx *svc.ServiceContext, ctx context.Context, couponId int64, source string) (*coupon.CouponInfoResponse, error) {
	couponInfo := &coupon.CouponInfoResponse{}
	redisVal, _ := svcCtx.Redis.Get(fmt.Sprintf(consts.CouponInfoAll, couponId, source))
	if redisVal != "" {
		_ = json.Unmarshal([]byte(redisVal), couponInfo)
		return couponInfo, nil
	} else {
		// 获取卡劵详情
		req := coupon.CouponInfoRequest{
			CouponID: couponId,
			Source:   source,
		}
		resp, err := coupon.NewClient().CouponInfo(ctx, &req)
		if err != nil {
			logc.Errorf(ctx, "service GetCouponInfo CouponInfo failed,req is:%v, err: %v", req, err)
			return couponInfo, err
		}
		if resp.ID > 0 {
			byteVal, err := json.Marshal(resp)
			if err == nil {
				// 缓存30分
				_ = svcCtx.Redis.Setex(fmt.Sprintf(consts.CouponInfoAll, couponId, source), string(byteVal), 30*60)
			}
			couponInfo = resp
		}
		return couponInfo, nil
	}
}

// 获取卡劵信息  map的key规则 CouponId+:+Source
func QueryCouponList(svcCtx *svc.ServiceContext, ctx context.Context, reqList []*QueryCoupon) (map[string]*CouponInfo, error) {
	couponMap := map[string]*CouponInfo{}
	for _, queryReq := range reqList {
		couponInfo := &coupon.CouponInfoResponse{}
		redisVal, _ := svcCtx.Redis.Get(fmt.Sprintf(consts.CouponInfoAll, queryReq.CouponId, queryReq.Source))
		if redisVal != "" {
			_ = json.Unmarshal([]byte(redisVal), &couponInfo)
			couponMap[fmt.Sprintf("%d:%s", queryReq.CouponId, queryReq.Source)] = &CouponInfo{
				Amount:                      couponInfo.Amount,
				Currency:                    couponInfo.Currency,
				CouponRewardTranslationList: couponInfo.CouponRewardTranslationList,
			}
		} else {
			// 获取卡劵详情
			req := coupon.CouponInfoRequest{
				CouponID: queryReq.CouponId,
				Source:   queryReq.Source,
			}
			resp, err := coupon.NewClient().CouponInfo(ctx, &req)
			if err != nil {
				logc.Errorf(ctx, "service GetCouponInfo CouponInfo failed,req is:%v, err: %v", req, err)
				return nil, err
			}
			if resp.ID > 0 {
				byteVal, err := json.Marshal(resp)
				if err == nil {
					// 卡劵创建之后不会改动，缓存一天
					_ = svcCtx.Redis.Setex(fmt.Sprintf(consts.CouponInfoAll, queryReq.CouponId, queryReq.Source), string(byteVal), 86400)
				}
				couponInfo = resp
			}
			couponMap[fmt.Sprintf("%d:%s", queryReq.CouponId, queryReq.Source)] = &CouponInfo{
				Amount:                      couponInfo.Amount,
				Currency:                    couponInfo.Currency,
				CouponRewardTranslationList: couponInfo.CouponRewardTranslationList,
			}
		}
	}
	return couponMap, nil
}

// 获取卡劵类型
func GetCouponType(svcCtx *svc.ServiceContext, ctx context.Context, couponId int64, source string) (int, error) {
	couponInfo, couponErr := GetCouponInfo(svcCtx, ctx, couponId, source)
	if couponErr != nil {
		return 0, couponErr
	}

	couponMarket, ok := couponInfo.CouponExtInfo["market"].(string)
	if !ok {
		couponMarket = ""
	}
	welfareCouponType := consts.GetCouponTypeByTypeAndSubType(couponInfo.CouponType, couponMarket)
	return welfareCouponType, nil
}

// 获取奖励列表名称
func GetRewardNames(svcCtx *svc.ServiceContext, ctx context.Context, coupons []coupon.CouponInfoResponse) string {
	var rewardNames []string
	for _, couponInfo := range coupons {
		if couponInfo.CouponName != "" {
			rewardNames = append(rewardNames, couponInfo.CouponName)
		}
	}
	return strings.Join(rewardNames, ",")
}

func SendCouponPrice(ctx context.Context, uid, couponId int64, requestID, amount, source string) *SendCouponFormatResp {
	NewCouponCenterCall := service_client.NewCouponCenterCall(ctx)
	couponSendRes, err := NewCouponCenterCall.SendCouponById(uid, couponId, requestID, amount, source)
	if err != nil || couponSendRes.Code != 0 {
		logc.Warnf(ctx, "send coupon prize faild, uid:%d|couponId:%d|res:%v|mistake:%v", uid, couponId, couponSendRes, err)
	}
	return handleCouponRes(couponSendRes, err)
}

func handleCouponRes(couponSendRes *coupon.SendCouponByIdResponse, err error) *SendCouponFormatResp {
	isReturn := 0
	returnCode := couponSendRes.Code

	/**
	| 状态码      | 描述    |
	| -------- | ----- |
	| 10001    | 校验异常  |
	| 10000    | 请求太快了 |
	| 1008     | 系统异常  |
	| 20001    | 触发风控  |
	| 20002    | 资金不足  |
	| 500      | 内部错误  |
	| 0        | 成功     |
	| 50110505 | 重复请求  |
	| 50116003 | 金额必须大于0.0001  |
	| 50160000 | 无效参数  |
	*/
	//系统异常
	if err != nil && couponSendRes.Code == 0 {
		couponSendRes.Code = -1
		returnCode = -1
	} else {
		if couponSendRes.Code != consts.Success {
			returnCode = consts.ErrSystemError
			isReturn = 1
		}
	}

	return &SendCouponFormatResp{
		Code:       returnCode,
		CouponCode: couponSendRes.Code,
		Message:    couponSendRes.Message,
		IsReturn:   isReturn,
	}
}
