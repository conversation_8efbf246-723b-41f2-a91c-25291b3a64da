package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"context"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/models/welfare_user_register_info"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"time"
)

// 获取未登陆用户所在区域
func GetUnLoginUserBelongRegion(svcCtx *svc.ServiceContext, ctx context.Context, r *http.Request) (string, error) {
	countryId := utils.GetUserCountry(r)
	// 获取风控国家id配置集合
	riskCountryIdStr, err := GetWelfareConfig(svcCtx, ctx, consts.NewbieTaskRiskCountryList)
	if err != nil {
		logc.Infof(ctx, "region_service GetUnLoginUserBelongRegion GetWelfareConfig is err, param is:%s,err is:%v", consts.NewbieTaskRiskCountryList, err)
		return "", err
	}
	riskCountryIds := utils.ParseList(riskCountryIdStr)
	// 获取高净值国家id配置集合
	highWorthCountryIdStr, err := GetWelfareConfig(svcCtx, ctx, consts.NewbieTaskHighWorthCountryList)
	if err != nil {
		logc.Infof(ctx, "region_service GetUnLoginUserBelongRegion GetWelfareConfig is err, param is:%s,err is:%v", consts.NewbieTaskHighWorthCountryList, err)
		return "", err
	}
	highWorthCountryIds := utils.ParseList(highWorthCountryIdStr)
	// 获取sa国家id配置集合
	saCountryIdStr, err := GetWelfareConfig(svcCtx, ctx, consts.NewbieTaskSaCountryList)
	if err != nil {
		logc.Infof(ctx, "region_service GetUnLoginUserBelongRegion GetWelfareConfig is err, param is:%s,err is:%v", consts.NewbieTaskSaCountryList, err)
		return "", err
	}
	saCountryIds := utils.ParseList(saCountryIdStr)
	if utils.ContainsArray(saCountryIds, int64(countryId)) {
		return consts.NewbieTaskRegionSa, nil
	}
	if utils.ContainsArray(highWorthCountryIds, int64(countryId)) {
		return consts.NewbieTaskRegionHighWorth, nil
	}
	if utils.ContainsArray(riskCountryIds, int64(countryId)) {
		return consts.NewbieTaskRegionRisk, nil
	}

	return consts.NewbieTaskRegionCommon, nil
}

// 获取用户所在地区信息 兼容：未生成快照，新客期查地区，老客期查地区
func GetUserBelongRegion(svcCtx *svc.ServiceContext, ctx context.Context, uid int64, r *http.Request, registerCountryID, residenceCountryID int64, userRegisterInfo *welfare_user_register_info.WelfareUserRegisterInfo) (string, error) {
	if uid <= 0 {
		return consts.NewbieTaskRegionCommon, nil
	}
	//没有快照的情况或者没有地区数据的时候
	if userRegisterInfo == nil || userRegisterInfo.Id <= 0 || userRegisterInfo.Region == "" {
		//合规
		var err error
		var isComplianceRegion bool
		if registerCountryID != 0 {
			isComplianceRegion, err = NewComplianceCenterCall(ctx, svcCtx).IsComplianceRegion(utils.MustInt64(registerCountryID), r)
			if err != nil {
				return "", err
			}
		}
		var residenceIsComplianceRegion bool
		if residenceCountryID != 0 {
			residenceIsComplianceRegion, err = NewComplianceCenterCall(ctx, svcCtx).IsComplianceRegion(utils.MustInt64(residenceCountryID), r)
			if err != nil {
				return "", err
			}
		}

		if isComplianceRegion || residenceIsComplianceRegion {
			return consts.NewbieTaskRegionCompliance, nil
		}
		region, err := GetRegionByUid(svcCtx, ctx, uid, registerCountryID, residenceCountryID)
		return region, err
	}
	//新客期直接返回快照地区
	if userRegisterInfo.NewbieEndTime > time.Now().Unix() {
		return userRegisterInfo.Region, nil
	}
	//老客调用GetRegionByUid
	region, err := GetRegionByUid(svcCtx, ctx, uid, registerCountryID, residenceCountryID)
	return region, err
}

// 实时用户国家区域
func GetRegionByUid(svcCtx *svc.ServiceContext, ctx context.Context, uid, registerCountryID, residenceCountryID int64) (string, error) {
	if uid <= 0 {
		return consts.NewbieTaskRegionCommon, nil
	}

	if registerCountryID <= 0 && residenceCountryID <= 0 {
		userInfo, err := GetCenterUserInfoByUid(svcCtx, ctx, uid)
		if err != nil {
			logc.Infof(ctx, "user_service getUserBelongRegion GetCenterUserInfoByUid is err, uid is:%d,err is:%v", uid, err)
			return "", err
		}

		registerCountryID = utils.MustInt64(userInfo.RegisterCountryId)
		residenceCountryID = utils.MustInt64(userInfo.UserInfo.ResidenceCountryId)
	}

	// 获取风控国家id配置集合
	riskCountryIdStr, _ := GetWelfareConfig(svcCtx, ctx, consts.NewbieTaskRiskCountryList)
	riskCountryIds := utils.ParseList(riskCountryIdStr)
	if utils.ContainsArray(riskCountryIds, registerCountryID) || utils.ContainsArray(riskCountryIds, residenceCountryID) {
		return consts.NewbieTaskRegionRisk, nil
	}
	// 获取sa国家id配置集合
	saCountryIdStr, _ := GetWelfareConfig(svcCtx, ctx, consts.NewbieTaskSaCountryList)
	saCountryIds := utils.ParseList(saCountryIdStr)
	if utils.ContainsArray(saCountryIds, registerCountryID) || utils.ContainsArray(saCountryIds, residenceCountryID) {
		return consts.NewbieTaskRegionSa, nil
	}
	// 获取高净值国家id配置集合
	highWorthCountryIdStr, _ := GetWelfareConfig(svcCtx, ctx, consts.NewbieTaskHighWorthCountryList)
	highWorthCountryIds := utils.ParseList(highWorthCountryIdStr)
	if utils.ContainsArray(highWorthCountryIds, registerCountryID) || utils.ContainsArray(highWorthCountryIds, residenceCountryID) {
		return consts.NewbieTaskRegionHighWorth, nil
	}

	return consts.NewbieTaskRegionCommon, nil

}
