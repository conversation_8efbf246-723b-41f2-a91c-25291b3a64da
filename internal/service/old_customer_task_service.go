package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gateio/gateio-lib-common-go/requestools"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/models/welfare_user_tasks"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/types"
	"gateio_service_welfare_go/internal/utils"
	"net/http"
	"strconv"
	"time"
)

type OldCustomerTaskService struct {
	Logger logx.Logger
	ctx    context.Context
	svc    *svc.ServiceContext
}

func NewOldCustomerTaskService(ctx context.Context, svc *svc.ServiceContext) *OldCustomerTaskService {
	return &OldCustomerTaskService{Logger: logx.WithContext(ctx), ctx: ctx, svc: svc}
}

func (oc *OldCustomerTaskService) GetTaskList(taskType int64, r *http.Request) (*types.DailyTaskListFormatResp, error) {
	resp := &types.DailyTaskListFormatResp{
		List: []*types.DailyTaskListFormatItem{},
	}
	var respData types.DailyTaskListFormatResp
	var err error

	userInfo := requestools.GetUserInfo(r)
	uid := 0
	if userInfo != nil {
		uid = userInfo.UID
	}

	defer func() {
		utils.LogRouterData(oc.ctx, "oldCustomerGetTaskList", uid, taskType, resp, err)
	}()

	if uid <= 0 {
		return nil, consts.GetErrorMsg(r, consts.ErrUserLogin)
	}

	lang := requestools.GetUserLanguage(r)
	//接入缓存提高查询速度
	redisKey := ""
	if taskType == consts.UserTaskTypeVeteranDaily {
		redisKey = fmt.Sprintf(consts.WelfareDailyUserTasksKey, uid, lang)
	} else {
		redisKey = fmt.Sprintf(consts.WelfareLimitUserTasksKey, uid, lang)
	}

	redisVal, _ := oc.svc.Redis.Get(redisKey)
	if redisVal != "" {
		_ = json.Unmarshal([]byte(redisVal), &respData)
	}

	if respData.List != nil {
		oc.Logger.Infof("老客任务列表，读取缓存列表，uid:%d，type:%d", uid, taskType)
		resp = &respData
		return resp, nil
	}

	oc.Logger.Infof("老客任务列表，读取DB，uid:%d，type:%d", uid, taskType)

	userRegion, err := GetRegionByUid(oc.svc, oc.ctx, int64(uid), 0, 0)
	if err != nil {
		return nil, err
	}

	//获取用户最新的区域对应的列表
	taskCfg, err := dao.NewWelfareMysqlDBUtil(oc.svc.WelfareDB).QueryWelfareTaskCfgList(oc.ctx, userRegion, []int{int(taskType)}, 0)
	if err != nil {
		oc.Logger.Errorf("老客任务列表，获取用户最新的区域对应的列表失败，uid:%d", uid)
		return nil, err
	}

	//获取用户已经领取的任务
	userTaskList, err := dao.NewWelfareMysqlDBUtil(oc.svc.WelfareDB).GetUserTaskByUId(oc.ctx, int64(uid), consts.GetProcessTaskStatus(), int(taskType))
	if err != nil {
		oc.Logger.Errorf("老客任务列表获取用户已经领取的任务失败，uid:%d", uid)
		return nil, err
	}
	var allWelfareTaskId []int64
	taskIdMapUserTask := map[int64]*welfare_user_tasks.WelfareUserTasks{}
	for _, v := range userTaskList {
		allWelfareTaskId = append(allWelfareTaskId, v.TaskId)
		//此处存在已经过期，但是状态=2的任务，此类任务用户可以再次领取任务，再次领取任务的话，同时存在status=1和status=2的任务，需要过滤=2的任务，展示=1的任务
		if _, ok := taskIdMapUserTask[v.TaskCenterId]; !ok {
			taskIdMapUserTask[v.TaskCenterId] = v
		} else {
			if v.CreatedAt.Unix() > taskIdMapUserTask[v.TaskCenterId].CreatedAt.Unix() {
				taskIdMapUserTask[v.TaskCenterId] = v
			}
		}
	}

	if len(taskIdMapUserTask) > 0 {
		userTaskCfg, err := dao.NewWelfareMysqlDBUtil(oc.svc.WelfareDB).QueryWelfareTaskCfgListByIds(oc.ctx, allWelfareTaskId)
		if err != nil {
			oc.Logger.Errorf("老客任务列表获取用户已经领取的任务配置列表失败，uid:%d", uid)
			return nil, err
		}
		//合并当前用户区域最新的配置信息和已经领取的任务的配置信息
		//有可能用户之前领取的是高净值的任务，现在用户变成了普通任务
		taskCfg = append(taskCfg, userTaskCfg...)
	}

	if len(taskCfg) <= 0 {
		oc.Logger.Infof("老客任务列表没有配置，uid:%d", uid)
		return resp, nil
	}

	taskRealCfg, err := NewComplianceCenterCall(oc.ctx, oc.svc).GetComplianceTaskByKey(0, r, int(taskType), taskCfg)
	if err != nil {
		oc.Logger.Errorf("获取老客任务列表，获取合规失败，uid:%d,err:%v", uid, err)
		return nil, err
	}

	if len(taskRealCfg) <= 0 {
		oc.Logger.Infof("老客任务列表合规后，没有列表，uid:%d", uid)
		return resp, nil
	}

	var allTaskCenterId []int64
	for _, vvv := range taskRealCfg {
		allTaskCenterId = append(allTaskCenterId, vvv.TaskId)
	}

	//批量获取任务系统任务信息
	taskCenterInfoList, err := GetTaskListByTaskIds(oc.svc, oc.ctx, 0, allTaskCenterId)
	if err != nil {
		oc.Logger.Infof("老客任务列表，获取任务中台列表失败，uid:%d, err:%v", uid, err)
		return nil, err
	}

	//用户所有的每日任务，用于解锁逻辑
	var allUserTypeTask map[int64]*welfare_user_tasks.WelfareUserTasks
	if taskType == consts.UserTaskTypeVeteranDaily {
		allUserTypeTask, err = dao.NewWelfareMysqlDBUtil(oc.svc.WelfareDB).GetDailyTaskAllByUid(oc.ctx, int64(uid))
	} else {
		allUserTypeTask, err = dao.NewWelfareMysqlDBUtil(oc.svc.WelfareDB).GetLimitTaskAllByUid(oc.ctx, int64(uid))
	}

	if err != nil {
		return nil, err
	}

	var duplicateTask []int64
	if len(taskRealCfg) > 0 {
		for _, vv := range taskRealCfg {
			if _, ok := taskCenterInfoList[vv.TaskId]; !ok || taskCenterInfoList[vv.TaskId] == nil {
				oc.Logger.Errorf("获取老客任务列表，找不到对应的任务系统，uid:%d, task_id:%d", uid, vv.TaskId)
				continue
			}
			if taskCenterInfoList[vv.TaskId].Title == "" {
				oc.Logger.Infof("获取老客任务列表，任务的标题为空，uid:%d, task_id:%d", uid, vv.TaskId)
				continue
			}
			//合并之后，可能有重复的任务，需要去重
			if utils.ContainsArray(duplicateTask, vv.TaskId) {
				continue
			}

			//限时任务，需要过滤人群，不在人群中，过滤任务展示
			if taskType == consts.UserTaskTypeVeteranLimited && taskCenterInfoList[vv.TaskId].CrowdID > 0 {
				crowd, err := service_client.NewMarketingCenterCall(oc.ctx).CheckUserInCrowd(int64(uid), taskCenterInfoList[vv.TaskId].CrowdID)
				if err != nil {
					oc.Logger.Errorf("获取老客任务列表，请求人群检测接口失败，uid:%d|crowdId:%d|task_id:%d|err:%v", uid, taskCenterInfoList[vv.TaskId].CrowdID, vv.TaskId, err)
					continue
				}

				if crowd.UserInCrowd == false {
					oc.Logger.Infof("获取老客任务列表，不在人群中，uid:%d|task_id:%d|crowdId:%d", uid, vv.TaskId, taskCenterInfoList[vv.TaskId].CrowdID)
					continue
				}
			}

			status := int64(0)
			duplicateTask = append(duplicateTask, vv.TaskId)
			//领取任务的状态和进度条
			CurProcess := ""
			startTime := int64(0)

			taskCenterInfo := &task.Task{}
			var rewards []*TaskCenterReward

			if _, ok := allUserTypeTask[vv.TaskId]; ok {

				//是否使用任务快照信息
				useSnapshotTaskInfo := false
				statusStr, _ := oc.svc.Redis.Get(fmt.Sprintf(consts.WelfareUidAndTaskIdAndStatus, uid, allUserTypeTask[vv.TaskId].Id))
				if statusStr != "" {
					oc.Logger.Infof("获取老客任务列表，使用缓存里面的状态值，uid:%d|task_id:%d|businessId:%d", uid, vv.TaskId, allUserTypeTask[vv.TaskId].Id)
					status, _ = strconv.ParseInt(statusStr, 10, 64)
				} else {
					status = int64(int(allUserTypeTask[vv.TaskId].Status))
				}

				startTime = allUserTypeTask[vv.TaskId].CreatedAt.Unix()
				taskCenterInfo, err = GetTaskByTaskIdAndBusinessId(oc.svc, oc.ctx, uid, int(vv.TaskId), strconv.FormatInt(allUserTypeTask[vv.TaskId].Id, 10))
				if err != nil || taskCenterInfo == nil {
					oc.Logger.Errorf("获取老客任务列表，获取任务接口数据失败，uid:%d|task_id:%d|businessId:%d", uid, vv.TaskId, allUserTypeTask[vv.TaskId].Id)
					continue
				}

				//进行中的交易任务和入金任务有交易条
				if utils.ContainsArray(consts.GetProcessTaskStatus(), int(allUserTypeTask[vv.TaskId].Status)) {
					if utils.ContainsArray([]int{consts.UserTaskTaskTypeSpots, consts.UserTaskTaskTypeFutures, consts.UserTaskTaskTypeDeposits}, int(vv.TaskType)) {
						RecordSchedule, err := service_client.NewTaskCenterCall(oc.ctx).RecordSchedule(int64(uid), vv.TaskId, consts.WelfareTaskBusinessType, strconv.FormatInt(taskIdMapUserTask[vv.TaskId].Id, 10))
						if err == nil {
							CurProcess = utils.MustString(RecordSchedule.Amount)
						}
					}
				}

				//如果任务在待领奖状态下24小时后还没领取，展示待领取状态时，需要用最新的奖励信息展示，反之，需要展示快照奖励信息
				if allUserTypeTask[vv.TaskId].Status == consts.StatusDone && time.Now().Unix() > taskCenterInfo.EndTime+24*3600 {
					useSnapshotTaskInfo = true
				}
				//如果已经领取的任务已经变为终态，展示待领取状态时，需要用最新的奖励信息展示，如果待解锁状态，需要展示快照奖励信息
				if utils.ContainsArray(consts.GetDoneTaskStatus(), int(allUserTypeTask[vv.TaskId].Status)) && time.Now().Unix() > taskCenterInfo.EndTime {
					useSnapshotTaskInfo = true
				}

				//存在任务，但是已经到了下次循环，前端展示待领取时，需要判断任务的时间
				if useSnapshotTaskInfo {
					if !oc.CheckTaskTime(taskCenterInfoList[vv.TaskId]) {
						oc.Logger.Infof("获取老客任务列表，固定时间，时间不符合，过滤，uid:%d|task_id:%d", uid, vv.TaskId)
						continue
					}
					rewards = GetTaskCenterReward(taskCenterInfoList[vv.TaskId])
				} else {
					rewards = GetTaskCenterReward(taskCenterInfo)
				}

			} else {
				taskCenterInfo = taskCenterInfoList[vv.TaskId]
				//未领取的任务，需要判断任务的时间
				if !oc.CheckTaskTime(taskCenterInfo) {
					oc.Logger.Infof("获取老客任务列表，固定时间，时间不符合，过滤，uid:%d|task_id:%d", uid, vv.TaskId)
					continue
				}
				rewards = GetTaskCenterReward(taskCenterInfo)
			}

			copyInfo := taskCenterInfo.CopyList[lang]
			//默认使用英文标题和描述
			if copyInfo.Desc == "" && copyInfo.Title == "" {
				copyInfo = taskCenterInfo.CopyList[consts.EN]
			}

			//总任务要求完成量
			taskTotalNum := int64(0)
			var prizeInfo []types.DailyTaskListPrizeInfo
			for _, vvv := range rewards {
				prizeInfoItem := types.DailyTaskListPrizeInfo{
					PrizeType: vvv.RewardType,
					PrizeNum:  vvv.RewardNum,
					TaskNum:   vvv.MinNum,
					Status:    vvv.Status,
				}
				prizeInfo = append(prizeInfo, prizeInfoItem)
				taskTotalNum = max(taskTotalNum, vvv.MinNum)
			}

			descStr := DealTaskDesc(copyInfo.Desc, taskTotalNum, 0, "", taskCenterInfo.ClaimDays)

			isShowRemainingTime := consts.CommonNo
			if taskCenterInfo.EffectiveTimeType == consts.TaskEffectiveTimeTypeDrawDown {
				isShowRemainingTime = consts.CommonYes
			}

			endTime := taskCenterInfo.EndTime
			//首次领取任务，可能任务中台的数据有延迟，没有值的情况下，拿开始时间+天数兜底
			//非首次领取任务，可能任务中台的数据有延迟，拿到的还是之前那一笔的结束时间，拿开始时间+天数兜底
			if endTime <= 0 || endTime <= startTime {
				//如果是领取后，则使用开始时间+领取天数来计算，如果是固定时间，则直接取任务配置的结束时间（因为没有天数）
				if taskCenterInfo.EffectiveTimeType == consts.TaskEffectiveTimeTypeDrawDown {
					endTime = startTime + taskCenterInfo.ClaimDays*3600*24
				} else {
					endTime = taskCenterInfo.TaskEndTime
				}
			}

			hiddenTime := int64(0)
			//未领奖的情况下，要保留24H
			if status == consts.StatusDone || status == consts.StatusInSettlement {
				hiddenTime = endTime + 24*3600
			}

			remainingTime := int64(0)
			if status > 0 {
				remainingTime = endTime
			}

			taskListItem := &types.DailyTaskListFormatItem{
				ID:                  vv.Id,
				TaskType:            vv.TaskType,
				Title:               copyInfo.Title,
				TitleHover:          copyInfo.TaskDesc,
				Desc:                descStr,
				CurProcess:          CurProcess,
				ActionType:          vv.ButtonType,
				RemainingTime:       remainingTime,
				IsShowRemainingTime: int64(isShowRemainingTime),
				StartTime:           startTime,
				Status:              status,
				HiddenTime:          hiddenTime,
				PrizeInfo:           prizeInfo,
			}
			resp.List = append(resp.List, taskListItem)
		}
	}

	byteVal, err := json.Marshal(resp)
	if err == nil {
		//将结果缓存1分钟
		_ = oc.svc.Redis.Setex(redisKey, string(byteVal), 60)
	}

	return resp, err

}

func (oc *OldCustomerTaskService) CheckTaskTime(taskCenterInfo *task.Task) bool {
	if taskCenterInfo.EffectiveTimeType == consts.TaskEffectiveTimeTypeFixed {
		if time.Now().Unix() < taskCenterInfo.TaskStartTime || time.Now().Unix() > taskCenterInfo.TaskEndTime {
			return false
		}
	}
	return true
}
