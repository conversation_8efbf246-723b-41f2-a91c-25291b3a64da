package service

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/datacenter"
	"context"
	"encoding/json"
	"errors"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/utils"
	"github.com/shopspring/decimal"
)

func GetDataCenterAppId() int64 {
	if utils.CheckDev() {
		return consts.DataCenterUserDepositTradingAppIdDev
	}
	return consts.DataCenterUserDepositTradingAppIdPro
}

// 获取用户任务结束后的入金量和交易量信息
func GetUserDepositTrading(ctx context.Context, uid int64, business []string, startTime, endTime, depositWithdraType string) (float64, error) {
	//为了避免慢查询使用分页查询
	pageNo := int64(1)
	pageSize := int64(100)
	//总数量
	changeUsdtDecimalAll := decimal.NewFromFloat(0)
	paramMap := map[string]interface{}{
		"ads.ads_user_asset_deposits_v2_hi.user_id":              uid,
		"ads.ads_user_asset_deposits_v2_hi.business":             business,
		"ads.ads_user_asset_deposits_v2_hi.begin_time":           startTime,
		"ads.ads_user_asset_deposits_v2_hi.end_time":             endTime,
		"ads.ads_user_asset_deposits_v2_hi.deposit_withdra_type": depositWithdraType,
	}
	if business == nil || len(business) == 0 {
		delete(paramMap, "ads.ads_user_asset_deposits_v2_hi.business")
	}
	for {
		req := &datacenter.QueryExecRequest{
			ApiID:        GetDataCenterAppId(),
			FilterParams: paramMap,
			Page:         pageNo,
			PageSize:     pageSize,
		}
		reqBytes, err := json.Marshal(req)
		resp, err := datacenter.NewClient().QueryExec(ctx, req)
		if err != nil {
			logc.Errorf(ctx, "data_center_service GetUserDepositTrading  uid is:%d ,params is:%s ,datacenter.QueryExec is err ,err is:%v", uid, string(reqBytes), err)
			return 0, err
		}
		respStructListVal, ok := resp.List.([]interface{})
		if !ok {
			respByte, _ := json.Marshal(resp)
			logc.Errorf(ctx, "data_center_service GetUserDepositTrading  uid is:%d ,params is:%s , respStructListVal is error ,resp is: %s", uid, string(reqBytes), string(respByte))
			return 0, errors.New("respStructListVal len is 0")
		}

		for _, respStruct := range respStructListVal {
			respStructByte, _ := json.Marshal(respStruct)

			respStructMap, ok := respStruct.(map[string]interface{})
			if !ok {
				logc.Errorf(ctx, "data_center_service GetUserDepositTrading  uid is:%d ,params is:%s , respStruct is not ok ,respStruct is: %s", uid, string(reqBytes), string(respStructByte))
				return 0, errors.New("respStructMap is not ok")
			}
			//获取USDT金额变化值
			changeUsdtDecimal, ok1 := respStructMap["change_usdt"].(string)
			if !ok1 {
				logc.Errorf(ctx, "data_center_service GetUserDepositTrading  uid is:%d ,params is:%s , changeUsdtDecimal is error ,respStruct is: %s", uid, string(reqBytes), string(respStructByte))
				return 0, errors.New("changeUsdtDecimal is not ok")
			}
			// 把字符串转换为 decimal.Decimal 类型
			num, err := decimal.NewFromString(changeUsdtDecimal)
			if err != nil {
				logc.Errorf(ctx, "data_center_service NewFromString  uid is:%d ,params is:%s , decimal.NewFromString(changeUsdtDecimal) is error ,respStruct is: %s", uid, string(reqBytes), changeUsdtDecimal)
				return 0, errors.New("changeUsdtDecimal is not ok")
			}
			changeUsdtDecimalAll = changeUsdtDecimalAll.Add(num)
		}

		//分页查询，如果当前页码所查条数大于总条数就跳出循环
		if pageNo*pageSize >= resp.TotalCount {
			break
		}
	}

	// 将decimal转回float64
	return changeUsdtDecimalAll.InexactFloat64(), nil
}
