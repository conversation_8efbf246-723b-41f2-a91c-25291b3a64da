package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"bitbucket.org/gatebackend/go-zero/core/logc"

	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/svc"
	"gateio_service_welfare_go/internal/utils"

	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/task"
)

// 获取任务列表信息
// 根据任务中心任务ID列表获取任务信息并放入缓存
func GetTaskListByTaskIds(svcCtx *svc.ServiceContext, ctx context.Context, uid int, taskCenterIds []int64) (map[int64]*task.Task, error) {
	if len(taskCenterIds) < 1 || len(taskCenterIds) > 50 {
		return nil, errors.New("taskCenterId is invalid")
	}
	taskMap := map[int64]*task.Task{}
	// 定义当前缓存中没有的任务中心的任务ID
	queryTaskIds := make([]int64, 0, len(taskCenterIds))
	// 先从缓存中获取
	for _, taskCenterId := range taskCenterIds {
		key := fmt.Sprintf(consts.TaskCenterTaskInfoKey, taskCenterId)
		if uid > 0 {
			key = fmt.Sprintf(consts.TaskCenterUserTaskInfoKey, uid, taskCenterId)
		}
		redisVal, _ := svcCtx.Redis.Get(key)
		if redisVal != "" {
			taskInfo := task.Task{}
			_ = json.Unmarshal([]byte(redisVal), &taskInfo)
			if taskInfo.TaskID != 0 {
				taskMap[taskInfo.TaskID] = &taskInfo
			} else {
				queryTaskIds = append(queryTaskIds, taskCenterId)
			}
		} else {
			queryTaskIds = append(queryTaskIds, taskCenterId)
		}

	}
	if len(queryTaskIds) > 0 {
		resp, err := service_client.NewTaskCenterCall(ctx).BatchTaskAll(int64(uid), consts.WelfareTaskBusinessType, "", utils.IntListToStr(queryTaskIds))
		if err != nil {
			logc.Errorf(ctx, "service_client.NewTaskCenterClient().BatchTaskAllByTaskIds is err:%v", err)
			return nil, err
		}
		for _, taskInfo := range resp.List {
			taskMap[taskInfo.TaskID] = &taskInfo
			byteVal, err := json.Marshal(taskInfo)
			if err == nil {
				if uid > 0 {
					// 缓存十分钟
					_ = svcCtx.Redis.Setex(fmt.Sprintf(consts.TaskCenterUserTaskInfoKey, uid, taskInfo.TaskID), string(byteVal), 300)
				} else {
					// 缓存十分钟
					_ = svcCtx.Redis.Setex(fmt.Sprintf(consts.TaskCenterTaskInfoKey, taskInfo.TaskID), string(byteVal), 300)
				}
			}
		}
	}
	return taskMap, nil
}

// 获取已领取任务的任务列表
func GetTaskByTaskIdAndBusinessId(svcCtx *svc.ServiceContext, ctx context.Context, uid, taskId int, businessId string) (*task.Task, error) {
	if uid <= 0 || taskId <= 0 || businessId == "" {
		return nil, errors.New("GetTaskByTaskIdAndBusinessId params is invalid")
	}

	key := fmt.Sprintf(consts.TaskCenterUserTaskBusinessKey, uid, taskId, businessId)
	redisVal, _ := svcCtx.Redis.Get(key)
	if redisVal != "" {
		taskInfo := &task.Task{}
		_ = json.Unmarshal([]byte(redisVal), &taskInfo)
		if taskInfo.TaskID != 0 {
			return taskInfo, nil
		}
	}

	resp, err := service_client.NewTaskCenterCall(ctx).BatchTaskAll(int64(uid), consts.WelfareTaskBusinessType, businessId, strconv.Itoa(taskId))
	if err != nil {
		logc.Errorf(ctx, "service_client.NewTaskCenterClient().BatchTaskAll is failed, uid:%d, taskId:%d, businessId:%s, mistake:%v", uid, taskId, businessId, err)
		return nil, err
	}

	if len(resp.List) <= 0 {
		logc.Errorf(ctx, "service_client.NewTaskCenterClient().BatchTaskAll is empty, uid:%d, taskId:%d, businessId:%s", uid, taskId, businessId)
		return nil, errors.New("service_client.NewTaskCenterClient().BatchTaskAll is empty")
	}

	taskInfo := resp.List[0]
	byteVal, err := json.Marshal(taskInfo)
	if err == nil {
		_ = svcCtx.Redis.Setex(key, string(byteVal), 300)
	}

	return &taskInfo, nil
}

// 获取任务中的奖励信息
func GetTaskCenterReward(taskInfo *task.Task) []*TaskCenterReward {
	if taskInfo == nil {
		return []*TaskCenterReward{}
	}
	rewards := make([]*TaskCenterReward, 0, 20)

	for _, rule := range taskInfo.RuleInfo {
		for _, condition := range rule.Conditions {
			rewardCouponId := int64(0)
			rewardSource := ""
			// 类型断言
			p := map[string]interface{}{}
			if condition.PrizeType == consts.PrizeTypeCoupon {
				_ = json.Unmarshal([]byte(condition.PrizeInfo), &p)
			}
			if val, ok := p["coupon_id"]; ok {
				if couponID, ok1 := val.(float64); ok1 {
					rewardCouponId = int64(couponID)
				}
			}
			if val, ok := p["coupon_source"]; ok {
				if couponSource, ok1 := val.(string); ok1 {
					rewardSource = couponSource
				}
			}

			minNum := condition.Min
			if len(condition.ConditionDetail) > 1 || minNum == 0 {
				for _, conditionDetail := range condition.ConditionDetail {
					if utils.ContainsArray(consts.GetTaskCenterMinMarks(), conditionDetail.Mark) {
						minNum = conditionDetail.Min
					}
				}
			}

			extraRewardType := int64(0)
			extraRewardNum := int64(0)
			extraRewardCouponId := int64(0)
			extraRewardSource := ""
			if condition.IsExtraPrize == 1 {
				extraRewardNum = condition.ExtraPrizeNum - condition.PrizeNum
				extraRewardType = condition.ExtraPrizeType
				// 类型断言
				if p, ok := condition.ExtraPrizeInfo.(map[string]interface{}); ok {
					if val, ok1 := p["coupon_id"]; ok1 {
						if couponID, ok2 := val.(float64); ok2 {
							extraRewardCouponId = int64(couponID)
						}
					}
					if val, ok1 := p["coupon_source"]; ok1 {
						if couponSource, ok2 := val.(string); ok2 {
							extraRewardSource = couponSource
						}
					}
				}
			}
			rewards = append(rewards, &TaskCenterReward{
				Mark:                rule.Mark,
				MinNum:              minNum,
				RewardType:          condition.PrizeType,
				RewardNum:           condition.PrizeNum,
				RewardCouponId:      rewardCouponId,
				RewardSource:        rewardSource,
				ExtraRewardCouponId: extraRewardCouponId,
				ExtraRewardSource:   extraRewardSource,
				ExtraRewardType:     extraRewardType,
				ExtraRewardNum:      extraRewardNum,
				Status:              condition.Status,
			})
		}
	}
	return rewards
}

// 获取任务列表的奖励信息
func QueryTaskCenterReward(taskList []*task.Task) map[int64][]*TaskCenterReward {
	rewardMap := make(map[int64][]*TaskCenterReward)
	for _, taskInfo := range taskList {
		rewards := make([]*TaskCenterReward, 0, 20)
		for _, rule := range taskInfo.RuleInfo {
			for _, condition := range rule.Conditions {
				rewardCouponId := int64(0)
				rewardSource := ""
				p := map[string]interface{}{}
				if condition.PrizeType == consts.PrizeTypeCoupon {
					_ = json.Unmarshal([]byte(condition.PrizeInfo), &p)
				}
				if val, ok := p["coupon_id"]; ok {
					if couponID, ok1 := val.(float64); ok1 {
						rewardCouponId = int64(couponID)
					}
				}
				if val, ok := p["coupon_source"]; ok {
					if couponSource, ok1 := val.(string); ok1 {
						rewardSource = couponSource
					}
				}
				extraRewardType := int64(0)
				extraRewardNum := int64(0)
				extraRewardCouponId := int64(0)
				extraRewardSource := ""
				if condition.IsExtraPrize == 1 {
					extraRewardNum = condition.ExtraPrizeNum - condition.PrizeNum
					extraRewardType = condition.ExtraPrizeType
					// 类型断言
					if p, ok := condition.ExtraPrizeInfo.(map[string]interface{}); ok {
						if val, ok1 := p["coupon_id"]; ok1 {
							if couponID, ok2 := val.(float64); ok2 {
								extraRewardCouponId = int64(couponID)
							}
						}
						if val, ok1 := p["coupon_source"]; ok1 {
							if couponSource, ok2 := val.(string); ok2 {
								extraRewardSource = couponSource
							}
						}
					}
				}
				minNum := condition.Min
				if len(condition.ConditionDetail) > 1 || minNum == 0 {
					for _, conditionDetail := range condition.ConditionDetail {
						if utils.ContainsArray(consts.GetTaskCenterMinMarks(), conditionDetail.Mark) {
							minNum = conditionDetail.Min
						}
					}
				}
				rewards = append(rewards, &TaskCenterReward{
					Mark:                rule.Mark,
					MinNum:              minNum,
					RewardType:          condition.PrizeType,
					RewardNum:           condition.PrizeNum,
					RewardCouponId:      rewardCouponId,
					RewardSource:        rewardSource,
					ExtraRewardCouponId: extraRewardCouponId,
					ExtraRewardSource:   extraRewardSource,
					ExtraRewardType:     extraRewardType,
					ExtraRewardNum:      extraRewardNum,
				})
			}
		}
		rewardMap[taskInfo.TaskID] = rewards
	}
	return rewardMap
}

// 获取任务中的奖励详细信息
func GetTaskCenterRewardDetail(svcCtx *svc.ServiceContext, ctx context.Context, taskInfo task.Task) []*TaskCenterRewardDetail {
	rewards := make([]*TaskCenterRewardDetail, 0, 20)
	for _, rule := range taskInfo.RuleInfo {
		for _, condition := range rule.Conditions {
			rewardCouponId := int64(0)
			rewardSource := ""

			rewardName := ""
			extraRewardName := ""

			p := map[string]interface{}{}
			_ = json.Unmarshal([]byte(condition.PrizeInfo), &p)

			if condition.PrizeType == consts.PrizeTypeCoupon {
				if val, ok := p["coupon_id"]; ok {
					if couponID, ok := val.(float64); ok {
						rewardCouponId = int64(couponID)
					} else {
						continue
					}
				} else {
					continue
				}
				if val, ok := p["coupon_source"]; ok {
					if source, ok := val.(string); ok {
						rewardSource = source
					} else {
						continue
					}
				} else {
					continue
				}
				if rewardCouponId == int64(0) || rewardSource == "" {
					continue
				}
				couponInfo, _ := GetCouponInfo(svcCtx, ctx, rewardCouponId, rewardSource)
				rewardName = couponInfo.CouponName
			} else {
				rewardName = fmt.Sprintf("%d%s", condition.PrizeNum, consts.GetTaskRewardTypeCNMap()[condition.PrizeType])
			}

			extraRewardType := int64(0)
			extraRewardNum := int64(0)
			extraRewardCouponId := int64(0)
			extraRewardSource := ""
			if condition.IsExtraPrize == 1 {
				extraRewardNum = condition.ExtraPrizeNum - condition.PrizeNum
				extraRewardType = condition.ExtraPrizeType
				// 类型断言
				if p, ok := condition.ExtraPrizeInfo.(map[string]interface{}); ok {
					if condition.ExtraPrizeType == consts.PrizeTypeCoupon {
						if val, ok := p["coupon_id"]; ok {
							if couponID, ok := val.(float64); ok {
								extraRewardCouponId = int64(couponID)
							} else {
								continue
							}
						} else {
							continue
						}
						if val, ok := p["coupon_source"]; ok {
							if source, ok := val.(string); ok {
								extraRewardSource = source
							} else {
								continue
							}
						} else {
							continue
						}
						if extraRewardCouponId == int64(0) || extraRewardSource == "" {
							continue
						}
						extraCouponInfo, _ := GetCouponInfo(svcCtx, ctx, extraRewardCouponId, extraRewardSource)
						extraRewardName = extraCouponInfo.CouponName
					} else {
						extraRewardName = fmt.Sprintf("%d%s", condition.ExtraPrizeNum, consts.GetTaskRewardTypeCNMap()[condition.ExtraPrizeType])
					}
				}
			}
			minNum := condition.Min
			if len(condition.ConditionDetail) > 1 || minNum == 0 {
				for _, conditionDetail := range condition.ConditionDetail {
					if utils.ContainsArray(consts.GetTaskCenterMinMarks(), conditionDetail.Mark) {
						minNum = conditionDetail.Min
					}
				}
			}
			rewards = append(rewards, &TaskCenterRewardDetail{
				Mark:                rule.Mark,
				MinNum:              minNum,
				RewardType:          condition.PrizeType,
				RewardNum:           condition.PrizeNum,
				RewardCouponId:      rewardCouponId,
				RewardSource:        rewardSource,
				ExtraRewardCouponId: extraRewardCouponId,
				ExtraRewardSource:   extraRewardSource,
				ExtraRewardType:     extraRewardType,
				ExtraRewardNum:      extraRewardNum,
				RewardName:          rewardName,
				ExtraRewardName:     extraRewardName,
			})
		}
	}
	return rewards
}

//处理任务描述
/*
taskDesc:任务描述
taskMin:规则数值
receiveNum:奖励数值
currency:单位

以下是描述中使用的关键字
{LightTaskMin} 高亮规则数值
{LightCouponAmount} 高亮奖励数值
{LightCouponCurrency} 高亮币种(单位)
{TaskMin} 规则数值
{CouponAmount} 奖励数值
{CouponCurrency} 币种(单位)
{Day} 天数
*/
func DealTaskDesc(taskDesc string, taskMin int64, receiveNum int64, currency string, day int64) string {
	// 处理描述 替换条件值
	//两个变量之间，加上空格
	taskDesc = strings.ReplaceAll(taskDesc, "}{", "} {")
	if strings.Contains(taskDesc, "{TaskMin}") {
		taskDesc = strings.Replace(taskDesc, "{TaskMin}", strconv.FormatInt(taskMin, 10), -1)
	}
	if strings.Contains(taskDesc, "{CouponAmount}") {
		taskDesc = strings.Replace(taskDesc, "{CouponAmount}", strconv.FormatInt(receiveNum, 10), -1)
	}
	if strings.Contains(taskDesc, "{CouponCurrency}") {
		taskDesc = strings.Replace(taskDesc, "{CouponCurrency}", currency, -1)
	}
	if strings.Contains(taskDesc, "{Day}") {
		taskDesc = strings.Replace(taskDesc, "{Day}", strconv.FormatInt(day, 10), -1)
	}
	// 处理高亮信息
	if strings.Contains(taskDesc, "{LightTaskMin}") {
		taskDesc = strings.Replace(taskDesc, "{LightTaskMin}", fmt.Sprintf("<span>%s</span>", strconv.FormatInt(taskMin, 10)), -1)
	}
	if strings.Contains(taskDesc, "{LightCouponAmount}") {
		taskDesc = strings.Replace(taskDesc, "{LightCouponAmount}", fmt.Sprintf("<span>%s</span>", strconv.FormatInt(receiveNum, 10)), -1)
	}

	if strings.Contains(taskDesc, "{LightCouponCurrency}") {
		taskDesc = strings.Replace(taskDesc, "{LightCouponCurrency}", fmt.Sprintf("<span>%s</span>", currency), -1)
	}
	return taskDesc
}

func GetEffectiveTime(item task.ListItem) string {
	// 将秒数转换为日期时间
	startTime := time.Unix(item.StartTime, 0).Format("2006-01-02 15:04:05")
	endTime := time.Unix(item.EndTime, 0).Format("2006-01-02 15:04:05")

	if item.EffectiveTimeType == 1 {
		// 固定时间范围，任务开始时间 - 结束时间
		return fmt.Sprintf("%s - %s", startTime, endTime)
	} else if item.EffectiveTimeType == 2 {
		// 领取后，天数：claim_days
		return fmt.Sprintf("领取后 %d 天", item.ClaimDays)
	}
	return ""
}
