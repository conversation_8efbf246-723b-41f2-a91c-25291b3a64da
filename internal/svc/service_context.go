package svc

import (
	"context"

	"gateio_service_welfare_go/internal/config"
	"gateio_service_welfare_go/internal/middleware"

	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/core/logx"
	"bitbucket.org/gatebackend/go-zero/core/proc"
	"bitbucket.org/gatebackend/go-zero/core/stores/redis"
	"bitbucket.org/gatebackend/go-zero/rest"
	"bitbucket.org/gatebackend/gorm-zero/gormc/config/mysql"
	"bitbucket.org/gateio/gateio-lib-base-go/encoding"
	cmiddleware "bitbucket.org/gateio/gateio-lib-common-go/middleware"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config              *config.Config
	waitForCalled       func()
	JsonHandler         encoding.Codec
	WelfareDB           *gorm.DB
	GrowthDataWelfareDB *gorm.DB
	FeeSettingDB        *gorm.DB
	Redis               *redis.Redis
	// 中间件
	TgWebhookAuthMiddleware rest.Middleware
	CronMiddleware          rest.Middleware
	SignatureEx             rest.Middleware
	AuthorizeEx             rest.Middleware
	Authorize               rest.Middleware
	KafkaConf               *config.KafkaConf
	UserCenterKafkaConf     *config.KafkaConf
	// 中间件
	Signature                   rest.Middleware // 验签中间件，验证请求数据的完整性和合法性，常用于防止数据篡改和伪造请求
	Authorization               rest.Middleware // 身份认证授权中间件，验证用户身份和访问权限，保护受限资源
	AuthMiddleware              rest.Middleware
	AuthAllowMiddleware         rest.Middleware
	ReplaceHtmlEntityMiddleware rest.Middleware
	RiskServiceUrl              string
	RiskGatewayAppId            string
}

func NewServiceContext(c *config.Config) *ServiceContext {
	/*waitForCalled := proc.AddShutdownListener(func() {
		// 当收到来自 os 的退出信号时会自动执行这个方法内部的逻辑
		// 对于需要关闭的资源, 可以在这里处理退出的逻辑, 比如 db.Close
		logx.Info("closing resources...")
	})*/

	ctx := context.Background()
	logc.Infof(ctx, "NewServiceContext")
	// 福利中心数据库
	dBWelfare, err := mysql.Connect(c.MysqlDefaultWelfare)
	if err != nil {
		logx.Errorf("mysql MysqlWelfare connection is not initialized, err: %v", err)
	}
	// 福利中心growth-data数据库
	dBGrowthDataWelfare, err := mysql.Connect(c.MysqlGrowthDataWelfare)
	if err != nil {
		logx.Errorf("mysql MysqlGrowthDataWelfare connection is not initialized, err: %v", err)
	}
	dBFeeSetting, err := mysql.Connect(c.MysqlFeeSettings)
	if err != nil {
		logx.Errorf("mysql MysqlFeeSetting connection is not initialized, err: %v", err)
	}
	// 用来监听 os.Signals, 当收到信号后关闭资源
	waitForCalled := proc.AddShutdownListener(func() {
		logx.Info("closing resources...")
		welfareDb, err := dBWelfare.DB()
		if err != nil {
			logx.Errorf("获取 dBWelfare 数据库链接失败, err: %v", err)
			return
		}
		err = welfareDb.Close()
		if err != nil {
			logx.Errorf("关闭 dBWelfare 数据库链接失败, err: %v", err)
			return
		}
		GrowthDataWelfareDb, err := dBGrowthDataWelfare.DB()
		if err != nil {
			logx.Errorf("获取 dBWelfare 数据库链接失败, err: %v", err)
			return
		}
		err = GrowthDataWelfareDb.Close()
		if err != nil {
			logx.Errorf("关闭 dBWelfare 数据库链接失败, err: %v", err)
			return
		}

		feeSetingDb, err := dBFeeSetting.DB()
		if err != nil {
			logx.Errorf("获取 feeSetingDb 数据库链接失败, err: %v", err)
			return
		}
		err = feeSetingDb.Close()
		if err != nil {
			logx.Errorf("关闭 feeSetingDb 数据库链接失败, err: %v", err)
			return
		}
	})

	// 初始化Redis
	logx.Must(c.Redis.Validate())

	rds := redis.MustNewRedis(c.Redis)

	logc.Infof(ctx, "rds connected")

	return &ServiceContext{
		Config:              c,
		waitForCalled:       waitForCalled,
		Redis:               rds,
		WelfareDB:           dBWelfare,
		GrowthDataWelfareDB: dBGrowthDataWelfare,
		FeeSettingDB:        dBFeeSetting,
		KafkaConf:           c.KafkaConf,
		UserCenterKafkaConf: c.UserCenterKafkaConf,
		// 中间件
		Signature:                   cmiddleware.Signature(),
		Authorization:               cmiddleware.Authorize(),
		AuthMiddleware:              middleware.NewAuthMiddleware().Handle,
		AuthAllowMiddleware:         middleware.NewAuthAllowMiddleware().Handle,
		ReplaceHtmlEntityMiddleware: middleware.NewReplaceHtmlEntityMiddleware().Handle,
		RiskServiceUrl:              c.RiskServiceUrl,
		RiskGatewayAppId:            c.RiskGatewayAppId,
	}
}

func (sc *ServiceContext) Close() {
	if sc.waitForCalled != nil {
		sc.waitForCalled()
	}
}
