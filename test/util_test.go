package test

import (
	"bitbucket.org/gatebackend/go-zero/core/logc"
	"bitbucket.org/gatebackend/go-zero/job/xxljob"
	"bitbucket.org/gateio/gateio-lib-service-call-go/servicecall/rebate"
	"context"
	"encoding/json"
	"fmt"
	"gateio_service_welfare_go/internal/consts"
	"gateio_service_welfare_go/internal/dao"
	"gateio_service_welfare_go/internal/mqs/kafka"
	"gateio_service_welfare_go/internal/service"
	"gateio_service_welfare_go/internal/service_client"
	"gateio_service_welfare_go/internal/task/advanced_task_send_prize"
	"gateio_service_welfare_go/internal/task/clean_user_points"
	"gateio_service_welfare_go/internal/task/demo"
	"gateio_service_welfare_go/internal/task/risk_warning"
	"gateio_service_welfare_go/internal/task/task_receive_coupon"
	"gateio_service_welfare_go/internal/utils"
	"go.opentelemetry.io/otel"
	"testing"
)

func TestGetWeek(t *testing.T) {
	fmt.Println(utils.GetDateByWeekday(0))
}

func TestDemo(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")

	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	msg := demo.Demo(ctx, svcCtx, &xxljob.TaskRequest{})
	// 检查返回的结果并处理潜在的错误
	if msg == "" {
		t.Errorf("预期返回信息，但得到的是空字符串")
	} else {
		t.Log("任务返回信息:", msg)
	}

}

func TestGetWelfareConfigByName(t *testing.T) {
	// 初始化测试环境
	ctx := context.Background()

	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	data, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).GetWelfareConfigByName(ctx, "newbie_task_black_country_list")
	if err != nil {
		fmt.Println("err is:", err)
	}
	fmt.Println("data is:", data.Config)
	val, err := service.GetWelfareConfig(svcCtx, ctx, "newbie_task_black_country_list")
	if err != nil {
		fmt.Println("err is:", err)
	}
	fmt.Println("val is:", val)
}

func TestUpdateTaskStatus(t *testing.T) {
	// 初始化测试环境
	ctx := context.Background()

	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	data, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).UpdateTaskStatus(ctx, svcCtx, 0, 3111906984, 4)
	if err != nil {
		fmt.Println("err is:", err)
	}
	fmt.Println("data is:", data)
	val, err := service.GetWelfareConfig(svcCtx, ctx, "newbie_task_black_country_list")
	if err != nil {
		fmt.Println("err is:", err)
	}
	fmt.Println("val is:", val)
}

func TestAdvancedTaskSendPrize(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	msg := advanced_task_send_prize.AdvancedTaskSendPrize(ctx, svcCtx, &xxljob.TaskRequest{})
	// 检查返回的结果并处理潜在的错误
	if msg == "" {
		t.Errorf("预期返回信息，但得到的是空字符串")
	} else {
		t.Log("任务返回信息:", msg)
	}
}

func TestRiskWarningDayCommand(t *testing.T) {
	// 初始化服务上下文
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	msg := risk_warning.RiskWarningDayCommand(ctx, svcCtx, &xxljob.TaskRequest{})
	// 检查返回的结果并处理潜在的错误
	if msg == "" {
		t.Errorf("预期返回信息，但得到的是空字符串")
	} else {
		t.Log("任务返回信息:", msg)
	}
}

func TestRiskWarningHourCommand(t *testing.T) {
	// 初始化服务上下文
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	msg := risk_warning.RiskWarningHourCommand(ctx, svcCtx, &xxljob.TaskRequest{})
	// 检查返回的结果并处理潜在的错误
	if msg == "" {
		t.Errorf("预期返回信息，但得到的是空字符串")
	} else {
		t.Log("任务返回信息:", msg)
	}
}

func TestRiskWarningCommand(t *testing.T) {
	// 初始化服务上下文
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	msg := risk_warning.RiskWarningCommand(ctx, svcCtx, &xxljob.TaskRequest{})
	// 检查返回的结果并处理潜在的错误
	if msg == "" {
		t.Errorf("预期返回信息，但得到的是空字符串")
	} else {
		t.Log("任务返回信息:", msg)
	}
}

func TestCleanUserPoints(t *testing.T) {
	// 初始化服务上下文
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	msg := clean_user_points.CleanUserPoints(ctx, svcCtx, &xxljob.TaskRequest{
		ExecutorParams: "[2124430448]",
		//ExecutorParams: "",
	})
	// 检查返回的结果并处理潜在的错误
	if msg == "" {
		t.Errorf("预期返回信息，但得到的是空字符串")
	} else {
		t.Log("任务返回信息:", msg)
	}
}

func TestCheckSendCoupon(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	NewCouponCenterCall := service_client.NewCouponCenterCall(ctx)
	resp, err := NewCouponCenterCall.SendCouponById(2124430235, 1929, "2124430235_1", "", "mqllbvofcf")
	if err != nil {
		logc.Warnf(ctx, "send coupon prize faild :", err)
	}
	respByte, _ := json.Marshal(resp)
	t.Log(string(respByte))
}
func TestCheckSendCouponProgress(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	NewCouponCenterCall := service_client.NewCouponCenterCall(ctx)
	resp, err := NewCouponCenterCall.CheckSendCouponProgress("mqllbvofcf", "2124436967_3111904753")
	if err != nil {
		logc.Warnf(ctx, "send coupon prize faild :", err)
	}
	respByte, _ := json.Marshal(resp)
	t.Log(string(respByte))
}

func TestRegisterTaskReceive(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	msg := risk_warning.RegisterTaskReceive(ctx, svcCtx, &xxljob.TaskRequest{})
	// 检查返回的结果并处理潜在的错误
	if msg == "" {
		t.Errorf("预期返回信息，但得到的是空字符串")
	} else {
		t.Log("任务返回信息:", msg)
	}
}

func TestGetCouponInfo(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	resp, err := service.GetCouponInfo(svcCtx, ctx, 1854, "mqllbvofcf")
	if err != nil {
		logc.Warnf(ctx, "send coupon prize faild :", err)
	}
	respByte, _ := json.Marshal(resp)
	t.Log(string(respByte))
}

func TestQueryWelfareTaskCfgList(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	data, err := dao.NewWelfareMysqlDBUtil(svcCtx.WelfareDB).QueryWelfareTaskCfgList(ctx, "high_region", []int{consts.UserTaskTypeNewbieRegister, consts.UserTaskTypeNewbieGuide, consts.UserTaskTypeNewbieAdvanced}, 0)
	if err != nil {
		fmt.Println("err is:", err)
	}
	for _, v := range data {
		fmt.Println("data is:", v.Id, "   ", v.TaskRegionKey, "   ", v.TaskId)
	}
}

func TestGetUserBelongRegion(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	//先获取用户区域，再做其他处理
	region, err := service.GetUserBelongRegion(svcCtx, ctx, 2124437658, nil, 37, 37, nil)
	if err != nil {
		fmt.Println("err is:", err)
	}
	fmt.Println(region)
}

func TestTaskReceiveCouponWarning(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	msg := task_receive_coupon.TaskReceiveCouponWarning(ctx, svcCtx, &xxljob.TaskRequest{})
	// 检查返回的结果并处理潜在的错误
	if msg == "" {
		t.Errorf("预期返回信息，但得到的是空字符串")
	} else {
		t.Log("任务返回信息:", msg)
	}
}

func TestTaskReceiveCouponDeal(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	msg := task_receive_coupon.TaskReceiveCouponWarningDo(ctx, svcCtx, &xxljob.TaskRequest{
		ExecutorParams: "2025-07-01 05:32:16",
	})
	// 检查返回的结果并处理潜在的错误
	if msg == "" {
		t.Errorf("预期返回信息，但得到的是空字符串")
	} else {
		t.Log("任务返回信息:", msg)
	}
}

func TestUserRegisterDeal(t *testing.T) {
	// 初始化服务上下文
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	str := `{"uid": 1234,"timest": "2025-07-04 10:30:45","ip_reg": "*************","ref_uid": 987654,"reg_client": "mobile_app","sub_website_id": 101,"register_country_id": 37,"residence_country_id": 25}`
	kafka.UserRegisterDeal(ctx, svcCtx, str)
}

func TestVal(t *testing.T) {
	svcCtx, err := initTestContext()
	if err != nil {
		t.Fatalf("初始化上下文失败: %v", err)
	}
	// 确保在测试结束后关闭上下文
	defer closeTestContext(svcCtx)
	ctx := context.Background()
	tracer := otel.Tracer("your-instrumentation-name")
	// 启动一个 span 并将其注入上下文
	ctx, span := tracer.Start(ctx, "operation-name")
	defer span.End()
	req := &rebate.GetRebateSystemUserDetailRequest{
		UserId: 2124443913,
	}
	resp, err := rebate.NewClient().GetRebateSystemUserDetail(ctx, req)
	if err != nil {
		fmt.Println("err is:", err)
	}
	respByte, _ := json.Marshal(resp)
	t.Log(string(respByte))

}
