syntax = "v1"

//web新人任务初始化请求参数
type NewbieTaskInitRequest {
	ConstId string `json:"const_id"` //风控的设备指纹token
}

//web新人任务初始化返回值
type NewbieTaskInitResponse {
    RotationTime int64                 `json:"rotation_time"` //轮询时间
    RecState     string                `json:"rec_state"`     //重构状态 php,go
    newbieTask   []*NewbieTaskInitTask `json:"newbieTask"`    //任务信息
}

//新人任务信息
type NewbieTaskInitTask {
   ID            int64  `json:"id"`            //任务主键ID
   Type          int    `json:"type"`          //任务类型
   TaskNum       string `json:"task_num"`      //交易量
   Stage         int    `json:"stage"`         //任务阶段
   CurProgress   string `json:"curProgress"`   //进度
   Status        int    `json:"status"`        //状态 0未完成 1进行中 2已完成 3结算中 4已结算 5过期未完成
   PrizeID       int64  `json:"prize_id"`      //奖品ID
   statusUtcTime int64  `json:"statusUtcTime"` //status的时间
   Sort          int    `json:"sort"`          //排序
}

//测试接口请求参数
type GetHelloReq {
	Name string `json:"name"`
}

//添加任务返回值
type GetHelloResp {
    TaskID int64 `json:"task_id"`
    RedisVal string `json:"redis_val"`
}

//通用无参数请求结构体
type UniversalNoParamReq {}

//获取请求IP对应的国家ID
type GetNoLoginInfoResp {
    IsBlack int `json:"is_black"` //请求IP是否在黑名单中 1:是 0:否
}

//获取用户身份请求参数
type GetUserIdentityReq {
    ConstID         string  `json:"const_id"`
}

//web-获取用户身份返回值
type GetUserIdentityResp {
    IsMarketplace    int   `json:"is_marketplace"`     // 是否做市商用户 1:是 (切换为：mm_white_list_new表)
    IsEnterprise     int   `json:"is_enterprise"`      // 是否企业用户 1:是
    IsSub            int   `json:"is_sub"`             // 是否子账号 1:是
    AgencyType       int   `json:"agency_type"`        //  agency_type 代理商类型 0: 普通用户 1: 代理商 2: 代理商推荐注册用户 3:合伙人身份；4:合伙人邀请的用户
    //IsRiskNewbie     int   `json:"is_risk_newbie"`     // 新人任务是否命中封控 1:是
    Region           string `json:"region"`            // 用户区域
    IsBlack          int   `json:"is_black"`           // 是否是黑名单地区用户 1:是 （取config数据）
    UserIdentity     int   `json:"user_identity"`      // 用户新老客身份 1:新客 2:老客 3:旧福利中心新客 0:身份未知 （快照信息）
    NewUserEndTime   int64 `json:"new_user_end_time"`  // 新客结束时间戳 秒（快照信息）
    IsVisitNewbie    int   `json:"is_visit_newbie"`    // 是否访问过新客福利中心（访问新客接口）1是0否
    IsPopUp          int   `json:"is_pop_up"`          // 是否弹过弹窗 1是0否
}

//获取未登录态用户区域
type UnLoginUserRegionResp {
    UserRegion string `json:"userRegion"` // 未登录态用户区域
}

//积分兑换请求参数
type PointsExchangeReq {
    Id              int  `json:"id"`
    ConstID         string  `json:"const_id"`
}

// 积分兑换奖品列表
type PointsPrizeListResp {
	List []*PointsPrizeInfo  `json:"list"` // 奖品列表
}

// 奖品详情
type PointsPrizeInfo struct {
	Id  		     int `json:"id"` // 奖品id
	PrizeNameText    string `json:"prize_name_text"`    // 奖品多语言标题
	PrizeType        int    `json:"prize_type"`         // 类型 1卡券
	PrizeSubType     int    `json:"prize_sub_type"`     // 奖励类型
	PrizeValue       string `json:"prize_value"`        // 奖励类型的数量
	PrizeDescText    string `json:"prize_desc_text"`    // 奖品多语言描述
	PrizeUrl         string `json:"prize_url"`          // 商品描述地址
	ExchangePoints   int    `json:"exchange_points"`    // 积分
	Surplus          int    `json:"surplus"`            // 剩余数量
	Sort             int    `json:"sort"`               // 排序
}

//签到请求参数
type CheckInReq {
    ConstID         string  `json:"const_id"`
}

// 获取签到任务初始化信息返回值
type CheckInInitResp {
	List []*CheckInTaskInfo  `json:"list"` // 每日考勤和奖励信息的列表
}

// 表示每日的考勤和奖励信息
type CheckInTaskInfo {
	IsAttendance int `json:"is_attendance"`  // 是否出勤，0 表示未签到，非 0 表示签到
	PrizeType    int `json:"prize_type"`     // 奖励类型
	PrizeTypeNum int `json:"prize_type_num"` // 奖励类型的数量
	DayNum       int `json:"day_num"`      // 排序号
	IsToday      int `json:"is_today"`      // 是否是今天的签到期 1:是 0:不是
}

//签到入口查询返回值
type CheckInEntranceInfoResp {
    IsFirstVisit  int     `json:"is_first_visit"`   // //当日是否是首次访问福利中心 1:是 0:否 如果是首次访问并且没有签到，前端需要弹出签到弹窗
    IsAttendance  int     `json:"is_attendance"`   // 当天是否已签到 1:是 0:否
    Points        int     `json:"points"` // 积分
}

//分页查询领奖记录请求参数
type PrizeRecordReq {
    Status     string `json:"status"`       // 全部：all，兑换奖品：exchange，新人任务：newbie_task_new  限时任务 ：limited_time_tasks  每日任务：daily_task
    Day        int    `json:"day"`          // 查询时间范围（天），可选值：0（全部）、7（近7天）等
    PerPage    int    `json:"per_page"`     // 每页显示的记录数量，默认值为10
    Page       int    `json:"page"`         // 当前页码，与from_data_id参数二选一，优先使用from_data_id
    Sort       string `json:"sort"`         // 排序方式，可选值：desc（倒序）、asc（顺序），默认按时间倒序排列
}

//分页查询领奖记录返回值
type PrizeRecordResp {
   	CurrentPage int            `json:"current_page"` // 当前页码（仅在使用page参数分页时存在）
   	From        int64          `json:"from"`         // 当前页第一个数据的序号（从1开始）
   	LastPage    int            `json:"last_page"`    // 最后一页的页码
   	PerPage     int            `json:"per_page"`     // 每页显示的数据数量
   	To          int64          `json:"to"`           // 当前页最后一个数据的序号
   	Total       int64          `json:"total"`        // 数据总数量
   	List        []*PrizeRecord `json:"list"`         // 奖励数据列表
}

//奖励信息
type PrizeRecord {
    ID           int64  `json:"id"`           // 数据唯一标识ID
	ReceiveTime  int64  `json:"receive_time"` // 奖励领取时间戳（秒级）
	Type         int    `json:"type"`         // 奖励类型：1-点卡 2-VIP1 3-合约体验金 4-startup券 5-量化体验金 6-VIP+1 7-理财体验金 8-合约体验券 9-高息理财
	TypeNum      int    `json:"type_num"`     // 奖励类型对应的数量
	PrizeName    string `json:"prize_name"`   // 奖品名称 当前情况只有新福利中心里的积分兑换有名称，其余情况按照类型和数量显示
	PrizeSource  string `json:"prize_source"` // 来源 兑换奖品：exchange，新人任务：newbie_task_new  限时任务 ：limited_time_tasks  每日任务：daily task
}

//分页查询积分记录请求参数
type PointsRecordReq {
    SourceType int    `json:"source_type"`  // 类型 全部：0，1:挑战任务 2:福利任务 3:邀请任务 4:新人任务(包含新客入门，进阶任务) 5:签到任务 6:tg签到任务 7:限时任务(包含老客限时任务) 8:游戏中心 9:游戏中心-邀请 10:新客注册任务  11:每日任务 12:积分过期 13:积分升级  99 积分兑换
    Day        int    `json:"day"`          // 查询时间范围（天），可选值：0（全部）、7（近7天）等
    PerPage    int    `json:"per_page"`     // 每页显示的记录数量，默认值为10
    Page       int    `json:"page"`         // 当前页码，与from_data_id参数二选一，优先使用from_data_id
    Sort       string `json:"sort"`         // 排序方式，可选值：desc（倒序）、asc（顺序），默认按时间倒序排列
}

//分页查询领奖记录返回值
type PointsRecordResp {
   	CurrentPage int            `json:"current_page"` // 当前页码（仅在使用page参数分页时存在）
   	From        int64          `json:"from"`         // 当前页第一个数据的序号（从1开始）
   	LastPage    int            `json:"last_page"`    // 最后一页的页码
   	PerPage     int            `json:"per_page"`     // 每页显示的数据数量
   	To          int64          `json:"to"`           // 当前页最后一个数据的序号
   	Total       int64          `json:"total"`        // 数据总数量
   	List        []*PointRecord `json:"list"`         // 积分数据列表
}

//积分记录
type PointRecord {
    ID          int64    `json:"id"`           // 数据唯一标识ID
	Action      string `json:"action"`       // 积分变动动作：ncr-增加 decr-减少
	Points      int    `json:"points"`       // 积分数值
	SourceType  int    `json:"source_type"`  // 积分来源类型：0-全部
	ReceiveTime int64  `json:"receive_time"` // 积分获得时间戳（秒级）
}

//分页查询任务记录请求参数
type TaskRecordReq {
    Status int    `json:"status"`       // 任务状态  0 :全部  1:进行中 2:未发奖 3:已发奖 4:已过期
    Day        int    `json:"day"`          // 查询时间范围（天），可选值：0（全部）、7（近7天）等
    PerPage    int    `json:"per_page"`     // 每页显示的记录数量，默认值为10
    Page       int    `json:"page"`         // 当前页码，与from_data_id参数二选一，优先使用from_data_id
    Sort       string `json:"sort"`         // 排序方式，可选值：desc（倒序）、asc（顺序），默认按时间倒序排列
}

//分页查询领奖记录返回值
type TaskRecordResp {
   	CurrentPage int            `json:"current_page"` // 当前页码（仅在使用page参数分页时存在）
   	From        int64          `json:"from"`         // 当前页第一个数据的序号（从1开始）
   	LastPage    int            `json:"last_page"`    // 最后一页的页码
   	PerPage     int            `json:"per_page"`     // 每页显示的数据数量
   	To          int64          `json:"to"`           // 当前页最后一个数据的序号
   	Total       int64          `json:"total"`        // 数据总数量
   	List        []*TaskRecord `json:"list"`         // 积分数据列表
}

//积分记录
type TaskRecord {
    ID               int64  `json:"id"`           // 数据唯一标识ID
    TaskID           int64  `json:"task_id"`      // 关联的任务ID
    WelfareTaskType  int    `json:"welfare_task_type"`      // 福利中心任务类型：类型 1挑战任务 2福利任务 3邀请任务 4新人任务 10注册 11入门 12进阶
    TaskCenterType   int    `json:"task_center_type"`      // 任务中心任务类型：1kyc2认证 2现货 3合约 4邀请 5量化 6余币宝 7startup 8首次入金
    TaskName         string `json:"task_name"`    // 任务名称
    Status           int    `json:"status"`       // 任务状态  0 :全部  1:进行中 2:未发奖 3:已发奖 4:已过期
    Stage            int    `json:"stage"`       // 老福利中心的任务阶段
    ReceiveTime      int64  `json:"receive_time"` // 任务领取时间戳（秒级）
}

type DailyTaskListPrizeInfo struct {
    TaskNum   int64 `json:"task_num"`
    PrizeType int64 `json:"prize_type"`
    PrizeNum  int64 `json:"prize_num"`
    Status    int64   `json:"status"`
}

type DailyTaskListFormatResp struct {
    List []*DailyTaskListFormatItem `json:"list"`
}

type DailyTaskListFormatItem struct {
    ID            int64         `json:"id"`
    TaskType      int64         `json:"task_type"`
    Title         string        `json:"title"`
    TitleHover    string        `json:"title_hover"`
    Desc          string        `json:"desc"`
    CurProcess    string        `json:"cur_process"`
    ActionType    string        `json:"action_type"`
    RemainingTime int64         `json:"remaining_time"`
    IsShowRemainingTime int64         `json:"is_show_remaining_time"`
    StartTime     int64         `json:"start_time"`
    Status        int64         `json:"status"`
    HiddenTime    int64         `json:"hidden_time"`
    PrizeInfo     []DailyTaskListPrizeInfo `json:"prize_info"`
}

//获取入门任务请求参数
type GetBeginnerTasksReq {
    ConstID string `json:"const_id"`
}

//获取入门任务返回值
type GetBeginnerTasksResp {
    IsRiskNewbie      int   `json:"is_risk_newbie"`     // 新人任务是否命中封控 1:是
    BeginnerTasks     []*BeginnerTask `json:"beginner_tasks"`      // 入门任务列表信息
    DescAllReward     string          `json:"desc_all_reward"`     // 根据不同地区描述中的奖励值
    AllBeginnerReward int64             `json:"all_beginner_reward"` // 入门任务奖励总和
    AllBeginnerCurrency string        `json:"all_beginner_currency"` // 卡劵币种 也是单位 USDT,BTC(默认USDT)
    NewbieEndTime     int64           `json:"newbie_end_time"`     // 新客期到期时间戳(秒)
}

type BeginnerTask {
	WelfareTaskID      int64   `json:"welfare_task_id"`       // 福利中心任务ID
	WelfareTaskType    int     `json:"welfare_task_type"`     // 福利中心任务类型 1:注册 用于前端判断翻译
	TaskCenterType     int     `json:"task_center_type"`     // 任务中心任务类型：1kyc2认证 2现货 3合约 4邀请 5量化 6余币宝 7startup 8首次入金，9  定投理财，10 双币投资，11 理财宝，12 双卡买币（信用卡/借记卡），13 tg绑定，14 tg注册身份认证，15 tg-game，16 社媒，17 C2C，18 lanchpool  以前叫新币挖矿，19 游戏中心-邀请，20 逐仓杠杆，21 净入金，22 净合约，23 app下载任务，24 alpha任务25 HOLDer Airdrop任务 用于前端跳转链接使用
	TaskName           string  `json:"task_name"`             // 任务名称
	TaskDesc           string  `json:"task_desc"`             // 任务描述
	RewardType         int64   `json:"reward_type"`           // 奖励类型
	RewardNum          int64   `json:"reward_num"`            // 奖励数量
	ExtraRewardType    int64   `json:"extra_reward_type"`     // 额外奖励类型(选填)
	ExtraRewardNum     int64   `json:"extra_reward_num"`      // 额外奖励数量(选填)
	ButtonType         string  `json:"button_type"`           // 按钮类型 register:注册 download:下载 3.deposit 4.trade 5.to_complete
	Status             int     `json:"status"`                // 任务状态 0:未领取 1:已领取 2:未发奖 3:发奖中 4:已完成 5:已过期
	ExtraRewardEndTime int64   `json:"extra_reward_end_time"` // 额外奖励到期时间戳(秒)(选填)
	RewardCurrency     string  `json:"reward_currency"` // 卡劵币种 也是单位 USDT,BTC(默认USDT)
	TaskFinishTime     int64   `json:"task_finish_time"` //任务完成时间
}

//领取任务请求参数
type ReveiveTaskReq {
    TaskId          int64   `json:"task_id"`
    ConstID         string  `json:"const_id"`
    WelfareToken    string  `json:"welfare_token"`
}

//查询进阶任务返回值
type GetAdvancedTasksResp {
    AllRewardNum int64                `json:"all_reward_num"` // 所有奖励数值 最高奖励
    AllRewardCurrency string `json:"all_reward_currency"` // 卡劵币种 也是单位 USDT,BTC(默认USDT)
	GearTasks    []*AdvancedGearTask `json:"gear_tasks"`     // 档位任务列表信息
}

// 进阶任务档位信息
type AdvancedGearTask struct {
	IsFinishGear   int         `json:"is_finish_gear"`   // 是否完成档位 1:是
	WelfareTaskID  int64         `json:"welfare_task_id"`  // 福利中心任务ID
	TaskCenterID   int64         `json:"task_center_id"`   // 任务中心任务ID
	PrizeList      []*PrizeInfo `json:"prize_list"`       // 任务奖励信息
	GearAllPrizeNum int         `json:"gear_all_prize_num"` //当前档位的奖励之和
	TaskDepositNum int64         `json:"task_deposit_num"` // 任务入金数量
	UserDepositNum float64     `json:"user_deposit_num"` // 用户当前入金数量
	TaskTradingNum int64         `json:"task_trading_num"` // 任务交易数量
	UserTradingNum float64     `json:"user_trading_num"` // 用户当前交易数量
}

// 任务奖励信息
type PrizeInfo struct {
	PrizeType int64 `json:"prize_type"` // 奖励类型
	PrizeNum  string `json:"prize_num"`  // 奖励数量
	CardType  int `json:"card_type"`  // 1:固定等级 2:提升等级 prize_type=20:VIP体验卡
	ValidDays int `json:"valid_days"`  // 天数(vip) prize_type=20:VIP体验卡
	PrizeCurrency string `json:"prize_currency"` // 卡劵币种 也是单位 USDT,BTC(默认USDT)
}

//领取奖励请求参数
type ReveivePrizeReq {
    TaskId          int64   `json:"task_id"`
    ConstID         string  `json:"const_id"`
}

//新客奖励信息
type NewbiePrizeGuideResp {
   PrizeNum string `json:"prize_num"`
   NewbieDays int `json:"newbie_days"` //新客天数
}
