syntax = "v1"
import "./types.api"

@server(
	prefix: /api/web/v1/welfare-center
	group: outer/web
	middleware: AuthAllowMiddleware
    timeout : 30s
)

service gateio_service_welfare_go-api {

    @doc "测试接口"
    @handler GetHello
    get /getHello (GetHelloReq) returns (GetHelloResp)

    @doc "获取请求IP的国家ID"
    @handler GetNoLoginInfo
    get /getNoLoginInfo (UniversalNoParamReq) returns (GetNoLoginInfoResp)

    @doc "分页查询领奖记录"
    @handler PrizeRecord
    get /prizeRecord (PrizeRecordReq) returns (PrizeRecordResp)

    @doc "分页查询积分记录"
    @handler PointsRecord
    get /pointsRecord (PointsRecordReq) returns (PointsRecordResp)

    @doc "分页查询任务记录"
    @handler TaskRecord
    get /taskRecord (TaskRecordReq) returns (TaskRecordResp)

    @doc "积分兑换奖品"
    @handler PointsExchange
    post /pointsExchange (PointsExchangeReq) returns (UniversalNoParamReq)

    @doc "积分兑换奖品列表"
    @handler PointsPrizeList
    get /pointsPrizeList (UniversalNoParamReq) returns (PointsPrizeListResp)

    @doc "上报弹窗"
    @handler UploadPopUp
    post /uploadPopUp (UniversalNoParamReq) returns (UniversalNoParamReq)

    @doc "签到"
    @handler CheckIn
    post /checkIn (CheckInReq) returns (UniversalNoParamReq)

    @doc "获取签到任务初始化信息"
    @handler CheckInInit
    get /checkInInit (UniversalNoParamReq) returns (CheckInInitResp)

    @doc "info信息"
    @handler CheckInEntranceInfo
    get /info (UniversalNoParamReq) returns (CheckInEntranceInfoResp)

    @doc "获取用户身份"
    @handler GetUserIdentity
    get /getUserIdentity (UniversalNoParamReq) returns (GetUserIdentityResp)

    @doc "获取新客奖励信息"
    @handler NewbiePrizeGuide
    get /newbiePrizeGuide (UniversalNoParamReq) returns (NewbiePrizeGuideResp)

    @doc "查询新客入门任务信息"
    @handler GetBeginnerTasks
    get /getBeginnerTasks (GetBeginnerTasksReq) returns (GetBeginnerTasksResp)

    @doc "查询新客进阶任务信息"
    @handler GetAdvancedTasks
    get /getAdvancedTasks (GetBeginnerTasksReq) returns (GetAdvancedTasksResp)


    @doc "老客每日任务列表"
    @handler DailyTaskList
    get /dailyTaskList (UniversalNoParamReq) returns ([]DailyTaskListFormatResp)

    @doc "老客限时任务列表"
    @handler LimitTaskList
    get /limitTaskList (UniversalNoParamReq) returns ([]DailyTaskListFormatResp)

    @doc "领取任务"
    @handler ReceiveTask
    post /receiveTask (ReveiveTaskReq) returns (UniversalNoParamReq)

    @doc "领取奖励"
    @handler ReceivePrize
    post /receivePrize (ReveivePrizeReq) returns (UniversalNoParamReq)
}