syntax = "v1"
import "./types.api"

@server(
	prefix: /api/inner/v1/welfare-center
	group: inner/web
	middleware: ReplaceHtmlEntityMiddleware
	timeout : 10s
)

service gateio_service_welfare_go-api {
	// ---------- 枚举和包装接口----------

	@doc "测试接口"
	@handler GetInnerHello
	get /getInnerHello (GetInnerHelloReq) returns (GetInnerHelloResp)

	@doc "获取身份（人群）枚举"
	@handler GetIdentityEnum
	get /getIdentityEnum () returns (IdentityEnumResp)

	@doc "拉取卡券信息"
	@handler GetCouponInfo
	get /getCouponInfo (CouponInfoReq) returns (CouponInfoResp)

	@doc "拉取任务信息"
	@handler GetTaskInfo
	get /getTaskInfo (TaskInfoReq) returns (TaskInfoResp)

	@doc "拉取多语言信息"
	@handler GetLanguage
	get /getLanguage () returns (LauguageResp)

	// ----------新人任务----------

	@doc "获取地区配置"
	@handler GetNewCustomerStrategy
	get /getNewCustomerStrategy (NewCustomerStrategyReq) returns (NewCustomerStrategyResp)

	@doc "新增/修改地区配置"
	@handler UpsertNewCustomerStrategy
	post /upsertNewCustomerStrategy (UpsertNewCustomerStrategyReq) returns (UpsertNewCustomerStrategyResp)

	@doc "获取入门任务"
	@handler GetWelcomeTasks
	get /getWelcomeTasks (WelcomeTasksReq) returns (WelcomeTasksResp)

	@doc "新增/修改注册任务并提交审核"
	@handler UpsertRegistTask
	post /upsertRegistTask (UpsertRegistTaskReq) returns (UpsertRegistTaskResp)

	@doc "新增/修改其他任务并提交审核"
	@handler UpsertWelcomeTask
	post /upsertWelcomeTask (UpsertWelcomeTaskReq) returns (UpsertWelcomeTaskResp)

	@doc "获取进阶任务"
	@handler GetAdvancedTasks
	get /getAdvancedTasks (AdvancedTasksReq) returns (AdvancedTasksResp)

	@doc "新增/修改进阶任务并提交审核"
	@handler UpsertAdvancedTask
	post /upsertAdvancedTask (UpsertAdvancedTaskReq) returns (UpsertAdvancedTaskResp)

	@doc "新人任务 拖动排序"
	@handler SortNewCustomerTask
	post /sortNewCustomerTask (SortNewCustomerTaskReq) returns (SortNewCustomerTaskResp)

	@doc "新人任务审核上线/重新上线"
	@handler ApproveNewCustomerTask
	post /approveNewCustomerTask (ApproveNewCustomerTaskReq) returns (ApproveNewCustomerTaskResp)

	@doc "新人任务直接下线"
	@handler OfflineNewCustomerTask
	post /offlineNewCustomerTask (OfflineNewCustomerTaskReq) returns (OfflineNewCustomerTaskResp)

	// ----------老客任务----------

	@doc "获取每日任务"
	@handler GetDailyTasks
	get /getDailyTasks (GetDailyTasksReq) returns (GetDailyTasksResp)

	@doc "新增/修改每日任务并提交审核"
	@handler UpsertDailyTask
	post /upsertDailyTask (UpsertDailyTaskReq) returns (UpsertDailyTaskResp)

	@doc "获取限时任务"
	@handler GetLimitTimeTasks
	get /getLimitTimeTasks (GetLimitTimeTasksReq) returns (GetLimitTimeTasksResp)

	@doc "新增/修改限时任务并提交审核"
	@handler UpsertLimitTimeTask
	post /upsertLimitTimeTask (UpsertLimitTimeTaskReq) returns (UpsertLimitTimeTaskResp)

	@doc "老客新人拖动排序"
	@handler SortOldCustomerTask
	post /sortOldCustomerTask (SortOldCustomerTaskReq) returns (SortOldCustomerTaskResp)

	@doc "老客审核上线/重新上线"
	@handler ApproveOldCustomerTask
	post /approveOldCustomerTask (ApproveOldCustomerTaskReq) returns (ApproveOldCustomerTaskResp)

	@doc "老客直接下线"
	@handler OfflineOldCustomerTask
	post /offlineOldCustomerTask (OfflineOldCustomerTaskReq) returns (OfflineOldCustomerTaskResp)

	// ----------积分商城----------
	@doc "获取商品"
	@handler GetPrizes
	get /getPrizes () returns (GetPrizesResp)

	@doc "新增/修改商品"
	@handler UpsertPrize
	post /upsertPrize (UpsertPrizeReq) returns (UpsertPrizeResp)

	@doc "商品拖动排序"
	@handler SortPrize
	post /sortPrize (SortPrizeReq) returns (SortPrizeResp)

	@doc "商品下架"
	@handler OfflinePrize
	post /offlinePrize (OfflinePrizeReq) returns (OfflinePrizeResp)

}