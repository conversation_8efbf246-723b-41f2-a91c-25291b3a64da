syntax = "v1"

// ---------- 枚举和包装接口----------

// 测试接口请求参数
type GetInnerHelloReq {
	Name string `json:"name"`
}

// 添加任务返回值
type GetInnerHelloResp {
    TaskID int64 `json:"task_id"`
    RedisVal string `json:"redis_val"`
}

// 获取身份枚举返回值
type IdentityEnumResp {
    IdentityEnum map[int]string `json:"identity_enum"`
}

// 拉取卡券信息请求值
type CouponInfoReq {
    CouponId int64 `form:"coupon_id"`
    Source string `form:"source"`
}

// 拉取卡券信息返回值
type CouponInfoResp {
    Map map[string]interface{} `json:"map"`
}

// 拉取任务信息请求值
type TaskInfoReq {
    TaskId int64 `form:"task_id"`
}

// 拉取任务信息返回值
type TaskInfoResp {
    TaskName string `json:"task_name"`
    TaskType int `json:"task_type"`
    EffectiveTime string `json:"effective_time"`
    Reward string `json:"reward"`
    ExtraReward string `json:"extra_reward"`
}

// 拉取多语言
type LauguageResp {
    LauguageEnum map[string]string `json:"lauguage_enum"`
}

// ----------新人任务----------

// 获取新客地区配置请求值
type NewCustomerStrategyReq {
    CustomerType int64 `form:"customer_type,optional"`
}

// 获取新客地区配置返回值
type NewCustomerStrategyResp {
    List []*RegionInfo `json:"list"`
}
type RegionInfo struct {
    Id             int64   `json:"id"`
    TaskRegionKey  string  `json:"task_region_key"`
    TaskRegion     string  `json:"task_region"`
    DayNum         int   `json:"day_num"`
}

// 新增/修改新客地区配置请求值
type UpsertNewCustomerStrategyReq {
    Id             int64   `json:"id"`
    TaskRegionKey  string  `json:"task_region_key"`
    DayNum         int  `json:"day_num"`
}

type UpsertNewCustomerStrategyResp {
}

// 获取入门任务请求值
type WelcomeTasksReq {
    TaskRegionKey   string   `form:"task_region_key"`
    EffectiveStatus  int  `form:"effective_status"`
}

// 获取入门任务返回值
type WelcomeTasksResp {
    List []*TaskDetail `json:"list"`
}
type TaskDetail struct {
    Id              int64   `json:"id"`
    Sort            int  `json:"sort"`
    TaskRegionKey   string  `json:"task_region_key"`
    TaskRegion      string   `json:"task_region"`
    TaskIdentityName   string  `json:"task_identity_name"`
    TaskId          int64   `json:"task_id"`
    TaskName        string   `json:"task_name"`
    TaskType        int   `json:"task_type"`
    EffectiveTime   string   `json:"effective_time"`
    RewardName      string   `json:"reward_name"`
    LimitTimeReward string   `json:"limit_time_reward"`
    Status          int   `json:"status"`
    CreatorName     string   `json:"creator_name"`
    ApprovalName    string   `json:"approval_name"`
    ExtraReward     string   `json:"extra_reward"`
    StairsReward    string   `json:"stairs_reward"`
    ExtraTaskInfo   interface{}  `json:"extra_task_info"`
    TaskIdentityId  int  `json:"task_identity_id"`
    Type            int  `json:"type"`
    TaskCenterType  int  `json:"task_center_type"`
    ButtonType     string  `json:"button_type"`
    OnlineStatus    int  `json:"online_status"`
    Version int   `json:"version"`
    EffectiveStatus   int   `json:"effective_status"`
}

// 新增/修改注册任务并提交审核请求值
type UpsertRegistTaskReq {
    Id             int64   `json:"id"`
    TaskRegionKey  string  `json:"task_region_key"`
    Source         string  `json:"source"`
    RewardId       int64  `json:"reward_id"`
    ButtonType     string  `json:"button_type"`
}
type UpsertRegistTaskResp {
}

// 新增/修改其他任务并提交审核请求值
type UpsertWelcomeTaskReq {
    Id             int64   `json:"id"`
    TaskRegionKey  string  `json:"task_region_key"`
    TaskId         int64  `json:"task_id"`
    LimitDays      *int  `json:"limit_days,optional"`
    ButtonType     string  `json:"button_type"`
}
type UpsertWelcomeTaskResp {
}

// 获取进阶任务请求值
type AdvancedTasksReq {
    TaskRegionKey string `form:"task_region_key"`
    EffectiveStatus  int  `form:"effective_status"`
}

// 获取进阶任务返回值
type AdvancedTasksResp {
    List []*AdvancedTaskDetail `json:"list"`
}
type AdvancedTaskDetail struct {
    Id              int64   `json:"id"`
    TaskRegionKey   string  `json:"task_region_key"`
    TaskRegion      string   `json:"task_region"`
    TaskList        []*TaskDetail  `json:"task_list"`
    Status          int   `json:"status"`
    CreatorName     string   `json:"creator_name"`
    ApprovalName    string   `json:"approval_name"`
    OnlineStatus    int  `json:"online_status"`
    Version int   `json:"version"`
    EffectiveStatus  int      `json:"effective_status"`
}

// 新增/修改进阶任务并提交审核
type UpsertAdvancedTaskReq {
    Id             int64   `json:"id"`
    TaskRegionKey  string  `json:"task_region_key"`
    TaskIds        []int64 `json:"task_ids"`
}
type UpsertAdvancedTaskResp {
}

// 新人任务拖动排序
type SortNewCustomerTaskReq {
    TaskSortMap    map[string]int   `json:"task_sort_map"`
}
type SortNewCustomerTaskResp {
}

// 新人任务审核上线/重新上线
type ApproveNewCustomerTaskReq {
    Id    int64   `json:"id"`
    Version int   `json:"version"`
}
type ApproveNewCustomerTaskResp {
}

// 新人任务直接下线
type OfflineNewCustomerTaskReq {
    Id                int64   `json:"id"`
}
type OfflineNewCustomerTaskResp {
}

// ----------老客任务----------

// 获取每日任务
type GetDailyTasksReq {
    TaskRegionKey    string   `form:"task_region_key"`
    TaskIdentityId   int   `form:"task_identity_id,optional"`
    EffectiveStatus  int  `form:"effective_status"`
}
type GetDailyTasksResp {
    List []*TaskDetail `json:"list"`
}

// 新增/修改每日任务并提交审核
type UpsertDailyTaskReq {
    Id             int64   `json:"id"`
    TaskRegionKey  string  `json:"task_region_key"`
    TaskIdentityId int     `json:"task_identity_id,optional"`
    TaskId         int64  `json:"task_id"`
    ButtonType     string  `json:"button_type"`
}
type UpsertDailyTaskResp {
    List []*TaskDetail `json:"list"`
}

// 获取限时任务
type GetLimitTimeTasksReq {
    TaskRegionKey    string   `form:"task_region_key"`
    TaskIdentityId   int   `form:"task_identity_id,optional"`
    EffectiveStatus  int  `form:"effective_status"`
}
type GetLimitTimeTasksResp {
    List []*TaskDetail `json:"list"`
}

// 新增/修改限时任务并提交审核
type UpsertLimitTimeTaskReq {
    Id                int64   `json:"id"`
    TaskRegionKey   string  `json:"task_region_key"`
    TaskIdentityId int     `json:"task_identity_id,optional"`
    TaskId           int64   `json:"task_id"`
    ButtonType       string  `json:"button_type"`
}
type UpsertLimitTimeTaskResp {
}

// 老客任务拖动排序
type SortOldCustomerTaskReq {
    TaskSortMap    map[string]int   `json:"task_sort_map"`
}
type SortOldCustomerTaskResp {
}

// 老客审核上线/重新上线
type ApproveOldCustomerTaskReq {
    Id          int64   `json:"id"`
    Version     int     `json:"version"`
}
type ApproveOldCustomerTaskResp {
}

// 老客任务直接下线
type OfflineOldCustomerTaskReq {
    Id                int64   `json:"id"`
}
type OfflineOldCustomerTaskResp {
}

// ----------积分商城----------

// 获取商品
type GetPrizesResp {
    List []*PrizeDetail `json:"list"`
}
type PrizeDetail struct {
    Id              int64   `json:"id"`
    PrizeId         int64  `json:"prize_id"`
    PrizeName       map[string]string  `json:"prize_name"`
    PrizeVaule      string   `json:"prize_vaule"`
    PrizeType       string  `json:"prize_type"`
    PrizeUrl        string   `json:"prize_url"`
    ExchangePoints  int   `json:"exchange_points"`
    PrizeMaxNum     int   `json:"prize_max_num"`
    PrizeSurplusNum int   `json:"prize_surplus_num"`
    ExchangeCycle   int   `json:"exchange_cycle"`
    ExchangeNum     int   `json:"exchange_num"`
    Sort            int  `json:"sort"`
    Source          string   `json:"source"`
}

// 新增/修改商品
type UpsertPrizeReq {
    Id                int64   `json:"id"`
    PrizeId           int64  `json:"prize_id"`
    Source            string   `json:"source"`
    PrizeName         map[string]string `json:"prize_name"`
    PrizeDescUrl      string  `json:"prize_desc_url"`
    ExchangePoints    int  `json:"exchange_points"`
    PrizeMaxNum       int  `json:"prize_max_num"`
    ExchangeCycle     int  `json:"exchange_cycle"`
    ExchangeNum       int  `json:"exchange_num"`
}
type UpsertPrizeResp {
}

// 商品拖动排序
type SortPrizeReq {
    PrizeSortMap    map[string]int   `json:"prize_sort_map"`
}
type SortPrizeResp {
}

// 商品下架
type OfflinePrizeReq {
    Id                int64   `json:"id"`
}
type OfflinePrizeResp {
}

// ----------任务额外字段----------

// 注册任务额外信息
type RegisterTaskInfo struct {
    Source             string  `json:"source"`            //用户source
    CouponId           int64  `json:"coupon_id"`            //卡券id
    ExtraRewards       []Reward  `json:"extra_rewards"`     //额外奖励
    
}
type Reward struct {
    RewardId      int64  `json:"reward_id"`       //奖励id
    RewardType    int    `json:"reward_type"`     //奖励类型 1.卡券 2.积分
    RewardSubType int    `json:"reward_sub_type"` //奖励子类型 如券类型
    RewardName    string `json:"reward_name"`     //奖励名称
    RewardValue   string `json:"reward_value"`    //奖励价值
    RewardSource  string `json:"reward_source"`   //奖励source
}
type CouponInfo struct {
    ID                 int                `json:"id"`
    CouponName         string             `json:"coupon_name"`
    CouponType         string             `json:"coupon_type"`
    Currency           string             `json:"currency"`
    Amount             string             `json:"amount"`
    AmountType         int64              `json:"amount_type"`
    Status             int                `json:"status"`
    ExpireType         int                `json:"expire_type"`
    ExpireInfo         string             `json:"expire_info"`
    CouponSource       string             `json:"coupon_source"`
    CreatedAt          string             `json:"created_at"`
}

// 其他入门任务额外信息
type WelcomeTaskInfo struct {
    LimitDays          int     `json:"limit_days"`        //限时天数
    LimitRewards       []Reward  `json:"limit_rewards"`   //限时奖励
}

// 进阶任务额外信息
type AdvancedTaskInfo struct {
    SubTasks  []AdvancedSubTaskInfo `json:"sub_tasks"`   //子任务列表
}
type AdvancedSubTaskInfo struct {
    TaskId             int64       `json:"task_id"`        //任务id
    TaskName           string      `json:"task_name"`      //任务名称
    TaskType           int       `json:"task_type"`      //任务类型
    EffectiveTime      string      `json:"effective_time"` //生效时间
    TaskRules          []TaskRule  `json:"task_rules"`     //规则
    Rewards            []Reward    `json:"rewards"`        //奖励
    ExtraRewards      []Reward    `json:"extra_rewards"`  //额外奖励
}
type TaskRule struct {
    RuleDesc       string       `json:"rule_desc"`        //规则描述
    Goal           int        `json:"goal"`             //规则目标
}

// 每日/限时任务额外信息
type DailyTaskInfo struct {
    StairsInfos       []StairsInfo `json:"stairs_infos"`
}
type StairsInfo struct {
    RuleDesc       string       `json:"rule_desc"`        //阶梯描述
    Goal           int        `json:"goal"`             //阶梯目标
    Rewards        []Reward    `json:"rewards"`           //奖励
}