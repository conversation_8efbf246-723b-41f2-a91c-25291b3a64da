{"swagger": "2.0", "info": {"title": "", "version": ""}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/inner/v1/welfare-center/approveNewCustomerTask": {"post": {"summary": "新人任务审核上线/重新上线", "operationId": "ApproveNewCustomerTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ApproveNewCustomerTaskResp"}}}, "parameters": [{"name": "body", "description": " 新人任务审核上线/重新上线", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ApproveNewCustomerTaskReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/approveOldCustomerTask": {"post": {"summary": "老客审核上线/重新上线", "operationId": "ApproveOldCustomerTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ApproveOldCustomerTaskResp"}}}, "parameters": [{"name": "body", "description": " 老客审核上线/重新上线", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ApproveOldCustomerTaskReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getAdvancedTasks": {"get": {"summary": "获取进阶任务", "operationId": "GetAdvancedTasks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/AdvancedTasksResp"}}}, "parameters": [{"name": "task_region_key", "in": "query", "required": true, "type": "string"}, {"name": "status", "in": "query", "required": true, "type": "integer", "format": "int32"}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getCouponInfo": {"get": {"summary": "拉取卡券信息", "operationId": "GetCouponInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/CouponInfoResp"}}}, "parameters": [{"name": "coupon_id", "in": "query", "required": true, "type": "integer", "format": "int64"}, {"name": "source", "in": "query", "required": true, "type": "string"}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getDailyTasks": {"get": {"summary": "获取每日任务", "operationId": "GetDailyTasks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetDailyTasksResp"}}}, "parameters": [{"name": "task_region_key", "in": "query", "required": true, "type": "string"}, {"name": "task_identity_id", "in": "query", "required": true, "type": "integer", "format": "int32"}, {"name": "status", "in": "query", "required": true, "type": "integer", "format": "int32"}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getIdentityEnum": {"get": {"summary": "获取身份（人群）枚举", "operationId": "GetIdentityEnum", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/IdentityEnumResp"}}}, "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getInnerHello": {"get": {"summary": "测试接口", "operationId": "GetInnerHello", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetInnerHelloResp"}}}, "parameters": [{"name": "name", "in": "query", "required": true, "type": "string"}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getLanguage": {"get": {"summary": "拉取多语言信息", "operationId": "GetLanguage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/LauguageResp"}}}, "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getLimitTimeTasks": {"get": {"summary": "获取限时任务", "operationId": "GetLimitTimeTasks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetLimitTimeTasksResp"}}}, "parameters": [{"name": "task_region_key", "in": "query", "required": true, "type": "string"}, {"name": "task_identity_id", "in": "query", "required": true, "type": "integer", "format": "int32"}, {"name": "status", "in": "query", "required": true, "type": "integer", "format": "int32"}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getNewCustomerStrategy": {"get": {"summary": "获取地区配置", "operationId": "GetNewCustomerStrategy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/NewCustomerStrategyResp"}}}, "parameters": [{"name": "customer_type", "in": "query", "required": true, "type": "integer", "format": "int64"}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getPrizes": {"get": {"summary": "获取商品", "operationId": "GetPrizes", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/GetPrizesResp"}}}, "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getTaskInfo": {"get": {"summary": "拉取任务信息", "operationId": "GetTaskInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/TaskInfoResp"}}}, "parameters": [{"name": "task_id", "in": "query", "required": true, "type": "integer", "format": "int64"}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/getWelcomeTasks": {"get": {"summary": "获取入门任务", "operationId": "GetWelcomeTasks", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/WelcomeTasksResp"}}}, "parameters": [{"name": "task_region_key", "in": "query", "required": true, "type": "string"}, {"name": "status", "in": "query", "required": true, "type": "integer", "format": "int32"}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/offlineNewCustomerTask": {"post": {"summary": "新人任务直接下线", "operationId": "OfflineNewCustomerTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/OfflineNewCustomerTaskResp"}}}, "parameters": [{"name": "body", "description": " 新人任务直接下线", "in": "body", "required": true, "schema": {"$ref": "#/definitions/OfflineNewCustomerTaskReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/offlineOldCustomerTask": {"post": {"summary": "老客直接下线", "operationId": "OfflineOldCustomerTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/OfflineOldCustomerTaskResp"}}}, "parameters": [{"name": "body", "description": " 老客任务直接下线", "in": "body", "required": true, "schema": {"$ref": "#/definitions/OfflineOldCustomerTaskReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/offlinePrize": {"post": {"summary": "商品下架", "operationId": "OfflinePrize", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/OfflinePrizeResp"}}}, "parameters": [{"name": "body", "description": " 商品下架", "in": "body", "required": true, "schema": {"$ref": "#/definitions/OfflinePrizeReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/sortNewCustomerTask": {"post": {"summary": "新人任务 拖动排序", "operationId": "SortNewCustomerTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SortNewCustomerTaskResp"}}}, "parameters": [{"name": "body", "description": " 新人任务拖动排序", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SortNewCustomerTaskReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/sortOldCustomerTask": {"post": {"summary": "老客新人拖动排序", "operationId": "SortOldCustomerTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SortOldCustomerTaskResp"}}}, "parameters": [{"name": "body", "description": " 老客任务拖动排序", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SortOldCustomerTaskReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/sortPrize": {"post": {"summary": "商品拖动排序", "operationId": "SortPrize", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/SortPrizeResp"}}}, "parameters": [{"name": "body", "description": " 商品拖动排序", "in": "body", "required": true, "schema": {"$ref": "#/definitions/SortPrizeReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/upsertAdvancedTask": {"post": {"summary": "新增/修改进阶任务并提交审核", "operationId": "UpsertAdvancedTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpsertAdvancedTaskResp"}}}, "parameters": [{"name": "body", "description": " 新增/修改进阶任务并提交审核", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpsertAdvancedTaskReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/upsertDailyTask": {"post": {"summary": "新增/修改每日任务并提交审核", "operationId": "UpsertDailyTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpsertDailyTaskResp"}}}, "parameters": [{"name": "body", "description": " 新增/修改每日任务并提交审核", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpsertDailyTaskReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/upsertLimitTimeTask": {"post": {"summary": "新增/修改限时任务并提交审核", "operationId": "UpsertLimitTimeTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpsertLimitTimeTaskResp"}}}, "parameters": [{"name": "body", "description": " 新增/修改限时任务并提交审核", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpsertLimitTimeTaskReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/upsertNewCustomerStrategy": {"post": {"summary": "新增/修改地区配置", "operationId": "UpsertNewCustomerStrategy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpsertNewCustomerStrategyResp"}}}, "parameters": [{"name": "body", "description": " 新增/修改新客地区配置请求值", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpsertNewCustomerStrategyReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/upsertPrize": {"post": {"summary": "新增/修改商品", "operationId": "UpsertPrize", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpsertPrizeResp"}}}, "parameters": [{"name": "body", "description": " 新增/修改商品", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpsertPrizeReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/upsertRegistTask": {"post": {"summary": "新增/修改注册任务并提交审核", "operationId": "UpsertRegistTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpsertRegistTaskResp"}}}, "parameters": [{"name": "body", "description": " 新增/修改注册任务并提交审核请求值", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpsertRegistTaskReq"}}], "tags": ["inner/web"]}}, "/api/inner/v1/welfare-center/upsertWelcomeTask": {"post": {"summary": "新增/修改其他任务并提交审核", "operationId": "UpsertWelcomeTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/UpsertWelcomeTaskResp"}}}, "parameters": [{"name": "body", "description": " 新增/修改其他任务并提交审核请求值", "in": "body", "required": true, "schema": {"$ref": "#/definitions/UpsertWelcomeTaskReq"}}], "tags": ["inner/web"]}}}, "definitions": {"AdvancedSubTaskInfo": {"type": "object", "properties": {"task_id": {"type": "integer", "format": "int64", "description": "任务id"}, "task_name": {"type": "string", "description": "任务名称"}, "task_type": {"type": "integer", "format": "int32", "description": "任务类型"}, "effective_time": {"type": "string", "description": "生效时间"}, "task_rules": {"type": "array", "items": {"$ref": "#/definitions/TaskRule"}, "description": "规则"}, "rewards": {"type": "array", "items": {"$ref": "#/definitions/Reward"}, "description": "奖励"}, "extra_rewards": {"type": "array", "items": {"$ref": "#/definitions/Reward"}, "description": "额外奖励"}}, "title": "AdvancedSubTaskInfo", "required": ["task_id", "task_name", "task_type", "effective_time", "task_rules", "rewards", "extra_rewards"]}, "AdvancedTaskDetail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "task_region_key": {"type": "string"}, "task_region": {"type": "string"}, "task_list": {"type": "array", "items": {"$ref": "#/definitions/TaskDetail"}}, "status": {"type": "integer", "format": "int32"}, "creator_name": {"type": "string"}, "approval_name": {"type": "string"}}, "title": "AdvancedTaskDetail", "required": ["id", "task_region_key", "task_region", "task_list", "status", "creator_name", "approval_name"]}, "AdvancedTaskInfo": {"type": "object", "properties": {"sub_tasks": {"type": "array", "items": {"$ref": "#/definitions/AdvancedSubTaskInfo"}, "description": "子任务列表"}}, "title": "AdvancedTaskInfo", "required": ["sub_tasks"]}, "AdvancedTasksReq": {"type": "object", "properties": {"task_region_key": {"type": "string"}, "status": {"type": "integer", "format": "int32"}}, "title": "AdvancedTasksReq", "required": ["task_region_key", "status"]}, "AdvancedTasksResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/AdvancedTaskDetail"}}}, "title": "AdvancedTasksResp", "required": ["list"]}, "ApproveNewCustomerTaskReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "version": {"type": "integer", "format": "int32"}}, "title": "ApproveNewCustomerTaskReq", "required": ["id", "version"]}, "ApproveNewCustomerTaskResp": {"type": "object", "title": "ApproveNewCustomerTaskResp"}, "ApproveOldCustomerTaskReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "version": {"type": "integer", "format": "int32"}}, "title": "ApproveOldCustomerTaskReq", "required": ["id", "version"]}, "ApproveOldCustomerTaskResp": {"type": "object", "title": "ApproveOldCustomerTaskResp"}, "CouponInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "coupon_name": {"type": "string"}, "coupon_type": {"type": "string"}, "currency": {"type": "string"}, "amount": {"type": "string"}, "amount_type": {"type": "integer", "format": "int64"}, "status": {"type": "integer", "format": "int32"}, "expire_type": {"type": "integer", "format": "int32"}, "expire_info": {"type": "string"}, "coupon_source": {"type": "string"}, "created_at": {"type": "string"}}, "title": "CouponInfo", "required": ["id", "coupon_name", "coupon_type", "currency", "amount", "amount_type", "status", "expire_type", "expire_info", "coupon_source", "created_at"]}, "CouponInfoReq": {"type": "object", "properties": {"coupon_id": {"type": "integer", "format": "int64"}, "source": {"type": "string"}}, "title": "CouponInfoReq", "required": ["coupon_id", "source"]}, "CouponInfoResp": {"type": "object", "properties": {"map": {"$ref": "#/definitions/mapstringinterface"}}, "title": "CouponInfoResp", "required": ["map"]}, "DailyTaskInfo": {"type": "object", "properties": {"stairs_infos": {"type": "array", "items": {"$ref": "#/definitions/StairsInfo"}}}, "title": "DailyTaskInfo", "required": ["stairs_infos"]}, "GetDailyTasksReq": {"type": "object", "properties": {"task_region_key": {"type": "string"}, "task_identity_id": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}}, "title": "GetDailyTasksReq", "required": ["task_region_key", "task_identity_id", "status"]}, "GetDailyTasksResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/TaskDetail"}}}, "title": "GetDailyTasksResp", "required": ["list"]}, "GetInnerHelloReq": {"type": "object", "properties": {"name": {"type": "string"}}, "title": "GetInnerHelloReq", "required": ["name"]}, "GetInnerHelloResp": {"type": "object", "properties": {"task_id": {"type": "integer", "format": "int64"}, "redis_val": {"type": "string"}}, "title": "GetInnerHelloResp", "required": ["task_id", "redis_val"]}, "GetLimitTimeTasksReq": {"type": "object", "properties": {"task_region_key": {"type": "string"}, "task_identity_id": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}}, "title": "GetLimitTimeTasksReq", "required": ["task_region_key", "task_identity_id", "status"]}, "GetLimitTimeTasksResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/TaskDetail"}}}, "title": "GetLimitTimeTasksResp", "required": ["list"]}, "GetPrizesResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/PrizeDetail"}}}, "title": "GetPrizesResp", "required": ["list"]}, "IdentityEnumResp": {"type": "object", "properties": {"identity_enum": {"$ref": "#/definitions/mapintstring"}}, "title": "IdentityEnumResp", "required": ["identity_enum"]}, "JWTPayload": {"type": "object", "properties": {"user_id": {"type": "integer", "format": "int32"}, "username": {"type": "string"}}, "title": "JWTPayload", "required": ["user_id", "username"]}, "LauguageResp": {"type": "object", "properties": {"lauguage_enum": {"type": "object"}}, "title": "LauguageResp", "required": ["lauguage_enum"]}, "NewCustomerStrategyReq": {"type": "object", "properties": {"customer_type": {"type": "integer", "format": "int64"}}, "title": "NewCustomerStrategyReq", "required": ["customer_type"]}, "NewCustomerStrategyResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/RegionInfo"}}}, "title": "NewCustomerStrategyResp", "required": ["list"]}, "OfflineNewCustomerTaskReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "OfflineNewCustomerTaskReq", "required": ["id"]}, "OfflineNewCustomerTaskResp": {"type": "object", "title": "OfflineNewCustomerTaskResp"}, "OfflineOldCustomerTaskReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "OfflineOldCustomerTaskReq", "required": ["id"]}, "OfflineOldCustomerTaskResp": {"type": "object", "title": "OfflineOldCustomerTaskResp"}, "OfflinePrizeReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "OfflinePrizeReq", "required": ["id"]}, "OfflinePrizeResp": {"type": "object", "title": "OfflinePrizeResp"}, "PrizeDetail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "prize_id": {"type": "integer", "format": "int64"}, "prize_name": {"type": "object"}, "prize_vaule": {"type": "string"}, "prize_type": {"type": "string"}, "prize_url": {"type": "string"}, "exchange_points": {"type": "integer", "format": "int32"}, "prize_max_num": {"type": "integer", "format": "int32"}, "prize_surplus_num": {"type": "integer", "format": "int32"}, "exchange_cycle": {"type": "integer", "format": "int32"}, "exchange_num": {"type": "integer", "format": "int32"}}, "title": "PrizeDetail", "required": ["id", "prize_id", "prize_name", "prize_vaule", "prize_type", "prize_url", "exchange_points", "prize_max_num", "prize_surplus_num", "exchange_cycle", "exchange_num"]}, "RegionInfo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "task_region_key": {"type": "string"}, "task_region": {"type": "string"}, "day_num": {"type": "integer", "format": "int32"}}, "title": "RegionInfo", "required": ["id", "task_region_key", "task_region", "day_num"]}, "RegisterTaskInfo": {"type": "object", "properties": {"source": {"type": "string", "description": "用户source"}, "coupon_id": {"type": "integer", "format": "int64", "description": "卡券id"}, "extra_rewards": {"type": "array", "items": {"$ref": "#/definitions/Reward"}, "description": "额外奖励"}}, "title": "RegisterTaskInfo", "required": ["source", "coupon_id", "extra_rewards"]}, "Reward": {"type": "object", "properties": {"reward_id": {"type": "integer", "format": "int64", "description": "奖励id"}, "reward_type": {"type": "integer", "format": "int32", "description": "奖励类型 1.卡券 2.积分"}, "reward_sub_type": {"type": "integer", "format": "int32", "description": "奖励子类型 如券类型"}, "reward_name": {"type": "string", "description": "奖励名称"}, "reward_value": {"type": "string", "description": "奖励价值"}, "reward_source": {"type": "string", "description": "奖励source"}}, "title": "<PERSON><PERSON>", "required": ["reward_id", "reward_type", "reward_sub_type", "reward_name", "reward_value", "reward_source"]}, "SortNewCustomerTaskReq": {"type": "object", "properties": {"task_sort_map": {"$ref": "#/definitions/mapint64int"}}, "title": "SortNewCustomerTaskReq", "required": ["task_sort_map"]}, "SortNewCustomerTaskResp": {"type": "object", "title": "SortNewCustomerTaskResp"}, "SortOldCustomerTaskReq": {"type": "object", "properties": {"task_sort_map": {"$ref": "#/definitions/mapint64int"}}, "title": "SortOldCustomerTaskReq", "required": ["task_sort_map"]}, "SortOldCustomerTaskResp": {"type": "object", "title": "SortOldCustomerTaskResp"}, "SortPrizeReq": {"type": "object", "properties": {"prize_sort_map": {"$ref": "#/definitions/mapint64int"}}, "title": "SortPrizeReq", "required": ["prize_sort_map"]}, "SortPrizeResp": {"type": "object", "title": "SortPrizeResp"}, "StairsInfo": {"type": "object", "properties": {"rule_desc": {"type": "string", "description": "阶梯描述"}, "goal": {"type": "integer", "format": "int32", "description": "阶梯目标"}, "rewards": {"type": "array", "items": {"$ref": "#/definitions/Reward"}, "description": "奖励"}}, "title": "StairsInfo", "required": ["rule_desc", "goal", "rewards"]}, "TaskDetail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "sort": {"type": "integer", "format": "int32"}, "task_region_key": {"type": "string"}, "task_region": {"type": "string"}, "task_identity_name": {"type": "string"}, "task_id": {"type": "integer", "format": "int64"}, "task_name": {"type": "string"}, "task_type": {"type": "integer", "format": "int32"}, "effective_time": {"type": "string"}, "reward_name": {"type": "string"}, "limit_time_reward": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "creator_name": {"type": "string"}, "approval_name": {"type": "string"}, "extra_reward": {"type": "string"}, "stairs_reward": {"type": "string"}}, "title": "TaskDetail", "required": ["id", "sort", "task_region_key", "task_region", "task_identity_name", "task_id", "task_name", "task_type", "effective_time", "reward_name", "limit_time_reward", "status", "creator_name", "approval_name", "extra_reward", "stairs_reward"]}, "TaskInfoReq": {"type": "object", "properties": {"task_id": {"type": "integer", "format": "int64"}}, "title": "TaskInfoReq", "required": ["task_id"]}, "TaskInfoResp": {"type": "object", "properties": {"task_name": {"type": "string"}, "task_type": {"type": "integer", "format": "int32"}, "effective_time": {"type": "string"}, "reward": {"type": "string"}, "extra_reward": {"type": "string"}}, "title": "TaskInfoResp", "required": ["task_name", "task_type", "effective_time", "reward", "extra_reward"]}, "TaskRule": {"type": "object", "properties": {"rule_desc": {"type": "string", "description": "规则描述"}, "goal": {"type": "integer", "format": "int32", "description": "规则目标"}}, "title": "TaskRule", "required": ["rule_desc", "goal"]}, "UpsertAdvancedTaskReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "task_region_key": {"type": "string"}, "task_id": {"type": "array", "items": {"type": "integer", "format": "int64"}}}, "title": "UpsertAdvancedTaskReq", "required": ["id", "task_region_key", "task_id"]}, "UpsertAdvancedTaskResp": {"type": "object", "title": "UpsertAdvancedTaskResp"}, "UpsertDailyTaskReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "task_region_key": {"type": "string"}, "task_identity_id": {"type": "integer", "format": "int32"}, "task_id": {"type": "integer", "format": "int64"}, "button_type": {"type": "string"}}, "title": "UpsertDailyTaskReq", "required": ["id", "task_region_key", "task_identity_id", "task_id", "button_type"]}, "UpsertDailyTaskResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/TaskDetail"}}}, "title": "UpsertDailyTaskResp", "required": ["list"]}, "UpsertLimitTimeTaskReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "task_region_key": {"type": "string"}, "task_identity_id": {"type": "integer", "format": "int32"}, "task_id": {"type": "integer", "format": "int64"}, "button_type": {"type": "string"}}, "title": "UpsertLimitTimeTaskReq", "required": ["id", "task_region_key", "task_identity_id", "task_id", "button_type"]}, "UpsertLimitTimeTaskResp": {"type": "object", "title": "UpsertLimitTimeTaskResp"}, "UpsertNewCustomerStrategyReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "task_region_key": {"type": "string"}, "day_num": {"type": "integer", "format": "int32"}}, "title": "UpsertNewCustomerStrategyReq", "required": ["id", "task_region_key", "day_num"]}, "UpsertNewCustomerStrategyResp": {"type": "object", "title": "UpsertNewCustomerStrategyResp"}, "UpsertPrizeReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "prize_id": {"type": "integer", "format": "int64"}, "source": {"type": "string"}, "prize_name": {"type": "object"}, "prize_desc_url": {"type": "string"}, "exchange_points": {"type": "integer", "format": "int32"}, "prize_max_num": {"type": "integer", "format": "int32"}, "exchange_cycle": {"type": "integer", "format": "int32"}, "exchange_num": {"type": "integer", "format": "int32"}}, "title": "UpsertPrizeReq", "required": ["id", "prize_id", "source", "prize_name", "prize_desc_url", "exchange_points", "prize_max_num", "exchange_cycle", "exchange_num"]}, "UpsertPrizeResp": {"type": "object", "title": "UpsertPrizeResp"}, "UpsertRegistTaskReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "task_region_key": {"type": "string"}, "source": {"type": "string"}, "reward_id": {"type": "integer", "format": "int64"}, "button_type": {"type": "string"}}, "title": "UpsertRegistTaskReq", "required": ["id", "task_region_key", "source", "reward_id", "button_type"]}, "UpsertRegistTaskResp": {"type": "object", "title": "UpsertRegistTaskResp"}, "UpsertWelcomeTaskReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "task_region_key": {"type": "string"}, "task_id": {"type": "integer", "format": "int64"}, "limit_days": {"type": "integer", "format": "int32"}, "button_type": {"type": "string"}}, "title": "UpsertWelcomeTaskReq", "required": ["id", "task_region_key", "task_id", "limit_days", "button_type"]}, "UpsertWelcomeTaskResp": {"type": "object", "title": "UpsertWelcomeTaskResp"}, "WelcomeTaskInfo": {"type": "object", "properties": {"limit_days": {"type": "integer", "format": "int32", "description": "限时天数"}, "limit_rewards": {"type": "array", "items": {"$ref": "#/definitions/Reward"}, "description": "限时奖励"}}, "title": "WelcomeTaskInfo", "required": ["limit_days", "limit_rewards"]}, "WelcomeTasksReq": {"type": "object", "properties": {"task_region_key": {"type": "string"}, "status": {"type": "integer", "format": "int32"}}, "title": "WelcomeTasksReq", "required": ["task_region_key", "status"]}, "WelcomeTasksResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/TaskDetail"}}}, "title": "WelcomeTasksResp", "required": ["list"]}}, "securityDefinitions": {"apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Enter JWT Bearer token **_only_**", "name": "Authorization", "in": "header"}}}