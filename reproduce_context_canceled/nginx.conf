events {
    worker_connections 1024;
}

http {
    upstream welfare_backend {
        server 127.0.0.1:8080;
    }
    
    # 测试1: 默认配置（会传播客户端断开）
    server {
        listen 9080;
        server_name localhost;
        
        location / {
            proxy_pass http://welfare_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 默认情况下 proxy_ignore_client_abort 是 off
            # 这意味着客户端断开时，nginx会关闭到后端的连接
        }
    }
    
    # 测试2: 开启 proxy_ignore_client_abort（不传播客户端断开）
    server {
        listen 9081;
        server_name localhost;
        
        location / {
            proxy_pass http://welfare_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 开启此选项，即使客户端断开，nginx也会等待后端响应
            proxy_ignore_client_abort on;
        }
    }
    
    # 测试3: 设置较短的代理超时
    server {
        listen 9082;
        server_name localhost;
        
        location / {
            proxy_pass http://welfare_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 设置较短的超时时间
            proxy_connect_timeout 1s;
            proxy_send_timeout 1s;
            proxy_read_timeout 1s;
        }
    }
}
