package main

import (
	"fmt"
	"net"
	"sync"
	"time"
)

// 测试内网服务的context canceled问题
func testInternalService(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()
	
	fmt.Printf("请求 %d: 开始连接内网服务...\n", requestID)
	
	// 连接到内网服务
	conn, err := net.Dial("tcp", "gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio:80")
	if err != nil {
		fmt.Printf("请求 %d: 连接失败: %v\n", requestID, err)
		return
	}
	
	fmt.Printf("请求 %d: 连接成功\n", requestID)
	
	// 构造HTTP请求
	request := "GET /api/inner/v1/welfare-center/getTaskInfo?task_id=1589 HTTP/1.1\r\n" +
		"Host: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio\r\n" +
		"User-Agent: TestClient/1.0\r\n" +
		"Connection: close\r\n" +
		"\r\n"
	
	// 发送请求
	_, err = conn.Write([]byte(request))
	if err != nil {
		fmt.Printf("请求 %d: 发送失败: %v\n", requestID, err)
		conn.Close()
		return
	}
	
	fmt.Printf("请求 %d: 请求已发送\n", requestID)
	
	// 等待一小段时间，确保服务器开始处理请求
	time.Sleep(200 * time.Millisecond)
	
	fmt.Printf("请求 %d: 主动断开连接\n", requestID)
	
	// 主动关闭连接（模拟客户端断开）
	conn.Close()
	
	fmt.Printf("请求 %d: 连接已关闭\n", requestID)
}

func main() {
	fmt.Println("=== 开始测试内网服务 context canceled 问题 ===")
	fmt.Println("目标服务: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio")
	fmt.Println("请求URL: /api/inner/v1/welfare-center/getTaskInfo?task_id=1589")
	fmt.Println("测试次数: 100次")
	fmt.Println()
	
	var wg sync.WaitGroup
	
	// 并发发送100个请求
	for i := 1; i <= 100; i++ {
		wg.Add(1)
		go testInternalService(i, &wg)
		
		// 每个请求之间间隔50ms，避免过于密集
		time.Sleep(50 * time.Millisecond)
	}
	
	// 等待所有请求完成
	wg.Wait()
	
	fmt.Println()
	fmt.Println("=== 所有请求已完成 ===")
	fmt.Println("请检查内网服务的日志，查看是否有 'context canceled' 错误")
}
