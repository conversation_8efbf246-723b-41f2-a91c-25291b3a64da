package main

import (
	"fmt"
	"net"
	"os"
	"time"
)

// 模拟客户端提前断开连接的情况
func main() {
	fmt.Println("=== 模拟客户端提前断开连接导致 context canceled ===")
	
	// 连接到福利服务
	conn, err := net.Dial("tcp", "localhost:8080")
	if err != nil {
		fmt.Printf("连接失败: %v\n", err)
		os.Exit(1)
	}
	
	fmt.Println("1. 连接到服务器成功")
	
	// 构造HTTP请求
	request := "GET /api/web/v1/welfare-center/tasks?task_ids=2155,2159,2237 HTTP/1.1\r\n" +
		"Host: localhost:8080\r\n" +
		"User-Agent: TestClient/1.0\r\n" +
		"Connection: close\r\n" +
		"\r\n"
	
	// 发送请求
	_, err = conn.Write([]byte(request))
	if err != nil {
		fmt.Printf("发送请求失败: %v\n", err)
		os.Exit(1)
	}
	
	fmt.Println("2. 发送HTTP请求成功")
	
	// 等待一小段时间，确保服务器开始处理请求
	time.Sleep(500 * time.Millisecond)
	
	fmt.Println("3. 等待500ms后主动关闭连接（模拟用户关闭浏览器）")
	
	// 主动关闭连接（模拟用户关闭浏览器或网络中断）
	conn.Close()
	
	fmt.Println("4. 连接已关闭，服务器应该会收到 context canceled 错误")
	
	// 等待一段时间，让服务器完成日志输出
	time.Sleep(3 * time.Second)
	
	fmt.Println("5. 测试完成")
}
