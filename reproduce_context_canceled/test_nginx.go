package main

import (
	"fmt"
	"net"
	"time"
)

// 测试通过nginx代理时的context canceled问题
func testNginxProxy(port string, testName string) {
	fmt.Printf("\n=== %s (端口:%s) ===\n", testName, port)
	
	// 连接到nginx代理
	conn, err := net.Dial("tcp", "localhost:"+port)
	if err != nil {
		fmt.Printf("连接失败: %v\n", err)
		return
	}
	
	fmt.Println("1. 连接到nginx代理成功")
	
	// 构造HTTP请求
	request := "GET /api/web/v1/welfare-center/tasks?task_ids=2155,2159,2237 HTTP/1.1\r\n" +
		"Host: localhost:" + port + "\r\n" +
		"User-Agent: TestClient/1.0\r\n" +
		"Connection: close\r\n" +
		"\r\n"
	
	// 发送请求
	_, err = conn.Write([]byte(request))
	if err != nil {
		fmt.Printf("发送请求失败: %v\n", err)
		return
	}
	
	fmt.Println("2. 发送HTTP请求成功")
	
	// 等待一小段时间，确保请求开始处理
	time.Sleep(500 * time.Millisecond)
	
	fmt.Println("3. 等待500ms后主动关闭连接")
	
	// 主动关闭连接
	conn.Close()
	
	fmt.Println("4. 连接已关闭")
	
	// 等待服务器处理完成
	time.Sleep(3 * time.Second)
}

func main() {
	fmt.Println("请确保已启动:")
	fmt.Println("1. 后端服务: go run server.go")
	fmt.Println("2. nginx: nginx -c $(pwd)/nginx.conf")
	fmt.Println()
	
	// 测试默认配置（应该会产生context canceled）
	testNginxProxy("9080", "测试默认nginx配置")
	
	// 测试开启proxy_ignore_client_abort（应该不会产生context canceled）
	testNginxProxy("9081", "测试开启proxy_ignore_client_abort")
	
	// 测试nginx超时（应该会产生超时错误）
	testNginxProxy("9082", "测试nginx代理超时")
	
	fmt.Println("\n=== 所有测试完成 ===")
}
