package main

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"
)

// 场景1: 极短超时（1ms）
func testVeryShortTimeout(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("极短超时 %d: 开始HTTP请求（1ms超时）...\n", requestID)

	// 创建HTTP客户端（设置极短的超时时间）
	client := &http.Client{
		Timeout: 1 * time.Millisecond, // 1ms超时，几乎不可能完成
	}

	// 创建请求
	req, err := http.NewRequest("GET",
		"http://gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio/api/inner/v1/welfare-center/getTaskInfo?task_id=1589", nil)
	if err != nil {
		fmt.Printf("极短超时 %d: 创建请求失败: %v\n", requestID, err)
		return
	}

	req.Header.Set("User-Agent", "TestClient-VeryShort/1.0")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("极短超时 %d: 请求失败（预期）: %v\n", requestID, err)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("极短超时 %d: 意外成功，状态码: %d\n", requestID, resp.StatusCode)
}

// 场景2: Context超时（使用context.WithTimeout）
func testContextTimeout(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("Context测试 %d: 开始HTTP请求（200ms context超时）...\n", requestID)

	// 创建带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
	defer cancel()

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second, // 客户端超时时间较长
	}

	// 创建带context的请求
	req, err := http.NewRequestWithContext(ctx, "GET",
		"http://gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio/api/inner/v1/welfare-center/getTaskInfo?task_id=1589", nil)
	if err != nil {
		fmt.Printf("Context测试 %d: 创建请求失败: %v\n", requestID, err)
		return
	}

	req.Header.Set("User-Agent", "TestClient-Context/1.0")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Context测试 %d: 请求失败（预期）: %v\n", requestID, err)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("Context测试 %d: 意外成功，状态码: %d\n", requestID, resp.StatusCode)
}

// 场景4: 立即断开TCP连接（最激进）
func testImmediateDisconnect(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("立即断开 %d: 开始连接...\n", requestID)

	// 连接到内网服务
	conn, err := net.Dial("tcp", "gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio:80")
	if err != nil {
		fmt.Printf("立即断开 %d: 连接失败: %v\n", requestID, err)
		return
	}

	// 构造HTTP请求
	request := "GET /api/inner/v1/welfare-center/getTaskInfo?task_id=1589 HTTP/1.1\r\n" +
		"Host: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio\r\n" +
		"User-Agent: TestClient-Immediate/1.0\r\n" +
		"Connection: keep-alive\r\n" +
		"\r\n"

	// 发送请求
	_, err = conn.Write([]byte(request))
	if err != nil {
		fmt.Printf("立即断开 %d: 发送失败: %v\n", requestID, err)
		conn.Close()
		return
	}

	fmt.Printf("立即断开 %d: 请求已发送，立即断开连接\n", requestID)

	// 立即断开连接（不等待）
	conn.Close()

	fmt.Printf("立即断开 %d: 连接已断开\n", requestID)
}

// 场景5: 发送部分请求后断开
func testPartialRequest(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("部分请求 %d: 开始连接...\n", requestID)

	// 连接到内网服务
	conn, err := net.Dial("tcp", "gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio:80")
	if err != nil {
		fmt.Printf("部分请求 %d: 连接失败: %v\n", requestID, err)
		return
	}

	// 只发送部分HTTP请求头
	partialRequest := "GET /api/inner/v1/welfare-center/getTaskInfo?task_id=1589 HTTP/1.1\r\n" +
		"Host: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio\r\n"

	// 发送部分请求
	_, err = conn.Write([]byte(partialRequest))
	if err != nil {
		fmt.Printf("部分请求 %d: 发送失败: %v\n", requestID, err)
		conn.Close()
		return
	}

	fmt.Printf("部分请求 %d: 发送部分请求，等待50ms后断开\n", requestID)

	// 等待很短时间后断开
	time.Sleep(50 * time.Millisecond)
	conn.Close()

	fmt.Printf("部分请求 %d: 连接已断开\n", requestID)
}

func main() {
	fmt.Println("=== 激进测试内网服务 Context Canceled 问题 ===")
	fmt.Println("目标服务: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio")
	fmt.Println("请求URL: /api/inner/v1/welfare-center/getTaskInfo?task_id=1589")
	fmt.Println()

	var wg sync.WaitGroup

	fmt.Println("场景1: 极短超时测试（30次，1ms超时）")
	// 测试极短超时
	for i := 1; i <= 30; i++ {
		wg.Add(1)
		go testVeryShortTimeout(i, &wg)
		time.Sleep(100 * time.Millisecond) // 间隔100ms
	}

	fmt.Println("\n场景2: Context超时测试（30次，1ms context超时）")
	// 测试Context超时
	for i := 1; i <= 30; i++ {
		wg.Add(1)
		go testContextTimeout(i, &wg)
		time.Sleep(100 * time.Millisecond) // 间隔100ms
	}

	fmt.Println("\n场景3: 立即断开TCP连接（40次）")
	// 测试立即断开TCP连接
	for i := 1; i <= 40; i++ {
		wg.Add(1)
		go testImmediateDisconnect(i, &wg)
		time.Sleep(50 * time.Millisecond) // 间隔50ms
	}

	fmt.Println("\n场景4: 发送部分请求后断开（40次）")
	// 测试发送部分请求后断开
	for i := 1; i <= 40; i++ {
		wg.Add(1)
		go testPartialRequest(i, &wg)
		time.Sleep(50 * time.Millisecond) // 间隔50ms
	}

	// 等待所有请求完成
	wg.Wait()

	fmt.Println("\n=== 所有测试完成 ===")
	fmt.Println("总共发送了140个激进请求，模拟4种context canceled场景")
	fmt.Println("请检查内网服务日志，查看是否有 'context canceled' 错误")
	fmt.Println()
	fmt.Println("预期结果：")
	fmt.Println("- 场景1: 1ms超时，应该产生超时错误")
	fmt.Println("- 场景2: 1ms context超时，应该产生 context deadline exceeded")
	fmt.Println("- 场景3: 立即断开TCP，应该产生 context canceled")
	fmt.Println("- 场景4: 部分请求断开，应该产生连接错误")
}
