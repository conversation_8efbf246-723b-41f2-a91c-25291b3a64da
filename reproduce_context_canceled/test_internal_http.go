package main

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"
)

// 使用HTTP客户端超时测试内网服务
func testInternalHTTP(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("请求 %d: 开始HTTP请求...\n", requestID)

	// 创建带超时的context（很短的超时时间）
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 200 * time.Millisecond, // 200ms超时
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET",
		"http://gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio/api/inner/v1/welfare-center/getTaskInfo?task_id=1589", nil)
	if err != nil {
		fmt.Printf("请求 %d: 创建请求失败: %v\n", requestID, err)
		return
	}

	// 设置请求头
	req.Header.Set("User-Agent", "TestClient/1.0")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求 %d: 请求失败: %v\n", requestID, err)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("请求 %d: 请求成功，状态码: %d\n", requestID, resp.StatusCode)
}

// 使用TCP连接直接断开的方式
func testInternalTCP(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("TCP请求 %d: 开始连接...\n", requestID)

	// 连接到内网服务
	conn, err := net.Dial("tcp", "gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio:80")
	if err != nil {
		fmt.Printf("TCP请求 %d: 连接失败: %v\n", requestID, err)
		return
	}

	// 构造HTTP请求
	request := "GET /api/inner/v1/welfare-center/getTaskInfo?task_id=1589 HTTP/1.1\r\n" +
		"Host: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio\r\n" +
		"User-Agent: TestClient/1.0\r\n" +
		"Connection: close\r\n" +
		"\r\n"

	// 发送请求
	_, err = conn.Write([]byte(request))
	if err != nil {
		fmt.Printf("TCP请求 %d: 发送失败: %v\n", requestID, err)
		conn.Close()
		return
	}

	fmt.Printf("TCP请求 %d: 请求已发送，等待200ms后断开\n", requestID)

	// 等待一小段时间后断开
	time.Sleep(200 * time.Millisecond)
	conn.Close()

	fmt.Printf("TCP请求 %d: 连接已断开\n", requestID)
}

func main() {
	fmt.Println("=== 测试内网服务 context canceled 问题 ===")
	fmt.Println("目标服务: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio")
	fmt.Println("请求URL: /api/inner/v1/welfare-center/getTaskInfo?task_id=1589")
	fmt.Println()

	var wg sync.WaitGroup

	fmt.Println("方式1: HTTP客户端超时（50次）")
	// 使用HTTP客户端超时
	for i := 1; i <= 50; i++ {
		wg.Add(1)
		go testInternalHTTP(i, &wg)
		time.Sleep(100 * time.Millisecond) // 间隔100ms
	}

	fmt.Println("\n方式2: TCP连接直接断开（50次）")
	// 使用TCP连接直接断开
	for i := 1; i <= 50; i++ {
		wg.Add(1)
		go testInternalTCP(i, &wg)
		time.Sleep(100 * time.Millisecond) // 间隔100ms
	}

	// 等待所有请求完成
	wg.Wait()

	fmt.Println("\n=== 所有测试完成 ===")
	fmt.Println("请检查内网服务日志，查看是否有 'context canceled' 错误")
	fmt.Println("预期结果：服务端应该会记录多个 context canceled 错误")
}
