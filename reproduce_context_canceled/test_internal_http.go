package main

import (
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"
)

// 发送完整的HTTP请求（不断开）
func testInternalHTTP(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("请求 %d: 开始HTTP请求...\n", requestID)

	// 创建HTTP客户端（设置较长的超时时间）
	client := &http.Client{
		Timeout: 30 * time.Second, // 30秒超时，足够完成请求
	}

	// 创建请求
	req, err := http.NewRequest("GET",
		"http://gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio/api/inner/v1/welfare-center/getTaskInfo?task_id=1589", nil)
	if err != nil {
		fmt.Printf("请求 %d: 创建请求失败: %v\n", requestID, err)
		return
	}

	// 设置请求头
	req.Header.Set("User-Agent", "TestClient/1.0")

	// 发送请求并等待完整响应
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求 %d: 请求失败: %v\n", requestID, err)
		return
	}
	defer resp.Body.Close()

	// 读取响应内容
	body := make([]byte, 1024)
	n, _ := resp.Body.Read(body)

	fmt.Printf("请求 %d: 请求成功，状态码: %d, 响应长度: %d\n", requestID, resp.StatusCode, n)
}

// 使用TCP连接直接断开的方式
func testInternalTCP(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("TCP请求 %d: 开始连接...\n", requestID)

	// 连接到内网服务
	conn, err := net.Dial("tcp", "gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio:80")
	if err != nil {
		fmt.Printf("TCP请求 %d: 连接失败: %v\n", requestID, err)
		return
	}

	// 构造HTTP请求
	request := "GET /api/inner/v1/welfare-center/getTaskInfo?task_id=1589 HTTP/1.1\r\n" +
		"Host: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio\r\n" +
		"User-Agent: TestClient/1.0\r\n" +
		"Connection: close\r\n" +
		"\r\n"

	// 发送请求
	_, err = conn.Write([]byte(request))
	if err != nil {
		fmt.Printf("TCP请求 %d: 发送失败: %v\n", requestID, err)
		conn.Close()
		return
	}

	fmt.Printf("TCP请求 %d: 请求已发送，等待200ms后断开\n", requestID)

	// 等待一小段时间后断开
	time.Sleep(200 * time.Millisecond)
	conn.Close()

	fmt.Printf("TCP请求 %d: 连接已断开\n", requestID)
}

func main() {
	fmt.Println("=== 测试内网服务正常请求 ===")
	fmt.Println("目标服务: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio")
	fmt.Println("请求URL: /api/inner/v1/welfare-center/getTaskInfo?task_id=1589")
	fmt.Println("发送10个完整的HTTP请求，不断开连接")
	fmt.Println()

	var wg sync.WaitGroup

	// 发送10个完整的HTTP请求
	for i := 1; i <= 10; i++ {
		wg.Add(1)
		go testInternalHTTP(i, &wg)
		time.Sleep(500 * time.Millisecond) // 间隔500ms
	}

	// 等待所有请求完成
	wg.Wait()

	fmt.Println("\n=== 所有请求完成 ===")
	fmt.Println("请检查内网服务日志，确认是否收到了请求")
}
