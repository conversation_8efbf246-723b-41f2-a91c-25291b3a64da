package main

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"
)

// 场景1: HTTP客户端超时（模拟客户端设置的超时时间过短）
func testHTTPTimeout(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("超时测试 %d: 开始HTTP请求（100ms超时）...\n", requestID)

	// 创建HTTP客户端（设置很短的超时时间）
	client := &http.Client{
		Timeout: 100 * time.Millisecond, // 100ms超时，很可能不够
	}

	// 创建请求
	req, err := http.NewRequest("GET",
		"http://gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio/api/inner/v1/welfare-center/getTaskInfo?task_id=1589", nil)
	if err != nil {
		fmt.Printf("超时测试 %d: 创建请求失败: %v\n", requestID, err)
		return
	}

	req.Header.Set("User-Agent", "TestClient-Timeout/1.0")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("超时测试 %d: 请求失败（预期）: %v\n", requestID, err)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("超时测试 %d: 意外成功，状态码: %d\n", requestID, resp.StatusCode)
}

// 场景2: Context超时（使用context.WithTimeout）
func testContextTimeout(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("Context测试 %d: 开始HTTP请求（200ms context超时）...\n", requestID)

	// 创建带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
	defer cancel()

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second, // 客户端超时时间较长
	}

	// 创建带context的请求
	req, err := http.NewRequestWithContext(ctx, "GET",
		"http://gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio/api/inner/v1/welfare-center/getTaskInfo?task_id=1589", nil)
	if err != nil {
		fmt.Printf("Context测试 %d: 创建请求失败: %v\n", requestID, err)
		return
	}

	req.Header.Set("User-Agent", "TestClient-Context/1.0")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("Context测试 %d: 请求失败（预期）: %v\n", requestID, err)
		return
	}
	defer resp.Body.Close()

	fmt.Printf("Context测试 %d: 意外成功，状态码: %d\n", requestID, resp.StatusCode)
}

// 使用TCP连接直接断开的方式
func testInternalTCP(requestID int, wg *sync.WaitGroup) {
	defer wg.Done()

	fmt.Printf("TCP请求 %d: 开始连接...\n", requestID)

	// 连接到内网服务
	conn, err := net.Dial("tcp", "gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio:80")
	if err != nil {
		fmt.Printf("TCP请求 %d: 连接失败: %v\n", requestID, err)
		return
	}

	// 构造HTTP请求
	request := "GET /api/inner/v1/welfare-center/getTaskInfo?task_id=1589 HTTP/1.1\r\n" +
		"Host: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio\r\n" +
		"User-Agent: TestClient/1.0\r\n" +
		"Connection: close\r\n" +
		"\r\n"

	// 发送请求
	_, err = conn.Write([]byte(request))
	if err != nil {
		fmt.Printf("TCP请求 %d: 发送失败: %v\n", requestID, err)
		conn.Close()
		return
	}

	fmt.Printf("TCP请求 %d: 请求已发送，等待200ms后断开\n", requestID)

	// 等待一小段时间后断开
	time.Sleep(200 * time.Millisecond)
	conn.Close()

	fmt.Printf("TCP请求 %d: 连接已断开\n", requestID)
}

func main() {
	fmt.Println("=== 测试内网服务 Context Canceled 问题 ===")
	fmt.Println("目标服务: gateio-service-welfare-go-inner.app-gateio.ntrnl-tke-dev.gateio")
	fmt.Println("请求URL: /api/inner/v1/welfare-center/getTaskInfo?task_id=1589")
	fmt.Println()

	var wg sync.WaitGroup

	fmt.Println("场景1: HTTP客户端超时测试（50次，100ms超时）")
	// 测试HTTP客户端超时
	for i := 1; i <= 50; i++ {
		wg.Add(1)
		go testHTTPTimeout(i, &wg)
		time.Sleep(200 * time.Millisecond) // 间隔200ms
	}

	fmt.Println("\n场景2: Context超时测试（50次，200ms context超时）")
	// 测试Context超时
	for i := 1; i <= 50; i++ {
		wg.Add(1)
		go testContextTimeout(i, &wg)
		time.Sleep(200 * time.Millisecond) // 间隔200ms
	}

	fmt.Println("\n场景3: TCP连接直接断开测试（50次）")
	// 测试TCP连接直接断开
	for i := 1; i <= 50; i++ {
		wg.Add(1)
		go testInternalTCP(i, &wg)
		time.Sleep(200 * time.Millisecond) // 间隔200ms
	}

	// 等待所有请求完成
	wg.Wait()

	fmt.Println("\n=== 所有测试完成 ===")
	fmt.Println("总共发送了150个请求，模拟3种context canceled场景")
	fmt.Println("请检查内网服务日志，查看是否有 'context canceled' 错误")
	fmt.Println()
	fmt.Println("预期结果：")
	fmt.Println("- 场景1: 客户端超时，可能产生 context deadline exceeded")
	fmt.Println("- 场景2: Context超时，可能产生 context deadline exceeded")
	fmt.Println("- 场景3: TCP断开，可能产生 context canceled")
}
