# Context Canceled 问题复现

这个项目用于复现Go HTTP服务中"context canceled"错误的几种场景。

## 问题原因

根据文章分析，Go官方HTTP库在处理连接时会创建`context.WithCancel`，当客户端提前断开连接时，HTTP库会调用`cancel()`，导致所有使用该context的子操作收到"context canceled"错误。

## 复现场景

### 1. 直接客户端断开连接

**启动服务器：**
```bash
go run server.go
```

**测试客户端断开：**
```bash
go run client_disconnect.go
```

**预期结果：**
- 服务器日志显示"context canceled"错误
- 任务中心服务调用被中断

### 2. HTTP客户端超时

**启动服务器：**
```bash
go run server.go
```

**测试客户端超时：**
```bash
go run nginx_test.go
```

**预期结果：**
- 客户端超时断开连接
- 服务器收到"context canceled"错误

### 3. Nginx代理场景

**启动服务器：**
```bash
go run server.go
```

**启动nginx（需要安装nginx）：**
```bash
nginx -c $(pwd)/nginx.conf
```

**测试nginx代理：**
```bash
go run test_nginx.go
```

**预期结果：**
- 端口9080（默认配置）：产生context canceled
- 端口9081（proxy_ignore_client_abort on）：不产生context canceled
- 端口9082（代理超时）：产生超时错误

## 关键代码说明

### 服务器端关键点

1. **Context传递**：
```go
req, err := http.NewRequestWithContext(r.Context(), "GET", url, nil)
```
使用请求的context创建HTTP客户端请求，这样当原始context被取消时，HTTP请求也会被取消。

2. **超时中间件**：
```go
ctx, cancel := context.WithTimeout(r.Context(), timeout)
defer cancel()
r = r.WithContext(ctx)
```
模拟go-zero的WithTimeout机制。

### 客户端断开模拟

```go
conn, err := net.Dial("tcp", "localhost:8080")
// 发送请求
conn.Write([]byte(request))
// 等待一段时间后主动关闭
time.Sleep(500 * time.Millisecond)
conn.Close()
```

### Nginx配置对比

- **默认配置**：`proxy_ignore_client_abort off`（默认）
  - 客户端断开时，nginx会关闭到后端的连接
  
- **忽略客户端断开**：`proxy_ignore_client_abort on`
  - 客户端断开时，nginx仍会等待后端响应

## 解决方案

1. **增加超时时间**：给接口设置更长的超时时间
2. **优化性能**：减少服务处理时间
3. **Nginx配置**：根据业务需求配置`proxy_ignore_client_abort`
4. **错误处理**：在代码中正确处理context canceled错误
5. **监控告警**：添加context canceled错误的监控

## 验证方法

运行测试后，观察服务器日志：

- 看到"Context已取消: context canceled"表示复现成功
- 看到"调用任务中心失败: Get \"...\": context canceled"表示问题传播成功

这与你们线上日志中的错误模式完全一致。
