package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"
)

// 模拟任务中心服务
func taskCenterHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("TaskCenter: 收到请求 %s", r.URL.String())

	// 模拟任务中心服务的处理时间（比较慢）
	time.Sleep(2 * time.Second)

	// 检查context状态
	if r.Context().Err() != nil {
		log.Printf("TaskCenter: Context已取消: %v", r.Context().Err())
		http.Error(w, "Context canceled", http.StatusRequestTimeout)
		return
	}

	// 返回模拟数据
	response := map[string]interface{}{
		"code": 0,
		"data": map[string]interface{}{
			"list": []map[string]interface{}{
				{"task_id": 2155, "name": "任务1"},
				{"task_id": 2159, "name": "任务2"},
				{"task_id": 2237, "name": "任务3"},
			},
		},
	}

	w.<PERSON>er().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
	log.Printf("TaskCenter: 请求处理完成")
}

// 模拟福利服务
func welfareHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("Welfare: 收到请求 %s", r.URL.String())

	// 模拟调用任务中心服务
	taskIds := r.URL.Query().Get("task_ids")
	if taskIds == "" {
		taskIds = "2155,2159,2237"
	}

	// 创建HTTP客户端请求任务中心
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 使用请求的context（这里是关键！）
	req, err := http.NewRequestWithContext(r.Context(), "GET",
		fmt.Sprintf("http://localhost:8081/api/inner/v1/task/batch-task-all?task_ids=%s", taskIds), nil)
	if err != nil {
		log.Printf("Welfare: 创建请求失败: %v", err)
		http.Error(w, "Internal error", http.StatusInternalServerError)
		return
	}

	log.Printf("Welfare: 开始调用任务中心服务...")
	resp, err := client.Do(req)
	if err != nil {
		// 这里就会出现 context canceled 错误
		log.Printf("Welfare: 调用任务中心失败: %v", err)
		http.Error(w, fmt.Sprintf("Task center error: %v", err), http.StatusInternalServerError)
		return
	}
	defer resp.Body.Close()

	log.Printf("Welfare: 任务中心调用成功")

	// 返回结果
	result := map[string]interface{}{
		"code":    0,
		"message": "success",
		"data":    fmt.Sprintf("获取到%s的任务信息", taskIds),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// 启动任务中心服务（端口8081）
func startTaskCenterServer() {
	mux := http.NewServeMux()
	mux.HandleFunc("/api/inner/v1/task/batch-task-all", taskCenterHandler)

	server := &http.Server{
		Addr:    ":8081",
		Handler: mux,
	}

	log.Println("任务中心服务启动在 :8081")
	if err := server.ListenAndServe(); err != nil {
		log.Printf("任务中心服务启动失败: %v", err)
	}
}

// 启动福利服务（端口8080）
func startWelfareServer() {
	mux := http.NewServeMux()
	mux.HandleFunc("/api/web/v1/welfare-center/tasks", welfareHandler)

	// 添加超时中间件（模拟go-zero的WithTimeout）
	timeoutMiddleware := func(timeout time.Duration) func(http.Handler) http.Handler {
		return func(next http.Handler) http.Handler {
			return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// 创建带超时的context
				ctx, cancel := context.WithTimeout(r.Context(), timeout)
				defer cancel()

				// 将新的context设置到request中
				r = r.WithContext(ctx)

				// 创建channel来监控处理完成
				done := make(chan struct{})
				var panicErr interface{}

				go func() {
					defer func() {
						if err := recover(); err != nil {
							panicErr = err
						}
						close(done)
					}()
					next.ServeHTTP(w, r)
				}()

				// 等待处理完成或超时
				select {
				case <-done:
					if panicErr != nil {
						log.Printf("Handler panic: %v", panicErr)
						http.Error(w, "Internal server error", http.StatusInternalServerError)
					}
				case <-ctx.Done():
					log.Printf("请求超时或被取消: %v", ctx.Err())
					cancel() // 确保取消
					if ctx.Err() == context.DeadlineExceeded {
						http.Error(w, "Request timeout", http.StatusRequestTimeout)
					} else {
						http.Error(w, "Request canceled", http.StatusRequestTimeout)
					}
				}
			})
		}
	}

	// 应用超时中间件（5秒超时，比任务中心的2秒处理时间长一些）
	handler := timeoutMiddleware(5 * time.Second)(mux)

	server := &http.Server{
		Addr:    ":8080",
		Handler: handler,
	}

	log.Println("福利服务启动在 :8080")
	if err := server.ListenAndServe(); err != nil {
		log.Printf("福利服务启动失败: %v", err)
	}
}

func main() {
	// 启动任务中心服务
	go startTaskCenterServer()

	// 等待任务中心服务启动
	time.Sleep(1 * time.Second)

	// 启动福利服务
	startWelfareServer()
}
