package main

import (
	"fmt"
	"io"
	"net/http"
	"time"
)

// 模拟通过nginx代理访问，然后客户端断开连接
func main() {
	fmt.Println("=== 模拟通过nginx代理访问后客户端断开 ===")
	
	// 创建HTTP客户端，设置较短的超时时间
	client := &http.Client{
		Timeout: 1 * time.Second, // 1秒超时，但服务器需要2秒处理
	}
	
	fmt.Println("1. 创建HTTP客户端，超时时间1秒")
	
	// 发送请求
	fmt.Println("2. 发送请求到福利服务...")
	resp, err := client.Get("http://localhost:8080/api/web/v1/welfare-center/tasks?task_ids=2155,2159,2237")
	
	if err != nil {
		fmt.Printf("3. 请求失败（预期）: %v\n", err)
		
		// 检查是否是context相关错误
		if err.Error() == "context deadline exceeded" {
			fmt.Println("   -> 这是客户端超时导致的")
		} else {
			fmt.Printf("   -> 错误类型: %T\n", err)
		}
	} else {
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		fmt.Printf("3. 请求成功: %s\n", string(body))
	}
	
	fmt.Println("4. 等待服务器完成处理...")
	time.Sleep(3 * time.Second)
	
	fmt.Println("5. 测试完成")
}
