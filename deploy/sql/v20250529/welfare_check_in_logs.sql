CREATE TABLE `welfare_check_in_logs`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT,
    `check_in_task_id`  bigint unsigned NOT NULL DEFAULT '0' COMMENT '签到任务ID',
    `uid`               bigint unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
    `day`               int unsigned NOT NULL DEFAULT '0' COMMENT 'Ymd格式',
    `is_cycle_last_day` int unsigned NOT NULL DEFAULT '0' COMMENT '是否是周期的最后一天 1:是',
    `prize_id`          int unsigned NOT NULL DEFAULT '0' COMMENT '奖品id',
    `prize_type`        int unsigned NOT NULL DEFAULT '0' COMMENT '奖品类型 1积分 2卡劵',
    `prize_type_num`    int unsigned NOT NULL DEFAULT '0' COMMENT '奖品数量',
    `status`            tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '状态 1:成功; 2:失败 3:发奖中',
    `created_at`        timestamp                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`        timestamp                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    KEY                 `idx_uid` (`uid`,`day`) USING BTREE,
    KEY                 `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='签到记录表';