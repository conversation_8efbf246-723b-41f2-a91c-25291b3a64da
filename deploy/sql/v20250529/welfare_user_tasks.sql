CREATE TABLE `welfare_user_tasks`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT,
    `uid`              bigint unsigned NOT NULL COMMENT 'uid',
    `type`             tinyint unsigned NOT NULL DEFAULT '0' COMMENT '类型 1新手任务 2福利任务 3邀请任务 4新人任务',
    `task_id`          bigint unsigned NOT NULL DEFAULT '0' COMMENT '任务id',
    `task_type`        tinyint unsigned NOT NULL DEFAULT '0' COMMENT '任务类型：1kyc2认证 2现货 3合约 4邀请 5量化 6余币宝 7startup 8首次入金',
    `task_progress`    decimal(20, 6) NOT NULL COMMENT '任务进度：U',
    `status`           tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态 0未完成 1进行中 2已完成 3结算中 4已结算 5过期',
    `points`           int unsigned NOT NULL DEFAULT '0' COMMENT '积分',
    `memo`             text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL COMMENT '备注',
    `created_at`       timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`       timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `stage`            tinyint unsigned NOT NULL DEFAULT '0' COMMENT '阶段默认0',
    `finish_task_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '完成任务时间戳 秒',
    `task_center_id`   bigint unsigned NOT NULL DEFAULT '0' COMMENT '任务中心任务id',
    `record_show`      tinyint unsigned NOT NULL DEFAULT '1' COMMENT '字段是否显示 1:显示 2:不显示',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                `idx_uid_tid` (`uid`,`task_id`) USING BTREE,
    KEY                `idx_uid_type` (`uid`,`type`,`task_type`) USING BTREE,
    KEY                `idx_status_type` (`status`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='福利用户任务表';