CREATE TABLE `welfare_user_register_info`
(
    `id`                      bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `uid`                     bigint unsigned NOT NULL DEFAULT '0' COMMENT '用户uid',
    `register_country_id`     int unsigned NOT NULL DEFAULT '0' COMMENT '注册国家',
    `residence_country_id`    int unsigned NOT NULL DEFAULT '0' COMMENT '注册IP国家',
    `created_at`              timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`              timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `region`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '地区',
    `is_visit_newbie`         tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否访问过新客福利中心（访问新客接口）1是0否',
    `is_pop_up`               tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否弹过弹窗 1是0否',
    `newbie_end_time`         bigint                                                        NOT NULL DEFAULT '0' COMMENT '新客期结束时间戳',
    `advanced_end_time`       bigint                                                        NOT NULL DEFAULT '0' COMMENT '进阶任务结算时间戳 用于n+3天发奖',
    `finish_advanced_receive` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否完成进阶任务发奖 1:是 2:否',
    `is_black`                tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否是黑敏感 1是 2否',
    PRIMARY KEY (`id`),
    KEY                       `idx_uid` (`uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户注册任务信息';