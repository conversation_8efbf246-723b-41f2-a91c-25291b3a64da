CREATE TABLE `welfare_points_records`
(
    `id`             int unsigned NOT NULL AUTO_INCREMENT,
    `uid`            bigint unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
    `action`         varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT '' COMMENT 'incr增加 decr减少',
    `points`         bigint unsigned NOT NULL DEFAULT '0' COMMENT '积分',
    `type`           tinyint unsigned NOT NULL DEFAULT '0' COMMENT '类型 1新手任务 2福利任务 3邀请任务',
    `task_id`        bigint unsigned NOT NULL DEFAULT '0' COMMENT '任务id',
    `task_type`      tinyint unsigned NOT NULL DEFAULT '0' COMMENT '任务类型：1kyc2认证 2现货 3合约 4邀请 5量化 6余币宝 7startup',
    `correlation_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '任务/兑换记录id',
    `memo`           text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL COMMENT '备注',
    `created_at`     timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`     timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_uid` (`uid`) USING BTREE,
    KEY              `idx_cid` (`correlation_id`) USING BTREE,
    KEY              `idx_type` (`type`,`task_type`,`task_id`) USING BTREE,
    KEY              `idx_created` (`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1979041007 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='福利积分记录';