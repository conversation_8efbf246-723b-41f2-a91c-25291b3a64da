DROP TABLE IF EXISTS `welfare_activity_user_task`;
CREATE TABLE `welfare_activity_user_task`
(
    `id`         bigint                                                       NOT NULL AUTO_INCREMENT,
    `uid`        bigint                                                       NOT NULL DEFAULT '0' COMMENT '用户 uid',
    `task_id`    bigint                                                       NOT NULL DEFAULT '0' COMMENT '任务表主键',
    `sys_id`     bigint                                                       NOT NULL DEFAULT '0' COMMENT '任务中心任务 id',
    `aid`        int                                                          NOT NULL DEFAULT '-1' COMMENT '活动标识 定向活动-1 ',
    `status`     tinyint                                                               DEFAULT '0' COMMENT '状态 0未完成 1进行中 2已完成 3结算中 4已结算 5过期',
    `points`     bigint                                                       NOT NULL DEFAULT '0' COMMENT '积分,任务中心拉取',
    `created_at` timestamp                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `source`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
    `ack`        tinyint                                                      NOT NULL DEFAULT '-1' COMMENT 'ack状态',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=119 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户任务状态记录表';