CREATE TABLE `welfare`.`welfare_task_cfgs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '数据ID',
  `task_region_key` varchar(50) NOT NULL DEFAULT '0' COMMENT '任务所在地区id welfare_region_cfgs.task_region_key',
  `task_region` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '任务所在地区标识',
  `task_identity_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '任务所在身份id 1.普通用户，2.VIP',
  `task_identity_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '任务所在身份',
  `task_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '任务ID',
  `task_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '福利中心任务类型，10 新客注册任务、11 入门任务',
  `task_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '任务系统任务类型转换为int 1 kyc2任务、2现货',
  `task_center_type` tinyint unsigned NOT NULL DEFAULT '0'  COMMENT '1常规任务、2循环任务、3多次任务、4用户可领取多次任务',
  `effective_time` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '生效时间',
  `extra_task_info` text COMMENT '任务额外信息',  -- 注册任务的source、限时配置、限时/额外/追加奖励信息
  `reward`  text  COMMENT '基础奖励list', -- 包括注册奖励，id type name,不需要存多语言，少存一点用到的就行',
  `button_type` varchar(16) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作按钮类型 1.register 2.download 3.deposit 4.trade 5.verify 6.complete',
  `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态：1=待审核, 2=已审核通过',
  `approved_snapshot` text COMMENT '审核通过的线上快照',
  `online_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '上线状态：1=上线, 2=下线',
  `creator_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `creator_name`  varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建人姓名',
  `approval_id` bigint unsigned NOT NULL DEFAULT '0'  COMMENT '审核人ID',
  `approval_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审核人姓名',
  `sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `pre_env` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否是预发环境 1:是',
  `version` int unsigned NOT NULL DEFAULT '0'  COMMENT '版本',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_welfare_task_id` (`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='福利中心任务表';

-- 增加索引
ALTER TABLE welfare_task_cfgs ADD INDEX idx_sort (sort);