CREATE TABLE `welfare_points_shops_limit_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `prize_id` int unsigned NOT NULL DEFAULT '0' COMMENT '奖品id',
  `type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '类型 1点卡 2vip1 3合约体验金 4startup券 5量化体验金 6vip+1',
  `type_num` int unsigned NOT NULL DEFAULT '0' COMMENT 'type数量',
  `num` int unsigned NOT NULL DEFAULT '0' COMMENT '奖品数量/份额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_prizeid` (`prize_id`,`type`,`type_num`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='福利积分商品额度变更记录';