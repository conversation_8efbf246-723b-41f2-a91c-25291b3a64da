CREATE TABLE `welfare`.`welfare_points_shop`
(
    `id`                      int unsigned NOT NULL AUTO_INCREMENT,
    `prize_id`                bigint unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
    `prize_name`              text NOT NULL COMMENT '商品（卡券）名称多语言',
    `prize_type`              tinyint unsigned NOT NULL DEFAULT '0' COMMENT '类型 1卡券',
    `prize_sub_type`          tinyint unsigned NOT NULL DEFAULT '0' COMMENT '类型 1点卡 2vip1 3合约体验金 4startup券 5量化体验金 6vip+1 7理财体验金 8合约体验券 9高息理财',
    `prize_value`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品价值',
    `prize_desc`              text NOT NULL COMMENT '商品描述多语言',
    `prize_url`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品描述地址',
    `prize_max_num`           int unsigned NOT NULL DEFAULT '0' COMMENT '奖品库存总额',
    `exchange_points`         bigint unsigned NOT NULL DEFAULT '0' COMMENT '积分',
    `exchange_cycle`          int unsigned NOT NULL DEFAULT '0' COMMENT '兑换周期',
    `exchange_num`            int unsigned NOT NULL DEFAULT '0' COMMENT '兑换次数',
    `source`                  varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT '' COMMENT '卡劵下发使用的source',
    `status`                  tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态：1=上架, 2=下架',
    `sort`                    int unsigned NOT NULL DEFAULT '0' COMMENT '排序 使用desc',
    `pre_env` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否是预发环境 1:是',
    `created_at`              timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`              timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    KEY                       `idx_prize_id` (`prize_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分商城表';
