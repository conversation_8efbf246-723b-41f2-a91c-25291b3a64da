CREATE TABLE `welfare`.`welfare_region_cfgs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `task_region_key` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '任务所在地区key',
  `task_region` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '任务所在地区',
  `day_num` int unsigned NOT NULL DEFAULT '0' COMMENT '周期配置 天数',
  `customer_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '客户类型 0新老客 1新客 2老客',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_task_region_key` (`task_region_key`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新客地区周期配置表';

INSERT INTO welfare.welfare_region_cfgs
(id, task_region, task_region_key, day_num, customer_type, created_at, updated_at)
VALUES(1, 'high_worth', '高净值', 15, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO welfare.welfare_region_cfgs
(id, task_region, task_region_key, day_num, customer_type, created_at, updated_at)
VALUES(2, 'common_region', '普通', 15, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO welfare.welfare_region_cfgs
(id, task_region, task_region_key, day_num, customer_type, created_at, updated_at)
VALUES(3, 'risk_region', '低净值', 15, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO welfare.welfare_region_cfgs
(id, task_region, task_region_key, day_num, customer_type, created_at, updated_at)
VALUES(4, 'sa_region', 'SA', 7, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

INSERT INTO welfare.welfare_region_cfgs
(id, task_region, task_region_key, day_num, customer_type, created_at, updated_at)
VALUES(5, 'compliance_region', '合规', 15, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);