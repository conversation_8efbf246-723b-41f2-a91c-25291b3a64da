CREATE TABLE `welfare_user_points_new`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT,
    `uid`              bigint unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
    `clean_point_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '积分上次清0时间戳 秒',
    `points`           int       NOT NULL DEFAULT '0',
    `created_at`       timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`       timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_uid` (`uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='福利用户积分';