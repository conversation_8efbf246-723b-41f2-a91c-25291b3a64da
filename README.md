# 福利中心重构

*  福利中心Golang重构服务

## 项目结构
[详细目录结构以及上下游调用链路参考文档](https://gtglobal.jp.larksuite.com/wiki/GrNswzpCLij574k1T8HjoHC4pag)

具体目录结构如下：
```
.
├── api                           # 接口定义文件
├── deploy                        # 部署文件
│   └── sql                       # 生成表model文件的建表sql文件
│       └── v20250324             # 最近上线执行的sql语句和创建的表
│           └── system.sql        # 需要更新的sql语句，或需要创建的表
├── etc                           # 配置文件
│   ├── gateio_service_welfare_go-api.yaml
├── internal                      # 项目内部文件
│   ├── config                    # 配置文件
│   │   └── config.go
│   ├── consts                    # 常量定义      
│   │   ├── err_codes.go          # 错误码
│   │   ├── language.go           # 语言相关
│   │   └── redis_keys.go         # redis Key
│   ├── dao                    # 数据层处理方法     
│   │   ├── welfare_config_dao.go # welfare_config表相关数据数据处理
│   ├── handler                   # handler定义
│   │   ├── inner                 # 内部接口控制器文件
│   │   ├── outer                 # 外部接口控制器文件
│   │   │   └── routes.go   # 外部接口路由文件 
│   │   │   └── routes_inner.go   # 内部接口路由文件
│   ├── logic                     # 业务逻辑文件logic
│   │   ├── inner                 # 内部接口逻辑文件
│   │   ├── outer                 # 外部接口逻辑文件
│   ├── middleware                # 接口中间件
│   ├── models                    # 数据库model文件
│   ├── mqs                       # 消息队列使用
│   │   ├── kafka                 # kafka
│   ├── service                   # 公共逻辑层
│   ├── service_client            # 调用其他服务逻辑处理层
│   ├── svc                       # 服务全局上下文
│   ├── types                     # 类型定义
│   └── utils                     # 工具函数包
├── script                        # 脚本  
│
├── Dockerfile                    # dockerfile
├── Makefile                      
├── README.md                     # 项目说明文档
├── gateio_service_welfare_go.api        # 接口定义入口文件
└── gateio_service_welfare_go.go         # main函数入口文件
 
```


## 开发环境准备
* go v1.23
* goctl v1.1.12 (务必从 [gatebackend/go-zero](https://bitbucket.org/gatebackend/go-zero/src/master/) 编译安装)

### 本地开发
#### 环境变量
```shell
export ENV_NAME=test
export NACOS_HOST=**********
export NACOS_PORT=8848
export NACOS_USERNAME=nacos_gate
export NACOS_PASSWORD=nacos_gate
export NACOS_NAMESPACE=gateio
export NACOS_DATA_ID=gateio-service-welfare-go
export NACOS_SERVICE_CALL_USERNAME=nacos_gate
export NACOS_SERVICE_CALL_PASSWORD=nacos_gate
export NACOS_SERVICE_CALL_DATA_ID=gateio-service-call
export NACOS_SERVICE_NAMESPACE=gateio
export NACOS_SERVICE_CALL_NAMESPACE=gateio
export NACOS_APP_SIGN_DATA_ID=gateio.common.sign
```

#### 或者新增.env文件
```shell
APP_ENV="test"
APP_NAME=gateio_service_welfare_go
IS_IN_K8S=true
ENV_NAME="test"
NACOS_HOST=**********
NACOS_PORT=8848
NACOS_USERNAME=nacos_gate
NACOS_PASSWORD=nacos_gate
NACOS_NAMESPACE=gateio
NACOS_DATA_ID=gateio-service-welfare-go
NACOS_SERVICE_CALL_USERNAME=nacos_gate
NACOS_SERVICE_CALL_PASSWORD=nacos_gate
NACOS_SERVICE_CALL_DATA_ID=gateio-service-call
NACOS_SERVICE_NAMESPACE=gateio
NACOS_SERVICE_CALL_NAMESPACE=gateio
NACOS_APP_SIGN_DATA_ID=gateio.common.sign

KAFKA_ENABLE=false
```

## 代码生成
将模板仓库 [skeleton_go](https://bitbucket.org/gateio/gateio_service_skeleton_go/src/master/) 放在与当前项目**同级目录**下

### api代码生成
```shell
goctl --style go_zero api go -api gateio_service_welfare_go.api -dir .
```

### model代码生成

```shell
goctl model mysql ddl  -src deploy/sql/v20250530/welfare_user_kyc_prize_detail.sql -home ./template  -dir ./internal/models/welfare_user_kyc_prize_detail -style go_zero
```

## 特别注意
``` 
redis使用的key必须必须加"welfare_go:"前缀 例如："welfare_go:newbieTask:userinfo:1234"，当出现大key时方便定位业务
生产禁止使用keys查询以及删除
```

## log使用

### 正常使用
```
logc.Errorf(ctx,"Update check-in status")
```
### logic层使用
```

// 签到
func NewCheckInLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckInLogic {
	return &CheckInLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}
func (l *CheckInLogic) CheckIn(req *types.CheckInReq, r *http.Request) (resp *types.UniversalNoParamReq, err error) {

    l.Logger.Errorf("Update check-in status")

}
```