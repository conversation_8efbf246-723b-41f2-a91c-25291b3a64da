FROM nexus-dev-image.fulltrust.link/base-images/golang:1.22-alpine AS build
WORKDIR /gate/src

COPY go.mod go.sum ./

RUN GOTOOLCHAIN=auto go mod download

COPY . .

RUN GOTOOLCHAIN=auto CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o main .

# 运行时镜像无需修改
FROM nexus-dev-image.fulltrust.link/base-images/alpine:latest
WORKDIR /app

COPY --from=build /gate/src/main /app
COPY --from=build /gate/src/etc /app/etc

# 外部服务
EXPOSE 3460
# 内部服务
EXPOSE 9580
# metric
EXPOSE 9502
CMD ["/app/main"]
